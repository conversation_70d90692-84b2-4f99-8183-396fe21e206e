{"compilerOptions": {"target": "ESNext", "noImplicitAny": false, "allowSyntheticDefaultImports": true, "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "paths": {"~/*": ["./src/*"], "@/*": ["./src/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "node_modules/element-plus/es/components/**/*.d.ts"], "exclude": ["node_modules", "dist", "components.d.ts"]}