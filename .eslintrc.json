{"root": true, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "@tanzerfe/eslint-config-lint"], "rules": {"@typescript-eslint/no-unused-vars": "off", "vue/multi-word-component-names": "off", "@typescript-eslint/no-this-alias": "off", "no-undef": "off", "no-control-regex": "off", "no-misleading-character-class": "off", "no-fallthrough": "off", "no-constant-condition": "off"}}