{"name": "education-management-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": " vite build --mode development", "build:aqsc": "vite build --mode aqsc", "build:production": "vite build --mode production", "build:wych_test": "vite build --mode wych_local_test", "build:wych_prod": "vite build --mode wych_local_prod", "preview": "vite preview", "tsc": "vue-tsc --noEmit", "lint": "eslint . --ext .js,.ts,.vue --ignore-path .eslint<PERSON>ore", "prepare": "husky install", "lint-staged": "lint-staged --no-stash", "commitlint": "commitlint --edit"}, "lint-staged": {"*.{vue,ts,tsx,js,jsx}": "eslint --ignore-path .eslint<PERSON>ore --fix"}, "husky": {"hooks": {"pre-commit": "lint-staged", "post-commit": "git update-index --again", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@kalimahapps/vue-icons": "^1.7.1", "@tanzerfe/tanzer-ui": "^0.0.6", "@types/node": "^20.14.9", "@vueuse/core": "^10.11.0", "@wangeditor/editor": "^5.1.23", "axios": "^1.7.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.7.6", "hel-iso": "^4.3.2", "hel-micro": "^4.9.10", "html-to-image": "^1.11.13", "js-base64": "^3.7.7", "js-file-downloader": "^1.1.25", "path": "^0.12.7", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "pubsub-js": "1.9.4", "qrcode-vue3": "^1.6.8", "sass": "^1.77.6", "uuid": "8.3.2", "video.js": "7.21.5", "vue": "^3.4.29", "vue-quill-editor": "^3.0.6", "vue-router": "^4.4.0"}, "devDependencies": {"@commitlint/cli": "17.8.1", "@commitlint/config-angular": "17.8.1", "@tanzerfe/eslint-config-lint": "^0.0.5", "@types/crypto-js": "^4.2.2", "@vitejs/plugin-vue": "^5.0.5", "eslint": "^8.36.0", "husky": "8.0.3", "lint-staged": "^15.2.7", "naive-ui": "^2.39.0", "postcss": "^8.4.49", "postcss-pxtorem": "^6.1.0", "typescript": "^5.2.2", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.2", "vite": "^5.3.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-windicss": "^1.9.3", "vue-tsc": "^2.0.21", "windicss": "^3.5.6"}}