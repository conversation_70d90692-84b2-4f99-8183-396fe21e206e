<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/changhang.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>中国外运长航资产安全管理平台</title>
    <style>
      #app {
        width: 100%;
        height: 100%;
      }
      /* 基于宽度的字体大小计算 */
      @media screen and (max-aspect-ratio: 1920/700) {
        html {
          font-size: calc(16 * (100vw / 1920));
        }
      }

      /* 基于高度的字体大小计算 */
      @media screen and (min-aspect-ratio: 1920/700) {
        html {
          font-size: calc(16 * (100vh / 1080));
        }
      }
    </style>
    <script>
      var screen = window.screen
      var docEl = document.documentElement
      docEl.style.width = screen.width + 'px'
    </script>
    <script src="/js/sockjs.min.js"></script>
    <script src="/js/stomp.min.js"></script>
    <!-- <script
      type="text/javascript"
      src="https://www.tanzercloud.com/img1/gis/js/Cesium/V1.116/Cesium/Cesium.js"
    ></script>
    <link
      href="https://www.tanzercloud.com/img1/gis/js/Cesium/V1.116/Cesium/Widgets/widgets.css"
      rel="stylesheet"
    /> -->
    <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/qweather-icons@1.3.0/font/qweather-icons.css" /> -->
    <!-- <script type="text/javascript"
    src="https://www.tanzercloud.com/api/v2/gissetter-service/MapEnginePackage/mapEnginePackage?mepName=IndoorGlobe.AG_LMSDisplay&wgId=64"></script> -->

    <link
      rel="stylesheet"
      type="text/css"
      href="https://www.tanzercloud.com/api/v2/gissetter-service/MapEnginePackage/mapEnginePackageCSS?mepName=IndoorThree.LMSDisplay_PMap_CSS&wgId=68"
    />
  </head>

  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
  <script
    type="text/javascript"
    src="https://www.tanzercloud.com/api/v2/gissetter-service/MapEnginePackage/mapEnginePackageJS?mepName=IndoorThree.LMSDisplay_PMap_GeoData&wgId=68"
  ></script>
  <script
    type="text/javascript"
    src="https://www.tanzercloud.com/api/v2/gissetter-service/MapEnginePackage/mapEnginePackageJS?mepName=IndoorThree.LMSDisplay_PMap&wgId=68"
  ></script>
  <!-- <script
    type="text/javascript"
    src="/js/ajaxhook.js"
  ></script> -->
  <!--  -->
</html>
