const cancelSignal = {}
const getSignal = (key?: string) => {
  if (key) return cancelSignal[key]
}

const addSignal = (key: string) => {
  // 当前请求存在 取消请求
  // if (cancelSignal[key]) cncelSignal(key)

  const controller = new AbortController()
  cancelSignal[key] = controller
}
const delSignal = (key: string) => {
  if (cancelSignal[key]) delete cancelSignal[key]
}
const cncelSignal = (key: string) => {
  cancelSignal[key].abort()
  delete cancelSignal[key]
}
const cnceAllSignal = () => {
  Object.keys(cancelSignal).forEach((i: any) => {
    cancelSignal[i].abort()
    delete cancelSignal[i]
  })
}
export { getSignal, addSignal, delSignal, cncelSignal, cnceAllSignal }
