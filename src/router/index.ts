/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-04 02:26:50
 * @LastEditors: jingjf <EMAIL>
 * @LastEditTime: 2024-07-13 16:26:20
 * @FilePath: /angang-platform/src/router/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createRouter, createWebHashHistory } from 'vue-router'
import defaultLayout from '@/layout/default.vue'

const routes = [
  {
    path: '/',
    redirect: import.meta.env.VITE_WYCH_LOCAL === 'true' ? '/nuc-login' : '/login',
  },
  ...[
    import.meta.env.VITE_WYCH_LOCAL === 'true'
      ? {
          path: '/nuc-login',
          component: () => import('@/view/login/nuc-login.vue'),
        }
      : {
          path: '/login',
          component: () => import('@/view/login/index.vue'),
        },
  ],
  {
    path: '/stagingMain',
    name: 'stagingMain',
    component: defaultLayout,
    children: [
      {
        path: '/staging',
        name: 'staging',
        meta: {
          pageTitle: '首页',
        },
        component: () => import('@/view/staging/index.vue'),
      },
    ],
  },
  {
    path: '/notifyTaskMain',
    name: 'notifyTaskMain',
    component: defaultLayout,
    children: [
      {
        path: '/notifyTask',
        name: 'notifyTask',
        meta: {
          pageTitle: '通知任务',
        },
        component: () => import('@/view/notifyTask/index.vue'),
      },
      {
        path: '/issueReport',
        name: 'issueReport',
        meta: {
          pageTitle: '发布公告',
        },
        component: () => import('@/view/notifyTask/comp/IssueReport.vue'),
      },
      {
        path: '/noticeDetail',
        name: 'noticeDetail',
        meta: {
          pageTitle: '公告详情',
        },
        component: () => import('@/view/notifyTask/comp/NoticeDetail.vue'),
      },
    ],
  },
  {
    path: '/digitalIntelligenceBrainMain',
    name: 'digitalIntelligenceBrainMain',
    component: defaultLayout,
    children: [
      {
        path: '/digitalIntelligenceBrain',
        name: 'digitalIntelligenceBrain',
        meta: {
          pageTitle: '数智大脑',
        },
        component: () => import('@/view/digitalIntelligenceBrain/index.vue'),
        // component: () => import('@/view/shuJuJiaShiCang/index.vue'),
      },
    ],
  },
  {
    path: '/orgManagementMain',
    name: 'orgManagementMain',
    component: defaultLayout,
    children: [
      {
        path: '/orgManagement',
        name: 'orgManagement',
        meta: {
          pageTitle: '组织管理',
        },
        // component: () => import('@/view/orgManagement/index.vue'),
        component: () => import('@/view/orgManagement/organizational.vue'),
      },
      {
        path: '/role',
        name: 'role',
        meta: {
          pageTitle: '角色管理',
        },
        component: () => import('@/view/orgManagement/role.vue'),
      },
      {
        path: '/post',
        name: 'post',
        meta: {
          pageTitle: '岗位管理',
        },
        component: () => import('@/view/orgManagement/post.vue'),
      },
      {
        path: '/member',
        name: 'member',
        meta: {
          pageTitle: '成员管理',
        },
        component: () => import('@/view/orgManagement/member.vue'),
      },
      {
        path: '/organizational',
        name: 'organizational',
        meta: {
          pageTitle: '组织管理',
        },
        component: () => import('@/view/orgManagement/organizational.vue'),
      },
    ],
  },
  {
    path: '/locationAreaMain',
    name: 'locationAreaMain',
    component: defaultLayout,
    children: [
      {
        path: '/regionalMgr',
        name: 'regionalMgr',
        meta: {
          pageTitle: '区域管理',
        },
        component: () => import('@/view/locationAreaManage/area.vue'),
      },
      {
        path: '/locationMgr',
        name: 'locationMgr',
        meta: {
          pageTitle: '区域管理',
        },
        component: () => import('@/view/locationAreaManage/location.vue'),
      },
    ],
  },
  {
    path: '/brainBigScreen',
    name: 'brainBigScreen',
    children: [
      {
        path: '/xgfScreen',
        name: 'xgfScreen',
        meta: {
          pageTitle: '履职-相关方员工',
        },
        component: () => import('@/view/xgfBigScreen/index.vue'),
      },
      {
        path: '/brainScreen',
        name: 'brainScreen',
        meta: {
          pageTitle: '数智大脑大屏',
        },
        component: () => import('@/view/brainBigScreen/homeOverview.vue'),
      },
      {
        path: '/shujuJiaShiCang',
        name: 'shujuJiaShiCang',
        meta: {
          pageTitle: '数智大脑大屏',
        },
        component: () => import('@/view/shuJuJiaShiCang/index.vue'),
      },
      {
        path: '/subgis',
        name: 'subgis',
        meta: {
          pageTitle: '数智大脑大屏',
        },
        component: () => import('@/view/shuJuJiaShiCang/subGis/index.vue'),
      },
    ],
  },
  {
    path: '/fieldOverview',
    name: 'fieldOverview',
    children: [
      {
        path: '/fieldOver',
        name: 'fieldOver',
        meta: {
          pageTitle: '矿场总览',
        },
        component: () => import('@/view/brainBigScreen/fieldOverview.vue'),
      },
    ],
  },
  {
    path: '/educationalTraining',
    name: 'educationalTraining',
    children: [
      {
        path: '/educationalTrain',
        name: 'educationalTrain',
        meta: {
          pageTitle: '教育培训',
        },
        component: () => import('@/view/brainBigScreen/educationalTraining.vue'),
      },
    ],
  },
  {
    path: '/monitorWarnMain',
    name: 'monitorWarnMain',
    children: [
      {
        path: '/monitorWarn',
        name: 'monitorWarn',
        meta: {
          pageTitle: '智慧监管安全大脑',
        },
        component: () => import('@/view/monitorWarn/index.vue'),
      },
    ],
  },
  {
    path: '/loginBehaviorAnalysis1',
    name: 'loginBehaviorAnalysis1',
    component: defaultLayout,
    children: [
      {
        path: '/loginBehaviorAnalysis',
        name: 'loginBehaviorAnalysis',
        meta: {
          pageTitle: '智慧监管安全大脑',
        },
        component: () => import('@/view/loginBehaviorAnalysis/index.vue'),
      },
    ],
  },
]
const router = createRouter({
  routes,
  history: createWebHashHistory(),
})
export default router
