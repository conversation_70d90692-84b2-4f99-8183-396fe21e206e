import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'

const piniaInstance = createPinia()

piniaInstance.use(
  createPersistedState({
    storage: window.localStorage,
    beforeRestore: () => {},
    afterRestore: () => {},
    serializer: {
      serialize: JSON.stringify,
      deserialize: JSON.parse,
    },
  })
)

export default piniaInstance
export { default as useUserInfo } from './useUserInfo'
export { default as useCounterStore } from './useCounterStore'
export { default as unitMapStore } from './unitMap'
