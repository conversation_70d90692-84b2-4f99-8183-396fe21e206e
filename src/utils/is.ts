// 定义一个toString函数，用于获取对象的类型字符串
const toString = Object.prototype.toString

// 判断一个值的类型是否为指定的type
export function is(val: unknown, type: string) {
  return toString.call(val) === `[object ${type}]`
}

// 判断一个值是否不是undefined
export function isDef<T = unknown>(val?: T): val is T {
  return typeof val !== 'undefined'
}

// 判断一个值是否是undefined
export function isUnDef<T = unknown>(val?: T): val is T {
  return !isDef(val)
}

// 判断一个值是否是对象类型
export function isObject(val: any): val is Record<any, any> {
  return val !== null && is(val, 'Object')
}

// 判断一个值是否为空（支持数组、字符串、Map、Set、对象）
export function isEmpty<T = unknown>(val: T): val is T {
  if (isArray(val) || isString(val)) {
    return val.length === 0
  }

  if (val instanceof Map || val instanceof Set) {
    return val.size === 0
  }

  if (isObject(val)) {
    return Object.keys(val).length === 0
  }

  return false
}

// 判断一个值是否是Date类型
export function isDate(val: unknown): val is Date {
  return is(val, 'Date')
}

// 判断一个值是否是null
export function isNull(val: unknown): val is null {
  return val === null
}

// 判断一个值是否是null或undefined
export function isNullAndUnDef(val: unknown): val is null | undefined {
  return isUnDef(val) && isNull(val)
}

// 判断一个值是否是null或undefined
export function isNullOrUnDef(val: unknown): val is null | undefined {
  return isUnDef(val) || isNull(val)
}

// 判断一个值是否是Number类型
export function isNumber(val: unknown): val is number {
  return is(val, 'Number')
}

// 判断一个值是否是Promise类型
export function isPromise<T = any>(val: unknown): val is Promise<T> {
  return is(val, 'Promise') && isObject(val) && isFunction(val.then) && isFunction(val.catch)
}

// 判断一个值是否是String类型
export function isString(val: unknown): val is string {
  return is(val, 'String')
}

// 判断一个值是否是函数
export function isFunction(val: unknown): val is Fn {
  return typeof val === 'function'
}

// 判断一个值是否是Boolean类型
export function isBoolean(val: unknown): val is boolean {
  return is(val, 'Boolean')
}

// 判断一个值是否是RegExp类型
export function isRegExp(val: unknown): val is RegExp {
  return is(val, 'RegExp')
}

// 判断一个值是否是数组
export function isArray(val: any): val is Array<any> {
  return val && Array.isArray(val)
}

// 判断一个值是否是Window对象
export function isWindow(val: any): val is Window {
  return typeof window !== 'undefined' && is(val, 'Window')
}

// 判断一个值是否是DOM元素
export function isElement(val: unknown): val is Element {
  return isObject(val) && !!val.tagName
}

// 判断一个值是否是Map类型
export function isMap(val: unknown): val is Map<any, any> {
  return is(val, 'Map')
}

// 判断当前环境是否是服务器环境
export const isServer = typeof window === 'undefined'

// 判断当前环境是否是客户端环境
export const isClient = !isServer

// 判断一个字符串是否是URL
export function isUrl(path: string): boolean {
  const reg =
    /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/
  return reg.test(path)
}
