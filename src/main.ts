/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-04 02:09:04
 * @LastEditors: jingjf <EMAIL>
 * @LastEditTime: 2024-07-15 10:59:45
 * @FilePath: /angang-platform/src/main.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createApp } from 'vue'
import './style.scss'
import '@/assets/font/family.scss'
import '@/assets/css/base.scss'
import App from './App.vue'
import router from './router'
import piniaInstance, { useUserInfo } from './store'
import 'virtual:windi.css'
import registerComponent from './common/registerComponent'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import elementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import '@wangeditor/editor/dist/css/style.css'
import 'virtual:svg-icons-register'
import ChartTitle from '@/view/shuJuJiaShiCang/components/chartTitle.vue'
import { setFavicon } from './hooks/updIcon'

router.beforeEach((to: any, from: any, next: any) => {
  const isWYCH_LOCAL = import.meta.env.VITE_WYCH_LOCAL === 'true'
  const ui = useUserInfo()
  setFavicon(ui)
  console.log(from)
  //判断该用户有没有登录过
  if (to.path === '/login' || to.path === '/xgfScreen' || to.path === '/nuc-login') {
    next()
  } else {
    if (ui.value.id) {
      next()
    } else {
      next({ path: isWYCH_LOCAL ? '/nuc-login' : '/login' })
    }
  }
})

const app = createApp(App)
registerComponent(app)
app.use(router)
app.use(piniaInstance)
app.use(elementPlus, { size: '', locale: zhCn })
app.component('ChartTitle', ChartTitle)
app.mount('#app')
