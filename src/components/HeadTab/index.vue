<template>
  <div class="head_box h-55px">
    <div class="flex">
      <div
        v-for="item in changeTabs"
        :class="['head_tab', { w_active: current === item.value }]"
        @click="changeTab(item)"
        :key="item.value"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps({
  changeTabs: {
    type: Array as any,
    default: () => [
      { label: '全部', value: '1' },
      { label: '第一', value: '2' },
    ],
  },
  type: {
    type: String,
    default: '0',
  },
})

const emit = defineEmits(['changeTab'])
const current = ref('1')

function changeTab(item: any) {
  current.value = item.value
  emit('changeTab', item)
}

watch(
  () => window.history.state.tab,
  (val = '1') => {
    current.value = val
  },
  { immediate: true }
)

watch(
  () => props.type,
  (val = '0') => {
    if (val === '1') {
      current.value = '0'
    }
  },
  { immediate: true }
)

defineOptions({ name: 'HeadTab' })
</script>

<style scoped lang="scss">
.head_box {
  font-weight: 400;
  font-size: 16px;
  color: #222222;
  border-radius: 4px 4px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: center;
  cursor: pointer;

  .head_tab {
    width: 120px;
    height: 40px;
    line-height: 40px;
    background: url('./tab_untive.png') no-repeat;
    background-size: 100% 100%;
    margin-left: -10px;

    &:first-child {
      text-align: start;
      padding-left: 20px;
      background: url('./first_active.png') no-repeat;
      background-size: 100% 100%;
      margin-left: 0;
    }

    &:hover {
      // color: rgba(64, 112, 255, 1);
    }
  }

  .w_active {
    width: 120px;
    height: 40px;
    line-height: 40px;
    background: url('./active.png') no-repeat;
    background-size: 100% 100%;
    color: #ffffff;
    z-index: 99;

    // border-bottom: 3px solid #4070ff;
    &:first-child {
      background: url('./tab_active.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
