<!-- customTimeline -->
<template>
  <div class="custom-timeline">
    <div v-if="activities.length > 0">
      <el-timeline>
        <el-timeline-item
          v-for="(activity, index) in activities as any"
          :key="index"
          :type="activity.type"
          :color="'#0bbd87'"
          :hollow="activity.hollow"
          :hide-timestamp="activity.hideTimestamp"
          :center="activity.center"
          :icon="activity.icon"
          :size="activity.size"
          :placement="activity.placement"
        >
          <div>
            日期：
            <span class="time">{{ activity.timestamp }}</span>
          </div>
          <div class="mt-14px">
            触发情况：
            <!-- <span class="describe">{{ activity.touchState }} </span> -->
            <span :style="{ color: borderColor(activity.touchState) }" v-if="activity.touchState == '2'">
              {{ '已拍照' }}
            </span>
            <span :style="{ color: borderColor(activity.touchState) }" v-if="activity.touchState == '0'">
              {{ '未触发' }}
            </span>
            <span :style="{ color: borderColor(activity.touchState) }" v-if="activity.touchState == '1'">
              {{ '已触发' }}
            </span>
          </div>
          <div class="mt-14px">
            巡查任务：
            <span class="describe">{{ activity.planName }} </span>
          </div>
          <template v-if="dot" #dot>
            <span class="circle" :style="{ borderColor: borderColor(activity.touchState) }"></span>
          </template>
        </el-timeline-item>
      </el-timeline>
      <div v-if="isShow" class="text-center cursor-pointer" @click="getMoreData" track>加载更多</div>
    </div>
    <no-data v-else></no-data>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElTimeline, ElTimelineItem } from 'element-plus'
import noData from './noData.vue'
// eventType的值例如火警/预警,添加圆圈样式
const props = defineProps({
  activities: {
    type: Array,
    default: () => {},
  },
  dot: {
    type: Boolean,
    default: true,
  },
  total: {
    type: Number,
    default: 0,
  },
})
const borderColor = computed(() => {
  return (type) => {
    let color = ''
    if (type == 0) {
      color = '#F30F0F'
    } else if (type == 1) {
      color = '#0080FF'
    } else if (type == 2) {
      color = '#0080FF'
    }
    return color
  }
})

const isShow = computed(() => {
  console.log(props.total, props.activities.length)
  return props.activities.length < props.total
})
const emits = defineEmits(['getMoerData'])
const getMoreData = () => {
  console.log('加载更多')
  emits('getMoerData')
}
</script>

<style lang="scss" scoped>
.custom-timeline {
  padding: 20px 5px;

  .describe,
  .time {
    color: #666;
  }

  .circle {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #ccc;
    background-color: #fff;
    margin-left: -20%;
    margin-top: -10%;
  }
}
</style>
