<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-09 15:31:14
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-11 08:56:39
 * @FilePath: /angang-platform/src/components/public/noData.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script lang="ts">
import { h, defineComponent } from 'vue'
import imgSrc from '@/assets/image/no-data-new.png'

export default defineComponent({
  name: 'noData',
  props: {
    labelTitle: {
      type: String,
      default: '暂无数据',
    },
    color: {
      type: String,
      default: '#909399',
    },
    imgSrc: {
      type: String,
      default: imgSrc,
    },
  },
  render() {
    return h(
      'div',
      {
        class: 'no-data-page text-center',
      },
      [
        h('img', {
          src: this.imgSrc,
          class: 'inline-block w-140px',
        }),
        h(
          'p',
          {
            style: `color: ${this.color}`,
            class: 'w-250px',
          },
          this.labelTitle
        ),
      ]
    )
  },
})
</script>

<style>
.no-data-page {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
