<!-- 按钮组组件 -->
<template>
  <div
    :class="[
      plain && 'button-group-plain',
      type === 'text' && 'button-group-text',
      type === 'primary' && 'button-group',
    ]"
  >
    <div
      v-for="btnItem in btnOptions"
      :key="btnItem.value"
      class="btn"
      track
      :class="{ activeTab: activeTab === btnItem.value }"
      @click="tabChange(btnItem.value)"
    >
      {{ btnItem.label }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
type item = {
  value: string
  label: string
}

const btnOptions = ref<item[]>([
  {
    value: '1',
    label: '近一周',
  },
  {
    value: '2',
    label: '近一月',
  },
  {
    value: '3',
    label: '近一年',
  },
])

const props = defineProps({
  activeTab: {
    type: String,
    default: '',
  },
  options: {
    type: Array,
    default: () => [],
  },
  plain: {
    type: Boolean,
    default: false, // 默认为安全\设备一张图的按钮样式,当plain为true的时候为普通样式
  },
  type: {
    type: String,
    default: 'primary',
  },
})

const emits = defineEmits(['update:activeTab', 'setTab'])

const tabChange = (type) => {
  emits('update:activeTab', type)
  emits('setTab', type)
}

onMounted(() => {
  if (props.options.length > 0) {
    btnOptions.value = props.options as item[]
  }
})
</script>

<style lang="scss" scoped>
.button-group {
  display: flex;
  align-items: flex-end;
  cursor: pointer;
  user-select: none;
  font-weight: 400;

  & > div {
    background-color: red;
    line-height: normal;
    line-height: 28px;
    background: rgba(4, 37, 80, 1);
    border: 1px solid #3f9bcf;
    border-left: none;
    padding: 0 8px;
    box-shadow: inset 0px 0px 18px 0px rgba(63, 155, 207, 1);
    font-size: 14px;
    color: rgba(116, 201, 255, 1);
  }

  & > div:nth-of-type(1) {
    border-left: 1px solid #3f9bcf;
  }

  .activeTab {
    background-image: url(@/assets/image/unitMap/tabs-bg.png);
    background-size: 100% 100%;
    color: #fff;
  }
}

.button-group-plain {
  display: flex;
  align-items: flex-end;
  cursor: pointer;
  user-select: none;
  font-weight: 400;

  & > div {
    line-height: normal;
    line-height: 28px;
    border: 1px solid #dcdfe6;
    padding: 0 8px;
    font-size: 14px;
    color: #666666;
    background-color: unset;
    box-shadow: unset;
  }

  & > div:nth-of-type(1) {
    border-left: 1px solid #dcdfe6;
    border-radius: 4px 0px 0px 4px;
  }

  & > div:last-child {
    border-radius: 0 4px 4px 0px;
  }

  .activeTab {
    background-image: unset;
    color: #0080ff;
    border-color: #0080ff !important;
  }
}

.button-group-text {
  display: flex;
  align-items: flex-end;
  cursor: pointer;
  user-select: none;
  font-weight: 400;

  & > div {
    line-height: normal;
    line-height: 22px;
    padding: 0 8px;
    font-size: 14px;
    color: #666666;
    background-color: unset;
    box-shadow: unset;
  }

  & > div:nth-of-type(1) {
    border-radius: 4px 0px 0px 4px;
  }

  & > div:nth-of-type(2) {
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
  }

  & > div:last-child {
    border-radius: 0 4px 4px 0px;
  }

  .activeTab {
    background-image: unset;
    color: #0080ff;
  }
}
</style>
