<!-- customTimeline -->
<template>
  <div class="custom-timeline">
    <div v-if="activities.length > 0">
      <el-timeline>
        <el-timeline-item
          v-for="(activity, index) in activities as any"
          :key="index"
          :type="activity.type"
          :color="'#0bbd87'"
          :hollow="activity.hollow"
          :hide-timestamp="activity.hideTimestamp"
          :center="activity.center"
          :icon="activity.icon"
          :size="activity.size"
          :placement="activity.placement"
        >
          <div>
            {{ borderColor(activity.eventType, 2) || '--' }}时间：
            <span class="time">{{ activity.timestamp }}</span>
          </div>
          <div class="mt-14px">
            异常描述：
            <span class="describe">{{ activity.content }} </span>
          </div>

          <template v-if="dot" #dot>
            <span class="circle" :style="{ borderColor: borderColor(activity.eventType, 1) }"></span>
          </template>
        </el-timeline-item>
      </el-timeline>
      <div v-if="isShow" class="text-center cursor-pointer pb-15px" @click="getMoreData" track>加载更多</div>
    </div>
    <no-data v-else></no-data>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
// import Timeline from  'element-plus'
import { ElTimeline } from 'element-plus'
import { ElTimelineItem } from 'element-plus'
import noData from './noData.vue'
// eventType的值例如火警/预警,添加圆圈样式
const props = defineProps({
  activities: {
    type: Array,
    default: () => {},
  },
  dot: {
    type: Boolean,
    default: true,
  },
  total: {
    type: Number,
    default: 0,
  },
})
const borderColor = computed(() => {
  return (eventType, type) => {
    if (type == 1) {
      let color = ''
      if (eventType == '火警' || eventType == 1) {
        color = '#F30C0C'
      } else if (eventType == '预警' || eventType == 2) {
        color = '#9F1D8B'
      } else if (eventType == '故障' || eventType == 3) {
        color = '#FD9905'
      } else if (eventType == '隐患' || eventType == 4) {
        color = '#F9E400'
      } else if (eventType == '动作' || eventType == 5) {
        color = '#2476EE'
      } else {
        color = '#999999'
      }
      return color
    } else if (type == 2) {
      let eventTypeName = ''
      if (eventType == 1) {
        eventTypeName = '火警'
      } else if (eventType == 2) {
        eventTypeName = '预警'
      } else if (eventType == 3) {
        eventTypeName = '故障'
      } else if (eventType == 4) {
        eventTypeName = '隐患'
      } else if (eventType == 5) {
        eventTypeName = '动作'
      } else {
        eventTypeName = '离线'
      }
      return eventTypeName
    }
  }
})

const isShow = computed(() => {
  console.log(props.total, props.activities.length)
  return props.activities.length < props.total
})
const emits = defineEmits(['getMoerData'])
const getMoreData = () => {
  console.log('加载更多')
  emits('getMoerData')
}
</script>

<style lang="scss" scoped>
.custom-timeline {
  padding: 20px 5px;

  .describe,
  .time {
    color: #666;
  }

  .circle {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #ccc;
    background-color: #fff;
    margin-left: -20%;
    margin-top: -10%;
  }
}
</style>
