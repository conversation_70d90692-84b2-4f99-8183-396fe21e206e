<template>
  <div class="video-component" v-if="!isIframe">
    <video controls :id="id" class="video-tag video-js vjs-default-skin">
      <source :src="videoUrl" />
    </video>
  </div>
  <div class="video-component" v-else>
    <iframe class="myiframe" :src="videoUrl" frameborder="0"></iframe>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, nextTick, computed } from 'vue'
import videojs from 'video.js'
import { v4 as uuidV4 } from 'uuid'

const props = defineProps({
  videoUrl: {
    type: [Object, String],
    default: '',
  },
})
let videoUrl = computed(() => {
  if (props.videoUrl && typeof props.videoUrl == 'string') {
    return props.videoUrl
  } else if (props.videoUrl._videoUrl) {
    return props.videoUrl?._videoUrl
  } else {
    return ''
  }
})
let isIframe = computed(() => {
  if (props.videoUrl && typeof props.videoUrl == 'string') {
    return false
  } else {
    return props.videoUrl.isIframe
  }
})

let videoInstance: any = null

const id = 'video_' + uuidV4()

onMounted(() => {
  if (isIframe.value) {
    return
  } else {
    nextTick(() => {
      videoInstance = videojs(id, {
        // children: null,
        autoplay: true,
      })
      videojs.hook('error', function (player, err) {
        document.querySelector('.vjs-modal-dialog-content').innerHTML =
          `<div style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;color: #fff;">
            <div>
              <div>
                暂时无法播放视频
              </div>
              <div>
                请查看以下问题解决：1.设备网络问题；2.设备电源问题；3.设备硬件故障
              </div>
            </div>
          </div>`
      })

      videoInstance.children().forEach(function (child) {
        console.log('🚀 ~ child:', child)
        // if (child.buildCSSClass() === 'vjs-loading-spinner' || child.buildCSSClass() === 'vjs-error-display') {
        // child.hide()
        // }
      })
    })
  }
})

onUnmounted(() => {
  videoInstance && videoInstance.dispose()
})
</script>

<script lang="ts">
export default {
  name: 'videoComponent',
}
</script>

<style lang="scss">
.video-component {
  height: 100%;
  width: 100%;

  .myiframe,
  .video-tag,
  video {
    width: 100%;
    height: 100%;
    object-fit: fill;
  }

  .vjs-control-bar {
    display: none;
  }
  // .vjs-modal-dialog-content {
  //   display: none;
  // }
  .vjs-modal-dialog {
    display: none;
  }
  .vjs-loading-spinner {
    display: none;
  }
  .vjs-big-play-button {
    display: none;
  }
}

.vjs-error .vjs-error-display::before {
  display: none !important;
  content: '';
}
</style>
