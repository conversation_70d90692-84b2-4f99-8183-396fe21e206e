<template>
  <div class="warning-echarts aaaaa">
    <!-- <div class="tip" v-show="onlineState === '1'">
      当前处于离线状态，以下展示的是离线前的记录
    </div> -->
    <div class="flex justify-between mb-16px">
      <div class="btn-group flex">
        <div
          class="item"
          :class="{ groupActive: activeSpanTab == n.groupId }"
          track
          v-for="(n, index) in btnSpanOptions"
          :key="index"
          @click="setSpanDataTab(n)"
        >
          {{ n.groupName }}
        </div>
      </div>
      <buttonGroup :plain="true" :options="btnOptions" v-model:activeTab="activeTab" @setTab="setTab"></buttonGroup>
    </div>
    <div class="echart-box">
      <div v-show="!showNoData" id="echarts-wrap" style="width: 100%" ref="elLineCharts"></div>

      <no-data v-show="showNoData"></no-data>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, nextTick, onBeforeUnmount } from 'vue'
import $API from '~/common/api'
import * as echarts from 'echarts'
import buttonGroup from '@/components/public/buttonGroup.vue'
import noData from '@/components/public/noData.vue'

const props = defineProps({
  deviceId: {
    type: String,
    default: '',
  },
})

const activeTab = ref('1')
const elLineCharts = ref(null)
const activeSpanTab = ref('1')
const showNoData = ref(false)
const btnSpanOptions: any = ref([
  // {
  //   groupId: '1',
  //   groupName: '剩余电流'
  // },
  // {
  //   value: '2',
  //   label: '线缆温度'
  // },
  // {
  //   value: '3',
  //   label: '相电流'
  // },
  // {
  //   value: '4',
  //   label: '相电压'
  // }
])
const btnOptions = ref([
  {
    value: '1',
    label: '近24小时',
  },
  {
    value: '7',
    label: '近一周',
  },
])

const setSpanDataTab = (val) => {
  showNoData.value = false
  if (!val) return
  activeSpanTab.value = val.groupId
  getdata(val)
  // queryDeviceCurve()
  // emits('activeChange', type)
}

const getdata = (item) => {
  let { flagName, flagUnit, curve } = dataInfo.value
  let itemData = curve[item.groupId]
  if (!itemData) return (showNoData.value = true)
  let arr: any = []

  if (item && item.children) {
    item.children.forEach((i) => {
      arr.push({
        name: flagName[i],
        unit: flagUnit[i],
        data: itemData[i],
      })
    })
    let options = getOptions(arr, itemData.dateTime)
    nextTick(() => {
      const chart = echarts.init(elLineCharts.value as any)
      chart.setOption(options, true)
    })
  } else {
    showNoData.value = true
  }
}

const setTab = (type) => {
  activeTab.value = type
  queryDeviceCurve()
  // emits('activeChange', type)
}

const groups = ref([] as any[])

const charts: any = []

const dataInfo: any = ref({})

function queryDeviceCurve() {
  $API
    .post({
      url: '/device/monitor/queryDeviceCurve',
      params: {
        // deviceId: 'AHHF_QHHFY_20180408_202009121114191729',
        deviceId: props.deviceId,
        duration: activeTab.value,
        modelType: 'unit_base_url',
      },
    })
    .then(async (res: any) => {
      if (res && res.code != 'success') return
      groups.value = res.data.group
      btnSpanOptions.value = res.data.group
      dataInfo.value = res.data
      if (groups.value && groups.value.length) {
        nextTick(() => {
          setSpanDataTab(groups.value[0])
        })
      } else {
        showNoData.value = true
      }
      // if (res && res.code === 'success') {
      //   btnSpanOptions.value = res.data.group
      //   const d = res.data
      //   let { flagName: fn, flagUnit: fu, curve = {}, group = [] } = d
      //   activeSpanTab.value  = group[0].groupId

      //   if (activeTab.value == 1) {
      //     // item[1] = '22'
      //   } else {
      //     curve = res.data
      //     // item[1] = '88'
      //     // item[3] = '66'
      //   }

      //   group.forEach((item: any) => {
      //     const list: any[] = (item.list = [])
      //     // let parent = curve[item.groupId] || {}
      //     let parent = {}

      //     if (activeTab.value == 1) {
      //       // item[1] = '22'
      //       parent = curve[item.groupId] || {}
      //     } else {
      //       curve = res.data
      //       // item[1] = '88'
      //       // item[3] = '66'
      //       parent = curve
      //     }

      //     item._dateTimes = parent.dateTime || parent.dataTimes || []

      //     item.children.forEach((flag) => {
      //       list.push({
      //         data: parent[flag] || [],
      //         unit: fu[flag],
      //         name: fn[flag]
      //       })
      //     })
      //   })

      //   if (group.length === 0) {
      //     group = [
      //       {
      //         groupName: '',
      //         list: [
      //           {
      //             data: [],
      //             name: '',
      //             unit: ''
      //           }
      //         ],
      //         _dateTimes: []
      //       }
      //     ]
      //   }
      //   // dataTimes = dt

      //   groups.value = group
      //   nextTick(() => {
      //     const els = document.querySelectorAll(
      //       '.warning-echarts .echarts-wrap'
      //     ) as NodeListOf<HTMLElement>
      //     group.forEach((item, index) => {
      //       let times = item._dateTimes
      //       // if (!times || times.length === 0) return

      //       const chart = echarts.init(els[index])
      //       chart.setOption(getOptions(item.list,times))
      //       charts.push(chart)
      //     })
      //   })
      // }
    })
}

function getOptions(list: any[], dataTimes = []) {
  const names: string[] = []
  const series: any[] = []
  let unit = ''

  list.forEach((item: any) => {
    names.push(item.name)
    series.push({
      symbol: 'none',
      name: item.name,
      type: 'line',
      smooth: true,
      itemStyle: {
        normal: {
          lineStyle: {
            width: 2, // 0.1的线条是非常细的了
            shadowColor: addShadowColor(item.name),
            shadowBlur: 4,
            shadowOffsetY: 5,
          },
        },
      },
      labelLine: {
        smooth: true,
      },
      data: item.data.map((item) => (item !== '' ? Number(item) : 0)),
    })
    unit = item.unit
  })

  const options = {
    color: ['#5AD8A6', '#4EDAF8', '#7A56FF', '#9a60b4'],
    tooltip: {
      trigger: 'axis',
      confine: true,
      formatter: function (params) {
        var name = ''
        var html = ''
        params.forEach(function (item) {
          var marker = item.marker
          name = '<div>' + item.name + '</div>'
          html += `<div class="flex justify-between items-center">
                        <div>
                            ${marker} <span>${item.seriesName}<\/span>
                        </div>
                        <span style="margin-left: 10px;">${item.data ? item.data + unit : '--'}</span>
                    <\/div>`
        })
        html += '</div>'
        return '<div>' + name + html
      },
    },
    title: {
      text: unit ? '单位(' + unit + ')' : '',
      top: '8%',
      textStyle: {
        fontSize: 14,
        color: '#6D6F73',
        fontWeight: 'normal',
      },
    },
    legend: {
      data: list.map(function (item) {
        return item.name
      }),
      right: 0,
      top: '8%',
      itemWidth: 6,
      itemHeight: 6,
      // icon: 'path://M96 512a32 32 0 0 1 32-32h768a32 32 0 0 1 0 64H128a32 32 0 0 1-32-32z'
      // icon: 'path://M62 462h900v100H62z'
      icon: 'circle',
    },
    grid: {
      left: '20',
      // right: '10%',
      bottom: '10',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      // boundaryGap: false,
      // axisLine: {
      //   // show: false
      // },
      // axisTick: {
      //   show: false
      // },
      data: dataTimes,
    },
    yAxis: {
      type: 'value',
      // max: 230,
      // min: 0,
      axisTick: {
        show: false,
      },
      axisLine: {
        // show: false
        lineStyle: {
          width: 5,
        },
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0,0,0,0.1)',
        },
      },
    },
    series: series,
  }

  return options
}
function addShadowColor(item) {
  if (item.substring(0, 1) == 'A') {
    return '#eaf4ed'
  }
  if (item.substring(0, 1) == 'B') {
    return '#eaf4ed'
  }
  if (item.substring(0, 1) == 'C') {
    return '#faf2f2'
  } else {
    return '#eaf4ed'
  }
}
onMounted(() => {
  queryDeviceCurve()
})

onBeforeUnmount(() => {
  charts.forEach((chart) => chart.dispose())
})
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss" scoped>
.warning-echarts {
  display: flex;
  flex-direction: column;

  .echart-box {
    position: relative;
    height: 160px;
    width: 100%;

    #echarts-wrap {
      height: 160px;
      width: 100%;
      min-width: 367px;
    }
  }

  .btn-group {
    color: rgba(153, 153, 153, 1);
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    padding-right: 20px;

    .groupActive {
      color: rgba(51, 51, 51, 1);
    }
  }

  .tip {
    padding: 8px;
    color: #ff4d4f;
    border-radius: 4px 4px 4px 4px;
    background: rgba(255, 242, 242, 1);
    margin-bottom: 10px;
  }

  .time-plain {
    color: #3093fa;
    border: 1px solid #3093fa;
    font-size: 10px;
    border-radius: 4px;
    padding: 3px 10px;
    display: flex;
    align-items: center;
  }
}
</style>
