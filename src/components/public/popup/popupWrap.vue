<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-09 15:31:14
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-16 17:36:02
 * @FilePath: /angang-platform/src/components/public/popup/popupWrap.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <popup-component v-model="modelValue">
    <div class="popup-wrap">
      <header class="flex justify-between items-center">
        <div class="sub-title" :style="{ maxWidth: subTitleWidth }">
          <myTooltip :str="popupTitle" :size="16" :weight="700"></myTooltip>
        </div>
        <slot name="unitName"></slot>
        <el-icon @click="close" :size="16" color="#999999">
          <icon-close-bold class="close-icon" />
        </el-icon>
      </header>
      <!-- <div class="content">
        <slot></slot>
      </div> -->
      <div :class="popupTitle == '安消联动' ? 'content2' : 'content'">
        <slot></slot>
      </div>
    </div>
  </popup-component>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  popupTitle: {
    type: String,
    default: '测试标题',
  },
  width: {
    type: String,
    default: '400px',
  },
  subTitleWidth: {
    type: String,
    default: '400px',
  },
})

const emits = defineEmits(['close', 'update:modelValue'])
const modelValue = computed({
  get: () => props.modelValue,

  set: (val) => {
    emits('update:modelValue', val)
  },
})

function close() {
  emits('close')
  emits('update:modelValue', false)
}
</script>

<script lang="ts">
export default {
  name: 'popupWrap',
}
</script>

<style lang="scss">
.popup-wrap {
  background: white;
  border-radius: 6px;

  header {
    height: 50px;
    // border-bottom: 1px solid #ebeef5;
    padding: 0 24px;
  }

  .content {
    padding: 24px;
  }

  .content2 {
    padding: 24px 24px 35px 24px;
  }

  .sub-title {
    max-width: 44em;
    color: #333333;
    position: relative;
    font-size: 18px;
    font-family: 'Alibaba-PuHuiTi-Medium';
  }

  .el-icon {
    cursor: pointer;

    .close-icon {
      width: 24px;
      height: 24px;
    }
  }
}
</style>
