<template>
  <transition :name="transitionName">
    <div class="popup-container" v-if="modelValue">
      <div class="popup-mask" @click="maskClick"></div>
      <slot></slot>
    </div>
  </transition>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  appendToBody: {
    type: Boolean,
    default: true,
  },
  closeOnClickModal: {
    type: Boolean,
    default: true,
  },
  name: {
    type: String,
    default: 'fade',
  },
})

const emits = defineEmits(['mask-click'])

const transitionName = computed(() => `popup-${props.name}`)

function maskClick() {
  if (props.closeOnClickModal) emits('mask-click')
}
</script>

<script lang="ts">
export default {
  name: 'popupComponent',
}
</script>

<style lang="scss">
.popup-container {
  position: fixed;
  height: 100%;
  width: 100%;
  z-index: 1000;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  .popup-mask {
    background: rgba(0, 0, 0, 0.5);
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    position: fixed;
    z-index: -1;
  }
}
.popup-fade-enter-active {
  animation: popup-fade-in 0.3s;
}

.popup-fade-leave-active {
  animation: popup-fade-out 0.3s;
}

.popup-left-enter-active {
  animation: popup-left-in 0.3s;
}

.popup-left-leave-active {
  animation: popup-left-out 0.3s;
}

.popup-right-enter-active {
  animation: popup-right-in 0.3s;
}

.popup-right-leave-active {
  animation: popup-right-out 0.3s;
}

@keyframes popup-fade-in {
  0% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes popup-fade-out {
  0% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
  100% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
}

@keyframes popup-left-in {
  0% {
    transform: translate3d(50px, 0px, 0);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes popup-left-out {
  0% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
  100% {
    transform: translate3d(50px, 0px, 0);
    opacity: 0;
  }
}

@keyframes popup-right-in {
  0% {
    transform: translate3d(-50px, 0px, 0);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes popup-right-out {
  0% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
  100% {
    transform: translate3d(-50px, 0px, 0);
    opacity: 0;
  }
}
</style>
