<template>
  <!-- 选择试卷库 -->
  <el-dialog v-model="examVisible" width="972px" @close="closeExamDialog" align-center>
    <div class="exam-box">
      <!-- 试卷列表 -->
      <div class="exam-list-box">
        <el-scrollbar>
          <el-radio-group v-model="examID">
            <div v-for="(item, index) in examList" :key="index" class="flex relative">
              <div class="radio">
                <el-radio :value="item.id" @change="optionChange(item, index)" />
              </div>
              <div class="exam-list">
                <div>
                  <div class="exam-title">试卷名称：{{ item.examName }}</div>
                  <div>
                    <span class="exam-time inline-block w-[220px]">创建时间:{{ item.createTime }}</span>
                    <span class="exam-time inline-block w-[77px]">总题数:{{ item.totalCount }}</span>
                    <span class="exam-time inline-block w-[65px]">总分:{{ item.totalScore }}</span>
                    <span class="exam-time inline-block w-[144px]">适用培训类型:{{ item.industryName }}</span>
                  </div>
                </div>
                <div class="mr-[20px]">
                  <el-button type="primary" @click="previewTestPaper(item)">
                    <svg-icon name="perview" :size="26" class="mt-[3px] mr-[5px]"></svg-icon>预览试卷
                  </el-button>
                </div>
              </div>
            </div>
          </el-radio-group>
          <no-data v-if="!examList.length"></no-data>
        </el-scrollbar>
      </div>
      <div class="btn-page" v-if="total">
        <el-pagination layout=" total, sizes, prev, pager, next, jumper" :page-size="pageSize"
          :page-sizes="[5, 10, 20, 50]" :total="total" v-model:current-page="curPage"
          @current-change="handleCurrentChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer pr-[20px]">
        <el-button @click="cancle">取消</el-button>
        <el-button type="primary" @click="questionSubmit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { list } from './exam'
import { ElMessage } from 'element-plus'
import $API from '~/common/api'

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: '',
  },
})
const examVisible = ref(false) // 试卷库弹框
const perviewVisible = ref(false) // 预览试卷
const examID = ref('') // 选取的试卷id
const previewId = ref('') // 预览试卷id
const examObj = ref({}) // 选取的试卷对象
const total = ref()
const curPage = ref(1)
const pageSize = ref(5)
const pageNo = ref(1)
const examList = ref<list[]>([]) // 试卷库数据
const emit = defineEmits(['update:dialogVisible', 'submit'])
// 获取试卷库
const getMyExamList = () => {
  let data = {
    pageNo: pageNo.value,
    pageSize: pageSize.value,
  }
  $API
    .post({
      url: 'train-server/examPaper/queryKsExamPaperByPage',
      data,
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        pageNo.value = res.data.pageNo
        pageSize.value = res.data.pageSize
        total.value = res.data.total
        examList.value = res.data.rows
      }
    })
}
// 选择试卷
const optionChange = (option, index) => {
  console.log('option', option, index)
  examObj.value = option
  examID.value = option.id
}
// 预览试卷
const previewTestPaper = (item) => {
  examObj.value = item
  previewId.value = item.id
  perviewVisible.value = true
}
// 取消选择试卷库
const cancle = () => {
  examID.value = ''
  examVisible.value = false
  emit('update:dialogVisible', false)
}
// 试卷库关闭
const closeExamDialog = () => {
  cancle()
}
// 确定提交选择试卷
const questionSubmit = () => {
  if (!examID.value) {
    ElMessage.warning('请选择试卷')
    return false
  }
  examVisible.value = false
  emit('update:dialogVisible', false)
  emit('submit', examObj.value)
}
const handleSizeChange = (val: number) => {
  pageSize.value = val
  getMyExamList()
}
const handleCurrentChange = (val: number) => {
  pageNo.value = val
  getMyExamList()
}

const closeFn = () => {
  perviewVisible.value = false
}

watch(
  () => props.dialogVisible,
  (val) => {
    examVisible.value = val
    examID.value = props.id
    // pageNo.value = 1
    // getMyExamList()
  },
  { immediate: true }
)

onMounted(() => {
  getMyExamList()
})
</script>

<style lang="scss" scoped>
.exam-box {
  padding-left: 20px;
  padding-right: 30px;
}

.exam-list-box {
  height: 510px;
  // overflow: hidden;
  // overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
  padding-top: 15px;
  margin-bottom: 10px;

  .exam-list {
    @apply ml-[10px] mb-[15px] pl-[16px] pb-[10px] pt-[10px] flex w-full;
    background: #ffffff;
    border: 1px solid #ebeef5;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .exam-title {
      font-weight: 500;
      font-size: 16px;
      color: #19191a;
      margin-bottom: 16px;
    }

    .exam-time {
      font-weight: 400;
      font-size: 14px;
      color: #303133;
      margin-right: 20px;
    }
  }

  .btn {
    margin-right: 10px;
  }

  :deep(.el-select__placeholder.is-transparent) {
    color: #1e1e1e;
  }

  .radio {
    // margin-left: 20px;
    margin-top: 20px;
  }
}

.btn-page {
  width: 100%;
  margin-top: 20px;
  padding-right: 20px;
  text-align: right;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-pagination.is-background .el-pager li) {
  border: 1px #d9d9d9 solid;
  border-radius: 4px 4px 4px 4px;
  margin: 0 3px;
}

:deep(.el-pagination.is-background .el-pager li.is-active) {
  border: 1px #335cff solid;
  border-radius: 4px;
}

:deep(.el-pagination.is-background .btn-prev),
:deep(.el-pagination.is-background .btn-next) {
  border: 1px #d9d9d9 solid;
  border-radius: 4px;
}

:deep(.el-dialog) {
  padding: 0 !important;
}

:deep(.el-dialog__headerbtn) {
  top: 5px !important;
}

:deep(.el-dialog__header.show-close) {
  padding-right: 0 !important;
  padding: 0;
  border-bottom: 0;
}

:deep(.dialog-footer) {
  @apply pb-[20px] pr-[20px];
}

:deep(.popup-container) {
  z-index: 2013;
}

:deep(.el-radio-group) {
  display: block;
}
</style>
