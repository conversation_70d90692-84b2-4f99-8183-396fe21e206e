<template>
  <div
    class="unitpopup flex-row justify-between cursor-pointer"
    :class="{ showAll: isShowBody }"
    @click="setShow($event)"
  >
    <div class="gg-build-monitor">
      <div class="gg-build-head">
        <div class="build-head-name">{{ data.buildingName }}</div>
        <div>
          <img class="up" src="../../../assets/image/unitMap/up.png" v-show="!isShowBody" alt="" />
          <img class="down" src="../../../assets/image/unitMap/angle-double-down.png" v-show="isShowBody" alt="" />
        </div>
      </div>
      <div class="gg-build-body" v-show="isShowBody">
        <div class="total_stateNum">
          <span class="build_state_title">设备</span><span class="build_state_num">{{ data.deviceCount || 0 }}</span>
        </div>
        <div class="total_stateNum">
          <span class="build_state_title">巡检点位</span
          ><span class="build_state_num">{{ data.bluetoothCount || 0 }}</span>
        </div>
        <div class="total_stateNum total-title">
          <span class="build_state_title">视频</span><span class="build_state_num">{{ data.videoCount || 0 }}</span>
        </div>
        <div class="build_stateNum build_state_alarm">
          <span class="build_state_title">火警</span
          ><span class="build_state_num" style="color: #fb1313">{{ data.alarmNum || 0 }}</span>
        </div>
        <div class="build_stateNum build_state_warn">
          <span class="build_state_title">预警</span
          ><span class="build_state_num" style="color: #9f1d8b">{{ data.warningNum || 0 }}</span>
        </div>
        <div class="build_stateNum build_state_trouble">
          <span class="build_state_title">故障</span
          ><span class="build_state_num" style="color: #fd9905">{{ data.faultNum || 0 }}</span>
        </div>
        <div class="build_stateNum build_state_abnormal">
          <span class="build_state_title">隐患</span
          ><span class="build_state_num" style="color: #f9e400">{{ data.troubleNum || 0 }}</span>
        </div>
        <div class="build_stateNum build_state_abnormal">
          <span class="build_state_title">动作</span
          ><span class="build_state_num" style="color: #2476ee">{{ data.movingNum || 0 }}</span>
        </div>
        <div class="build_stateNum build_state_abnormal">
          <span class="build_state_title">离线</span
          ><span class="build_state_num" style="color: #999">{{ data.offlineNum || 0 }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, watch, ref, onMounted } from 'vue'

interface Props {
  isShowAll: boolean
  name: string
  data: any
  onClick: (a, b, c) => void
}
const zIndexNum: any = ref(20)

const isShowBody = ref(false)
const props = withDefaults(defineProps<Props>(), {
  name: '',
  isShowAll: false,
  data: {},
  onClick: (a, b, c) => {},
})
onMounted(() => {
  sessionStorage.setItem('indexNum', '35')
})

//获取指定class的父节点
const getParents = (element, className) => {
  //dom.getAttribute('class')==dom.className，两者等价
  var returnParentElement = null
  function getpNode(element, className) {
    //创建父级节点的类数组
    let pClassList = element.parentNode.getAttribute('class')
    let pNode = element.parentNode
    if (!pClassList) {
      //如果未找到类名数组，就是父类无类名，则再次递归
      getpNode(pNode, className)
    } else if (pClassList && pClassList.indexOf(className) < 0) {
      //如果父类的类名中没有预期类名，则再次递归
      getpNode(pNode, className)
    } else if (pClassList && pClassList.indexOf(className) > -1) {
      returnParentElement = pNode
    }
  }
  getpNode(element, className)
  return returnParentElement
}

const setShow = (e) => {
  let target: any = null

  // isShowBody.value = !isShowBody.value

  if (e && e.path) {
    e.path.forEach((i) => {
      if (i.className == 'Build-popup') target = i
    })
  } else {
    target = getParents(e.target, 'Build-popup')
  }
  props.onClick(props.data, target)
  // let num = Number(sessionStorage.getItem('indexNum') || 15) || 15
  // num++
  // sessionStorage.setItem('indexNum', num.toString())
  // if (isShowBody.value) {
  //   target.style.zIndex = num
  // } else {
  //   target.style.zIndex = 1
  // }
}

// const onClick = ()=>{
// }
const clickHandle = () => {}
// defineExpose({ myname,myhasBtn })
const setData = () => {}
defineExpose({ setData })
</script>

<style lang="scss" scoped>
.showAll {
  position: relative;
  z-index: 99;
}

.unitpopup {
  .gg-build-monitor {
    border-radius: 8px;
    overflow: hidden;
    min-width: 160px;
  }

  .gg-build-head {
    /* background: #fff; */
    display: flex;
    align-items: center;
    width: 100%;
    height: 35px;
    font-size: 14px;
    box-sizing: border-box;
    padding: 0 5px;
    line-height: 35px;
    color: #161616;
    background: linear-gradient(180deg, #dee7ff 0%, #feffff 80%, #fff 100%);
    border-radius: 6px;
  }

  .gg-build-head {
    img {
      width: 15px;
    }
  }

  .ggicon-arrowTop {
    margin-top: 10px;
    /* color: #5163c9; */
    color: #161616;
    cursor: pointer;
    display: inline-block;
    width: 14px;
    float: right;
    margin-right: 4px;
  }

  .ggicon-arrowTop {
    transform: rotate(180deg);
  }

  // .simple-state-trouble,.simple-state-warn,.simple-state-alarm {
  // 	width: 20px;
  // 	height: 30px;
  // 	margin-top: 2px;
  // 	margin-right: 5px;
  // 	float: left;
  // }

  // .simple-state-warn {
  // 	background: url("../image/simple-logo-warn.png") no-repeat center;;
  // }
  // .simple-state-alarm {
  // 	background: url("../image/simple-logo-alarm.png") no-repeat center;;
  // }
  // .simple-state-trouble {
  // 	background: url("../image/simple-logo-hidden.png") no-repeat center;;
  // }

  .ggicon-arrowBottom {
    margin-top: 10px;
    /* color: #5163c9; */
    color: #161616;
    cursor: pointer;
    display: inline-block;
    width: 14px;
    float: right;
    margin-right: 4px;
  }

  .build-head-name {
    display: inline-block;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
  }

  .simple-head-name {
    display: inline-block;
    width: 130px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    /* color: #5163c9; */
    color: #161616;
  }

  .gg-build-body {
    /* margin-top: .5rem; */
    width: 174px;
    height: 230px;
    background: #fff;
    border-radius: 4px;
    padding: 0 15px;
  }

  .gg-build-body {
    width: 160px;
    // height: 10rem;
    background: #fff;
    border-radius: 4px;
  }

  .total_stateNum {
    height: 25px;
    line-height: 25px;
    display: flex;
    justify-content: space-between;
    /* border-bottom: 1px solid #e6e6e6; */
  }

  .total-title {
    border-bottom: 1px solid #e6e6e6;
  }

  .build_stateNum {
    position: relative;
    height: 24px;
    line-height: 24px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
