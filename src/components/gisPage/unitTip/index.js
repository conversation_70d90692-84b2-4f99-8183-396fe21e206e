/**
 * vue有两种形式的代码 compiler（模板）模式和 runtime 模式（运行时）
 * import Vue from "vue/dist/vue.esm.js"; 直接指定runtime模式
 */
// import Vue from "vue";
import { createApp, createVNode, render } from 'vue'

import gisPopup from './gisPopup.vue'
let el = null

export function setPopup(config) {
  const { elment, options } = config

  // let app = createApp(App)
  // registerComponent(app)
  // initGSParams()
  // app.use(piniaInstance)

  // app.use(router)

  // app.use(elementPlus, { size: '', locale: zhCn })

  // app.mount('#app')

  if (!elment) return
  // if (el) {
  //    elment.removeChild(el);
  //    el = null;
  //  }
  const PopUpConstructor = createVNode(gisPopup, {
    ...options,
  }) // 返回一个vue子类
  //创建实例并且挂载

  el = document.createElement('div')
  render(PopUpConstructor, el)
  // const popup = new PopUpConstructor().$mount(document.createElement('div'));
  //初始化参数
  // if( options ){
  // 	for (let key in options) {
  // 		popup[key] = options[key];
  // 	}
  // }
  //将元素插入目标节点上
  // PopUpConstructor.name = '华夏'
  elment.appendChild(el)
  // elment.appendChild(popup.$el);
  return el
  // return popup;
}
