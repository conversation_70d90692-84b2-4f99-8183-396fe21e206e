<template>
  <div class="text-[#333] text-[14px] h-full overflow-auto">
    <div class="pt-[10px]">
      <div>
        <span> 平台首次接收时间： </span>
        {{ info.firstEventTime || '--' }}
      </div>
    </div>
    <!--  -->
    <div class="pt-[10px] flex" v-if="info.firstPicBase64">
      <div class="min-w-[110px]">现场照片:</div>
      <div class="w-[150px] h-[100px] ml-[20px]">
        <el-image
          style="width: 100%; height: 100%"
          :src="getUrl(info.firstPicBase64)"
          :zoom-rate="1.2"
          :max-scale="7"
          :min-scale="0.2"
          :preview-src-list="[getUrl(info.firstPicBase64)]"
          :initial-index="4"
          fit="cover"
        >
          <template #error>
            <div class="image-slot">
              <el-icon><icon-picture /></el-icon>
            </div>
          </template>
        </el-image>
        <!-- <img class="w-full h-full" :src="getUrl(info.firstPicBase64)" alt="" /> -->
      </div>
    </div>
    <div class="pt-[10px]">
      <div>
        <span> 平台末次接收时间： </span>
        {{ info.iotReceiveTime || '--' }}
      </div>
    </div>
    <!--  -->
    <div class="pt-[10px] flex mr-[20px]" v-if="info.picBase64">
      <div class="min-w-[110px]">现场照片:</div>
      <div class="w-[150px] h-[100px] ml-[20px]">
        <!-- <img class="w-full h-full" :src="getUrl(info.picBase64)" alt="" /> -->
        <el-image
          style="width: 100%; height: 100%"
          :src="getUrl(info.picBase64)"
          :zoom-rate="1.2"
          :max-scale="7"
          :min-scale="0.2"
          :preview-src-list="[getUrl(info.picBase64)]"
          :initial-index="4"
          fit="cover"
        >
          <template #error>
            <div class="image-slot">
              <el-icon><icon-picture /></el-icon>
            </div>
          </template>
        </el-image>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { Picture as IconPicture } from '@element-plus/icons-vue'
// /monitor/getMonitorEventInfo?deviceId=20240823105205547479&unitId=AHHF_QHHFY_20180408
import $API from '~/common/api'
const props = defineProps<{
  unitId: string
  deviceId: string
}>()
watch(
  () => props.deviceId,
  (val) => {
    getMonitorEventInfo()
  }
)
const info = ref<{
  picBase64: string
  firstPicBase64: string
  [property: string]: any
}>({
  picBase64: '',
  firstPicBase64: '',
})
const getUrl = (val) => {
  // /ycsy/2024-11-27/images/ehs/ycsy/bwFck/8b01ef3237c64556ac3803045998fca5.png
  // https://agjp.tanzervas.com/aqsc/v1/ycsy//aqsc/v1/ycsy/2024-11-22/images/ehs/ycsyqt/bwFck/9605654ce55a4ceb927e7e286c55d888.png
  return 'https://agjp.tanzervas.com/aqsc/v1' + val
}
const getMonitorEventInfo = async () => {
  // if(! props.deviceId||! props.unitId) return
  const params = {
    deviceId: props.deviceId,
    unitId: props.unitId,
    // deviceId: 'z_621',
    // unitId: 'AHHF_QHHFY_20180408',
    // modelType: 'unit_base_url',
  }

  try {
    const res: any = await $API.post({
      url: '/ehs-clnt-internetMonitor-service/monitor/getMonitorEventInfo',
      params,
    })
    if (res.code !== 'success') return
    if (res.data && res.data.rows && res.data.rows.length > 0) {
      info.value = res.data.rows[0]
    }
  } catch (e) {}
}

onMounted(() => {
  getMonitorEventInfo()
})
</script>
<style lang="scss" scoped>
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 30px;
}
</style>
