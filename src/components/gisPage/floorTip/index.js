/**
 * vue有两种形式的代码 compiler（模板）模式和 runtime 模式（运行时）
 * import Vue from "vue/dist/vue.esm.js"; 直接指定runtime模式
 */
// import Vue from "vue";
import { createApp, createVNode, render } from 'vue'

import gisPopup from './gisPopup.vue'
let el = null
export function setPopup(config) {
  const { elment, options } = config
  if (!elment) return
  if (el) {
    el.remove()
    // elment.removeChild(el);
    // el = null;
  }
  const PopUpConstructor = createVNode(gisPopup, {
    ...options,
  }) // 返回一个vue子类
  //创建实例并且挂载
  el = document.createElement('div')
  render(PopUpConstructor, el)
  elment.appendChild(el)
  // elment.appendChild(popup.$el);
  return el
  // return popup;
}
