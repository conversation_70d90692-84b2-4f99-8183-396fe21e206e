<template>
  <div class="linkage">
    <div class="linkage-header" v-if="!isOnlyOne">
      <div class="left btn">
        <img src="@/assets/image/unitMap/left.png" alt="" />
      </div>
      <div class="scrollbar-box">
        <el-scrollbar ref="scrollbarRef" @scroll="scroll">
          <div>
            <div class="scrollbar-flex-content" ref="innerRef">
              <div
                v-for="item in videoList"
                :key="item"
                class="scrollbar-demo-item"
                :class="{ active: isActive == item.deviceId }"
                track
                @click="select(item)"
              >
                <myTooltip :str="item.deviceAddress"></myTooltip>
                <!-- {{item.deviceAddress}} -->
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div class="right btn" @click="rightClick">
        <img src="@/assets/image/unitMap/right.png" alt="" />
      </div>
    </div>

    <div class="video-box" :class="{ OnlyOnevideo: isOnlyOne }" v-loading="showLoading">
      <video-component v-if="videoPath" style="width: 100%; height: 100%" :video-url="videoPath"></video-component>
      <div v-if="!videoPath" class="nodata">
        <noData></noData>
      </div>
    </div>
    <!-- <div>
        <timelin></timelin>
      </div> -->
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import $API from '~/common/api'
import { getVideoUrl } from '@/common/services'
import videoComponent from '@/components/public/videoComponent.vue'
import noData from '@/components/public/noData.vue'
import myTooltip from './toolTipIndex.vue'

// import timelin from './timelin.vue'

const scrollTop = ref(0)
const scrollbarRef = ref()
const innerRef = ref<HTMLDivElement>()
const isActive = ref(1)
const videoPath: any = ref('')
const videoList = ref<any[]>([])
const showLoading = ref(false)
const props = defineProps({
  linkageData: {
    type: Object,
    default: () => {},
  },
  isOnlyOne: {
    type: Boolean,
    default: false,
  },
})

let CLEARVIDEO = PubSub.subscribe('CLEARVIDEO', async () => {
  videoPath.value = ''
  PubSub.unsubscribe(CLEARVIDEO)
})
onMounted(() => {
  if (props.isOnlyOne) {
    showOneVide(props.linkageData)
    return
  }
  queryLinkageVideoList()
})

const setScrollTop = () => {
  scrollbarRef.value!.setScrollLeft(scrollTop.value)
}
const scroll = ({ scrollLeft }) => {
  scrollTop.value = scrollLeft
}
const rightClick = () => {
  scrollTop.value = scrollTop.value + 80
  setScrollTop()
}
const select = async (item) => {
  isActive.value = item.deviceId
  videoPath.value = ''
  showLoading.value = true
  let videoObj = await getVideoUrl(item)
  showLoading.value = false
  videoPath.value = videoObj
}

const queryLinkageVideoList = async () => {
  const params = {
    deviceId: props.linkageData.deviceId,
    unitId: props.linkageData.unitId,
    // unitId :'AHHF_QHHFY_20180408'
  }

  try {
    const res: any = await $API.post({
      url: '/unit/distribution/queryLinkageVideoList',
      params,
    })
    if (res.code !== 'success') return
    videoList.value = res.data
    if (res.data.length > 0) {
      select(res.data[0])
    }
  } catch (e) {
    //TODO handle the exception
    videoList.value = []
  }
}

const showOneVide = async (item) => {
  isActive.value = item.deviceId
  videoPath.value = ''
  showLoading.value = true

  let videoObj = await getVideoUrl(item)
  videoPath.value = videoObj
  showLoading.value = false
}

//

// defineExpose({ myname,myhasBtn })
</script>

<style lang="scss" scoped>
.linkage {
  margin-left: 15px;
  background: #ffffff;
  width: 508px;
  height: 400px;
  border-radius: 10px 10px 0 0;

  .str-content {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;

    .elStr {
      position: absolute;
      right: 100%;
      top: 0;
    }
  }

  .str-span {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .linkage-header {
    display: flex;
    align-items: center;

    .scrollbar-box {
      flex: 1;
    }

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 28px;
      background: #ffffff;
      border: 1px solid #cecece;
      border-radius: 4px;
      flex-shrink: 0;
      margin: 0 15px;

      img {
        width: 12px;
      }
    }
  }

  .video-box {
    height: 303px;
    width: 100%;
  }

  .OnlyOnevideo {
    height: 100%;
    background-color: #000000;
  }

  .nodata {
    height: 303px;
    width: 100%;
    background-color: #000;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .scrollbar-flex-content {
    display: flex;
  }

  .scrollbar-demo-item {
    width: 64px;
    height: 40px;
    line-height: 40px;
    flex-shrink: 0;
    white-space: nowrap;
    text-align: center;
    text-overflow: ellipsis;
    overflow: hidden;
    color: #000;
  }

  .active {
    color: #fff;
    background: rgba(0, 128, 255, 1);
  }
}
</style>
