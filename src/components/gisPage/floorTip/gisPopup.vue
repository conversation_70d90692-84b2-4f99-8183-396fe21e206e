<template>
  <div class="flex deviceInfo">
    <div class="unitDetail">
      <div class="title">
        <div class="flex" style="align-items: center; position: relative">
          <div class="pr-10px" style="font-size: 16px; color: #333333">
            {{ listData.deviceTypeName }}
          </div>
          <div
            v-if="listData.deviceClassification == 3 || listData.deviceClassification == 4"
            style="display: flex; align-items: center"
          >
            <img v-if="listData.onlineState === 0" class="w-12px h-12px mr-3px" src="@/assets/image/dwyzt/online.png" />
            <img
              v-if="listData.onlineState == 1 && listData.deviceTypeId != '25030000'"
              class="w-12px h-12px mr-3px"
              src="@/assets/image/dwyzt/offline.png"
            />
            <!-- deviceTypeId: "25030000" -->
            <span
              v-if="listData.deviceTypeId != '25030000'"
              :class="listData.onlineState === 0 ? 'zx-status' : 'lx-status'"
            >
              {{ listData.onlineState === 0 ? '在线' : listData.onlineState === 2 ? '未激活' : '离线' }}</span
            >
          </div>

          <span
            class="device-status"
            :style="{
              background: addPointColorNew(pointData.priorityEventType),
            }"
          >
            {{ deviceStatus(pointData.priorityEventType) }}
          </span>
        </div>
        <div class="pt-7px" style="font-size: 12px; color: #999999">
          {{ listData.showDeviceAddress }}
        </div>
        <!-- <div class="close-btn">
          <el-icon :size="20" @click="close"><Close /></el-icon>
        </div> -->
      </div>
      <el-divider></el-divider>
      <div class="main">
        <div class="button flex bg-white">
          <div class="flex items-center w-full overflow-auto" v-if="listData && listData.deviceClassification">
            <!-- <div class="text"></div> -->
            <el-button
              class="device_label_button"
              v-if="isShowBtn && pointData.eventType == '1'"
              @click="aqls_button(5)"
              :class="aqlsActive == 5 ? 'device_active' : ''"
              >火警信息</el-button
            >
            <el-button
              class="device_label_button"
              @click="aqls_button(0)"
              :class="aqlsActive == 0 ? 'device_active' : ''"
              >设备信息</el-button
            >

            <!-- v-if="![1, 2, 5, 6, 4].includes(listData.deviceClassification) && listData.deviceTypeId != 23130000" -->
            <el-button
              v-if="listData.measurement && listData.measurement != '{}'"
              class="device_label_button"
              @click="aqls_button(1)"
              :class="aqlsActive == 1 ? 'device_active' : ''"
              >实时读数</el-button
            >
            <!-- v-if="![1, 2, 5, 6, 4].includes(listData.deviceClassification) && listData.deviceTypeId != 23130000" -->
            <el-button
              v-if="listData.measurement && listData.measurement != '{}'"
              class="device_label_button"
              @click="aqls_button(2)"
              :class="aqlsActive == 2 ? 'device_active' : ''"
              >监测曲线</el-button
            >
            <el-button
              v-if="![5, 6].includes(listData.deviceClassification)"
              class="device_label_button"
              @click="aqls_button(3)"
              :class="aqlsActive == 3 ? 'device_active' : ''"
              >异常记录</el-button
            >
            <el-button
              v-if="![1, 2, 3, 6, 4].includes(listData.deviceClassification)"
              class="device_label_button"
              @click="aqls_button(4)"
              :class="aqlsActive == 4 ? 'device_active' : ''"
              >触发记录</el-button
            >
          </div>
        </div>
        <DeviceInfoDetails v-if="aqlsActive == 0" :listData="listData" @showVideo="showVideo"></DeviceInfoDetails>
        <DwyztRealtimeDegrees v-if="aqlsActive == 1" :id="listData.deviceId" />
        <WarningEcharts class="mt-17px" v-if="aqlsActive == 2" :device-id="listData.deviceId" />
        <AbnormalRecord class="h-209px mt-18px relative" v-if="aqlsActive == 3" :deviceInfo="listData" />
        <DwyztCfjlAbnormalRecord class="h-209px mt-18px relative" v-if="aqlsActive == 4" :deviceInfo="listData" />
        <deviceTime
          class="mt-[10px] h-219px"
          v-if="aqlsActive == 5"
          :deviceId="pointData.deviceId"
          :unit-id="pointData.unitId"
        ></deviceTime>
      </div>
    </div>
    <div>
      <linkage v-if="isShowLinkage" :isOnlyOne="isOnlyOne" :linkageData="linkageData"></linkage>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { addPointColorNew } from '@/common/utils'
import DeviceInfoDetails from './deviceInfo.vue'
import DwyztRealtimeDegrees from '~/components/monitorDetail/dwyztRealtimeDegrees.vue'
import AbnormalRecord from '~/components/monitorDetail/abnormalRecord.vue'
import DwyztCfjlAbnormalRecord from '~/components/monitorDetail/dwyztCfjlAbnormalRecord.vue'
import WarningEcharts from '~/components/public/warningEchartsDwyzt.vue'
import { ref, onMounted, computed } from 'vue'
import $API from '~/common/api'
import { ElButton, ElDivider } from 'element-plus'
import config from '~/config'
import linkage from './linkage.vue'
import deviceTime from './deviceTime.vue'

const deviceIds = ref('23160000')
const aqlsActive: any = ref(null)
const isShowLinkage: any = ref(false)
const isOnlyOne: any = ref(false)
const props = defineProps({
  type: {
    type: String,
    default: '',
  },
  pointData: {
    type: Object,
    default: () => {},
  },
  isVideoBufferQuery: {
    type: Boolean,
    default: false,
  },
})
const isShowBtn = computed(() => {
  return deviceIds.value.includes(props.pointData.deviceTypeId)
})
const deviceStatus = computed(() => {
  return (type) => {
    let p = ''
    if (type == 7) {
      p = '离线'
    } else if (type == 1) {
      p = '火警'
    } else if (type == 2) {
      p = '预警 '
    } else if (type == 5) {
      p = '动作'
    } else if (type == 3) {
      p = '故障'
    } else if (type == 4) {
      p = '隐患'
    } else {
      p = '正常'
    }
    return p
  }
})
const listData: any = ref({})
const linkageData: any = ref({
  deviceId: '',
  unitId: '',
})
function aqls_button(val: any) {
  aqlsActive.value = val
}
onMounted(async () => {
  if (props.isVideoBufferQuery) {
    // linkageData.value.deviceId = 30601
    linkageData.value.deviceId = props.pointData.deviceId
    linkageData.value.unitId = props.pointData.unitId

    hasDeviceLinkage()
  }
  await getDeviceDetails()
})

const hasDeviceLinkage = async () => {
  const params = {
    deviceId: linkageData.value.deviceId,
  }

  try {
    const res: any = await $API.post({
      url: '/unit/distribution/hasDeviceLinkage',
      params,
    })
    if (res.code !== 'success') return
    isShowLinkage.value = res.data
  } catch (e) {
    isShowLinkage.value = false
  }
}
// const close = () => {
//   emit('close')
//   // counterStore.isShowMapDetail = false

// }
async function getDeviceDetails() {
  try {
    const params = {
      deviceId: props.pointData.deviceId,
      modelType: 'gis_serve_url',
    }

    const res: any = await $API.post({
      url: '/bitmap/queryDeviceInfoAndState',
      params,
    })
    if (res && res.code === 'success') {
      listData.value = res.data[0]
      listData.value.unitId = props.pointData.unitId
      aqlsActive.value = 0
      if (deviceIds.value.includes(props.pointData.deviceTypeId) && props.pointData.eventType == '1') {
        aqlsActive.value = 5
      }
    }
  } catch (error) {
  } finally {
  }
}

const showVideo = () => {
  linkageData.value = listData.value
  isShowLinkage.value = true
  isOnlyOne.value = true
}
</script>

<style lang="scss" scoped>
:deep(.custom-empty .custom-empty-page .gray) {
  color: #000000 !important;
}

::v-deep .el-divider--horizontal {
  margin: 12px 0 5px 0px !important;
}

.deviceInfo::after {
  content: '';
  display: block;
  height: 0;
  width: 0;
  position: absolute;
  left: 50%;
  bottom: 0;
  border: 10px solid;
  border-color: #fff transparent transparent transparent;
  transform: translate(-50%, 100%);
}

.unitDetail {
  background: #ffffff;
  color: #fff;
  border: 1px solid #edf6fc;
  // min-height: 322px;
  width: 442px;
  // width: 400px;
  height: 400px;
  box-shadow: 0px 4px 9px 0px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  display: flex;
  flex-direction: column;

  .main {
    padding: 15px;
    position: relative;
    flex: 1;

    .details {
      position: relative;

      > div {
        color: #dae1ef;
        margin-bottom: 12px;
      }

      .deviceBtn-box {
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }

    .device_active {
      background: #ffffff;
      border: 1px solid #0080ff;
      border-radius: 4px;
      font-weight: 400;
      color: #0080ff;
    }

    .unitAddress {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .aqzs-box {
      position: absolute;
      right: 10px;
      top: 0;
      height: 95px;
      width: 95px;
      background-image: url(@/assets/image/unitMap/detail-zs.png);
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .tip {
        position: absolute;
        left: 50%;
        bottom: -6px;
        transform: translate(-50%, 30%);

        width: 42px;
        height: 18px;
        line-height: 15px;
        font-size: 12px;
        text-align: center;
        border: 1px solid #63dde0;
        background: linear-gradient(0deg, #004188 0%, #00d6d8 98%);
        border-radius: 9px;
      }
    }
  }

  .title {
    padding: 21px 0px 0px 17px;
    display: flex;
    flex-direction: column;

    // justify-content: space-between;
    .zx-status {
      font-size: 12px;
      color: #0080ff;
    }

    .lx-status {
      font-size: 12px;
      color: #909299;
    }

    .device-status {
      width: 52px;
      height: 20px;
      line-height: 20px;
      line-height: 20px;
      background: #13a513;
      border-radius: 4px;
      color: #ffffff;
      font-size: 12px;
      // padding: 4px 0;
      text-align: center;
      margin-left: 91px;
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);
    }

    .close-btn {
      cursor: pointer;
    }

    // .el-icon svg {
    //   width: initial;
    //   height: initial;
    // }
  }

  .btn-box {
    display: flex;

    .btn {
      padding: 8px 14px;
      background-image: url(@/assets/image/unitMap/detail-btn.png);
      background-size: 100% 100%;
    }
  }

  .footer {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;

    .left {
      display: grid;
      grid-template-columns: repeat(2, 108px);
      justify-content: space-between;
      flex: 1;

      .item {
        width: 108px;
        height: 66px;
        display: flex;
        align-items: center;
        text-align: center;
        justify-content: center;
        flex-wrap: wrap;
        background-color: red;
        margin-bottom: 8px;
        padding: 10px;

        > div:first-child {
          width: 100%;
          font-size: 14px;
        }

        .num {
          font-size: 18px;

          span {
            font-size: 12px;
          }
        }
      }

      .fire {
        background: #650000;
        border: 1px solid #ff4545;
        box-shadow: inset 0px 0px 16px 0px #ff4343;
      }

      .warning {
        background: #700760;
        border: 1px solid #f13ad5;
        box-shadow: inset 0px 0px 16px 0px #e320c5;
      }

      .hidden-danger {
        background: #7d7200;
        border: 1px solid #ffed2f;
        box-shadow: inset 0px 0px 16px 0px #ffee36;
      }

      .fault {
        background: #774700;
        border: 1px solid #ffb74d;
        box-shadow: inset 0px 0px 16px 0px #ffb649;
      }
    }

    .deviceDetails-content {
      display: grid;
      grid-template-columns: repeat(3, 160px);
      justify-content: space-between;
      flex: 1;

      .item {
        width: 160px;
        height: 66px;
        display: flex;
        align-items: center;
        text-align: center;
        justify-content: center;
        flex-wrap: wrap;
        background: #0b389b;
        border: 1px solid #079afd;
        box-shadow: inset 0px 0px 16px 0px rgba(7, 181, 253, 0.91);
        margin-bottom: 8px;
        padding: 10px;

        .name {
          width: 100%;
          color: #e5f1ff;
          font-weight: 400;
          font-size: 14px;
        }

        .num {
          color: #ffffff;
          font-size: 18px;
        }
      }
    }

    .img-box {
      width: 258px;
      height: 140px;
      // background-color: #eee;
      margin-left: 17px;

      .el-image {
        background-color: rgba(0, 0, 0, 0) !important;
      }

      :deep(.el-image__error) {
        background-color: rgba(0, 0, 0, 0);

        .custom-empty-page {
          .gray {
            color: #fff !important;
          }
        }
      }
    }
  }
}
</style>
