<template>
  <el-tooltip effect="dark" :disabled="isShowToopTip" :content="str" placement="top-start">
    <div class="str-content" @mouseover="onMouseOver()">
      <div class="str-span" :style="fomartStyle">
        <span style="font-size: inherit"> {{ str }}</span>
      </div>
      <div class="elStr" ref="elStr" style="opacity: 0.6">
        {{ str }}
      </div>
    </div>
  </el-tooltip>
</template>
<script lang="ts">
export default {
  name: 'myTooltips',
}
</script>
<script lang="ts" setup>
import { ref, computed, withDefaults } from 'vue'
import { ElTooltip } from 'element-plus'
const isShowToopTip = ref<boolean>(true)
const elStr = ref<HTMLElement>()

const props = withDefaults(
  defineProps<{
    // text :文本
    str: string
    // 字号大小
    size?: number
    // 字体加粗
    weight?: number
    // 文本行数
    clamp?: number
    // 显示位置 element tooltip
    placement?: string
  }>(),
  {
    str: '',
    weight: 400,
    clamp: 2,
    placement: '',
  }
)

const fomartStyle = computed(() => {
  return `-webkit-line-clamp:${props.clamp};font-size:${props.size}px;font-weight:${props.weight};`
})
const onMouseOver = () => {
  let pNode = elStr.value?.parentNode
  let ph = (pNode as HTMLDivElement).offsetHeight || 0
  let sh = elStr.value?.offsetHeight || 0
  isShowToopTip.value = sh <= ph
}
</script>

<style lang="scss" scoped>
.str-content {
  width: 100%;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;

  .elStr {
    position: absolute;
    width: 100%;
    right: 100%;
    top: 0;
  }
}

.str-span {
  width: 100%;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  overflow: hidden;
  line-height: 1.2;
  -webkit-box-orient: vertical;
}
</style>
