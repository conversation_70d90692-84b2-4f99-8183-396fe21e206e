<template>
  <!-- 设备信息 -->
  <div class="gs-collapse mt-15px">
    <div class="collapse collapse-div" v-if="Object.keys(listData).length">
      <ul>
        <li v-if="Independent == '3' || Independent == '6'" class="line-item">
          <span class="left-content">设备IMEI：</span>
          <span class="right-content">{{ listData.deviceNum || '无' }} </span>
        </li>
        <li class="line-item">
          <span class="left-content">设备编号：</span>
          <span class="right-content">{{ listData.deviceId || '无' }} </span>
        </li>
        <li class="line-item" v-if="Independent == '1' || Independent == '2'">
          <span class="left-content">主机回路点位：</span>
          <span class="right-content">{{ switchCode(listData) }}</span>
        </li>
        <li class="line-item flex">
          <span class="left-content">品牌型号：</span>
          <span class="right-content">
            <myTooltip :str="brandmodel(listData)"></myTooltip>
          </span>
        </li>
        <!-- <li class="line-item">
         <span class="left-content">规格型号：</span>
          <span class="right-content"
            >{{ listData?.produceInfo?.model || '--' }}
          </span>
        </li> -->
      </ul>
    </div>
    <div class="showVideo" v-if="Independent == 6" track @click="showVideo">查看视频</div>
    <no-data v-show="Object.keys(listData).length == 0"></no-data>
  </div>
</template>
<script lang="ts">
export default {
  name: 'FireDetail',
}
</script>
<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { normalizeAddress } from '@/common/utils'
import { switchCode } from '@/utils'
import noData from '@/components/public/noData.vue'
import myTooltip from './toolTipIndex.vue'

const emits = defineEmits(['showVideo'])
const props = defineProps({
  listData: {
    type: Object,
    default: () => {},
  },
})

const Independent: any = ref('')
const monitorFlag: any = ref('')
const brandmodel = (data) => {
  let str = ''
  if (data.produceInfo && data.produceInfo.brand) {
    str += data.produceInfo.brand
  }
  if (data.produceInfo && data.produceInfo.model) {
    str += data.produceInfo.model
  }
  return str || '--'
}

const listData = computed(() => {
  return props.listData || '{}'
})

onMounted(() => {
  listData.value._deviceAddres = normalizeAddress(listData.value)
  Independent.value = props.listData.deviceClassification
  monitorFlag.value = props.listData.monitorFlag
})
const showVideo = () => {
  emits('showVideo')
}
//
</script>

<style lang="scss" scoped>
::v-deep .el-collapse-item__header {
  // background: red;
  display: flex;
  justify-content: space-between;
  // margin-bottom: 20px;
}

::v-deep .el-icon el-collapse-item__arrow {
  flex: 1;
}

.collapse {
  // color: #333333;
  // font-weight: 500;
  // font-size: 14px;
  // border: 1px solid #e9e9e9;
  // border-radius: 10px;
  .line-item {
    line-height: normal;
    color: #000;
    font-size: 14px;
    padding: 8px 0;
  }

  .right-content {
    padding-bottom: 0 !important;
  }

  .collapse-div {
    display: flex;

    .left {
      display: flex;
      flex-wrap: wrap;
      width: 100%;

      li {
        width: 100%;
        color: #607590;
        font-size: 14px;
        font-weight: 400;
      }

      li:last-child {
        margin-bottom: 0;
      }
    }

    .earlyWarning {
      display: flex;
    }

    .right {
      display: flex;
      flex-wrap: wrap;

      li {
        width: 100%;
        margin-bottom: 10px;
        color: #333333;
        font-size: 14px;
        font-weight: 400;
        // margin-left: 0px;
        // line-height: 25px;
      }
    }
  }
}

.mapInfo {
  cursor: pointer;
  color: #3093fa;

  img {
    width: 13px;
    height: 16px;
  }
}

.operate-disable {
  color: #ccc;
  pointer-events: none;

  img {
    width: 13px;
    height: 16px;
  }
}

.mapIcon {
  height: 30px;
  display: flex;

  .mapAddress {
    width: 140px;
    height: 30px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}

.el-collapse {
  border-top: 0;
}

:deep(.el-collapse-item__header.is-active) {
  border-bottom-color: #ebeef5;
}

:deep(.el-collapse-item__wrap) {
  border-bottom: 0;
}

:deep(.el-collapse-item__content) {
  padding: 0;
}

.gs-collapse .collapse {
  border: none;
  position: relative;
}

.gs-collapse {
  position: relative;
  height: calc(100% - 32px - 15px);
}

.showVideo {
  position: absolute;
  left: 50%;
  bottom: 10px;
  transform: translateX(-50%);
  width: 98px;
  height: 32px;
  line-height: 32px;
  background: #0080ff;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
}
</style>
