<!-- abnormalRecord -->
<template>
  <div class="abnormalRecord h-full">
    <div class="abnormalRecord-header">
      <div class="abnormal-num">异常：{{ pageModel.total }}</div>
      <buttonGroup :plain="true" :options="btnOptions" v-model:activeTab="activeTab" @setTab="setTab"></buttonGroup>
    </div>
    <div class="abnormalRecord-main h-[calc(100%-59px)]" v-loading="loading">
      <customTimeline
        :activities="abnormalRecordList"
        :total="pageModel.total"
        @getMoerData="getMoerData"
      ></customTimeline>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue'
import buttonGroup from '@/components/public/buttonGroup.vue'
import customTimeline from '@/components/public/customTImeline.vue'
import $API from '~/common/api'
import { PageModel } from '@/types'

const props = defineProps({
  deviceInfo: {
    type: Object,
    default: () => {},
  },
})
const loading = ref(true)
const activeTab = ref('1')
// 分页
const pageModel: PageModel = reactive({
  pageNo: 1,
  pageSize: 30,
  total: 0,
})

const btnOptions = ref([
  {
    value: '1',
    label: '近一周',
  },
  {
    value: '2',
    label: '近一月',
  },
  {
    value: '3',
    label: '近半年',
  },
])
const emits = defineEmits(['activeChange'])

const abnormalRecordList: any = ref([])

const setTab = (type) => {
  activeTab.value = type
  abnormalRecordList.value = []
  pageModel.pageNo = 1
  getAbnormalRecordList()
  emits('activeChange', type)
}

const activities = []

const getAbnormalRecordList = () => {
  loading.value = true
  $API
    .post({
      url: '/dispose/getDeviceAbnormalRecord',
      params: {
        deviceId: props.deviceInfo.deviceId,
        timeType: activeTab.value,
        unitId: props.deviceInfo.unitId,
        pageNo: pageModel.pageNo,
        pageSize: pageModel.pageSize,
        eventType: props.deviceInfo.eventType,
        modelType: 'unit_base_url',
      },
    })
    .then((res: any) => {
      let data =
        res.data.rows.map((item) => {
          return {
            content: item.eventDesc, // 内容
            timestamp: item.lastEventTime, // 时间戳
            type: 'primary', // 节点类型
            eventType: item.eventType, // 事件类型名称
            color: '', // 节点背景颜色
            hollow: true, // 是否空心点 ,根据事件类型名称添加空心远边框颜色
            hideTimestamp: true, // 是否隐藏时间戳
            center: false, //是否垂直居中
            size: 'normal', // normal / large
            icon: '', // string | Component
          }
        }) || []
      abnormalRecordList.value.push(...data)

      console.log(abnormalRecordList.value)
      pageModel.total = res.data.total
      setTimeout(() => {
        loading.value = false
      }, 280)
    })
}

const getMoerData = () => {
  pageModel.pageNo++
  getAbnormalRecordList()
}

onMounted(() => {
  getAbnormalRecordList()
})
</script>

<style lang="scss" scoped>
.abnormalRecord {
  overflow-y: hidden;
  .abnormalRecord-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .abnormal-num {
      color: #e74700;
      font-weight: bold;
    }
  }

  .abnormalRecord-main {
    overflow: hidden;
    &:hover {
      overflow-y: overlay;
      scrollbar-gutter: stable;
    }
    // min-height: 350px;
  }
}
</style>
