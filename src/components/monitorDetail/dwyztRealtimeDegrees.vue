<template>
  <div class="top h-[calc(100%-35px)]">
    <div class="topData">
      <div class="topTitle">
        <p class="fontSize2">对象</p>
        <p class="fontSize2">实时值</p>
      </div>
      <div class="topLi">
        <ul class="infinite-list" style="overflow: auto">
          <li v-for="(count, index) in countList" :key="index" class="infinite-list-item text-center">
            <div class="w-1/2 text-center" style="color: #666666">
              {{ count.monitorItem }}
            </div>
            <div class="w-1/2 text-center" :style="{ color: count.state == 1 ? '#FF5257' : '' }">
              {{ count.monitorItemValue }}{{ count.monitorItemUnit }}
            </div>
          </li>
        </ul>
      </div>
      <no-data class="mt-50px" v-show="Object.keys(countList).length == 0"></no-data>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'monitorDetail',
}
</script>
<script lang="ts" setup>
import { onMounted, ref, nextTick } from 'vue'
import * as echarts from 'echarts'
import $API from '~/common/api'
import { ElMessage } from 'element-plus'
import noData from '@/components/public/noData.vue'

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
})
const dataTimes: any = ref('')
function createLineChart(dataA, dataB, dataC, dataD, nameA, nameB, nameC, nameD, id, unit) {
  let lineBox1 = document.getElementById('linEchart' + id) as HTMLElement
  let myChart = echarts.init(lineBox1)
  let option = {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: [nameA, nameB, nameC, nameD],
      itemHeight: 0,
      itemWidth: 10,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      axisLine: { show: true },
      axisTick: { alignWithLabel: false, show: false },
      data: dataTimes.value,
    },
    yAxis: {
      type: 'value',
      name: ['单位(' + unit + ')'],
      axisLine: { show: true },
      splitLine: {
        //网格线
        lineStyle: {
          type: 'dashed', //设置网格线类型 dotted：虚线   solid:实线
          width: 2,
        },
        show: true, //隐藏或显示
      },
    },
    series: [
      {
        symbol: 'none',
        smooth: true,
        name: nameA,
        type: 'line',
        data: dataA,
      },
      {
        name: nameB,
        type: 'line',
        symbol: 'none',
        smooth: true,
        data: dataB,
      },
      {
        name: nameC,
        type: 'line',
        data: dataC,
        symbol: 'none',
        smooth: true,
      },
      {
        name: nameD,
        type: 'line',
        data: dataD,
        symbol: 'none',
        smooth: true,
      },
    ],
  }
  myChart.clear()
  myChart.setOption(option)
}
const countList: any = ref({})
const updateTime: any = ref('')
function getDeviceState() {
  $API
    .post({
      url: '/device/monitor/queryDeviceStateById',
      params: {
        deviceId: props.id,
        modelType: 'unit_base_url',
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        updateTime.value = res.data.deviceTime
        countList.value = JSON.parse(res.data.measurement || {})
      } else {
        ElMessage({
          message: res.message,
          type: 'error',
        })
      }
    })
}

onMounted(() => {
  getDeviceState()
})
</script>

<style lang="scss" scoped>
.infinite-list {
  height: 792x;
  padding: 0;
  margin: 0;
  list-style: none;
  width: 100%;
}
.infinite-list .infinite-list-item {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 32px;
  line-height: 32px;
  &:nth-child(2n) {
    // background-color: #f8faff;
  }
}
.infinite-list .infinite-list-item + .list-item {
  margin-top: 10px;
}
.collapse-div {
  padding: 40px 0 0 20px;
  display: flex;
  .left {
    display: flex;
    flex-wrap: wrap;
    li {
      width: 100%;
      color: #607590;
      // font-size: 14px;
      font-weight: 400;
    }
    li:last-child {
      margin-bottom: 0;
    }
  }
  .earlyWarning {
    display: flex;
  }
  .right {
    display: flex;
    flex-wrap: wrap;
    li {
      width: 100%;
      margin-bottom: 20px;
      color: #333333;
      // font-size: 14px;
      font-weight: 400;
    }
  }
}
ul {
  list-style: none;
  li {
    // background: none !important;
    font-weight: 500 !important;
    // font-size: 14px !important;
    color: #333333 !important;
  }
}
:deep.demo-tabs .el-tabs__content {
  max-height: 800px;
  overflow: auto;
}
.fontSize {
  font-weight: 500;
  // font-size: 14px;
  color: #333333;
}
.hiddenText {
  padding: 15px 0 10px 0;
}
.fontSize2 {
  font-weight: 400;
  // font-size: 14px;
  color: #333333;
}

.top {
  .topData {
    margin-top: 17px;
    // height: calc(100% - 15px);
    // height: 190px;
    // overflow-y: auto;
    // border: 1px solid #eeeeee;
    .topTitle {
      width: 100%;
      background: #fafafa;
      line-height: 45px;
      height: 49px;
      display: flex;
      justify-content: space-around;
    }
    .topLi {
      // padding: 15px 0 15px 0;
      height: 150px;
      overflow-y: auto;
      display: flex;
      justify-content: space-around;
      li {
        // margin-bottom: 15px;
      }
    }
  }
}
:deep(.el-tabs__nav-scroll) {
  display: flex;
  justify-content: center;
}
:deep(.el-tabs__nav-wrap::after) {
  background: transparent;
}
.w-chart {
  width: 370px;
  height: 270px;
}
.tip {
  padding: 8px;
  color: #ff4d4f;
  border-radius: 4px 4px 4px 4px;
  background: rgba(255, 242, 242, 1);
}
</style>
