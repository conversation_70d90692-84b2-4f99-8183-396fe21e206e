<template>
  <div class="com-title-a">
    <div class="vertical"></div>
    <slot>{{ props.title }}</slot>
    <slot name="left-extra"></slot>
    <div class="ml-auto">
      <slot name="right"></slot>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
})
defineOptions({ name: 'ComTitleA' })
</script>

<style scoped lang="scss">
.com-title-a {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
  color: #242526;

  .vertical {
    margin-right: 8px;
    width: 3px;
    height: 16px;
    background: #527cff;
    border-radius: 2px 2px 2px 2px;
  }
}
</style>
