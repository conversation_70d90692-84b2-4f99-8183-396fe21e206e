<template>
  <div class="title">
    <div class="vertical"></div>
    {{ props.title }}
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
})
defineOptions({ name: 'HeadTitle1' })
</script>

<style scoped lang="scss">
.title {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
  color: #242526;

  .vertical {
    margin-right: 8px;
    width: 3px;
    height: 16px;
    background: #527cff;
    border-radius: 2px 2px 2px 2px;
  }
}
</style>
