<!--仪表盘-->
<template>
  <div v-if="!isEmpty" class="h-full w-full" ref="gaugeChartRef"></div>
  <Empty v-else />
</template>
<script setup lang="ts">
import { markRaw, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { sleep } from '@/common/utils'
import Empty from '@/components/empty/index.vue'
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver'

const props = defineProps({
  colors: {
    type: Array,
    default: () => ['#1CE598', '#02BF7F'],
  },
  echartsData: {
    type: Array,
    default: () => {
      return []
    },
  },
  axisLine: {
    type: Array,
    default: () => {
      return [
        [0, '#e00000'],
        [0.595, '#e00000'],
        [0.6, '#fff'],
        [0.795, '#ff6a00'],
        [0.8, '#fff'],
        [0.895, '#f7ba1e'],
        [0.9, '#fff'],
        [1, '#5EC364'],
      ]
    },
  },
})
const isEmpty = ref(false)
const gaugeChartRef = ref()
const gaugeChart = ref<any>(null)
const observer = ref<ResizeObserver>()

function initEcharts() {
  gaugeChart.value = markRaw(echarts.init(gaugeChartRef.value))
  const option = {
    series: [
      {
        name: '',
        type: 'gauge',
        min: 0,
        max: 100,
        radius: '100%',
        center: ['50%', '55%'],
        axisLine: {
          roundCap: true,
          // 坐标轴线
          lineStyle: {
            width: 10,
            height: 1,
            color: props.axisLine,
          },
        },
        axisTick: {
          // 坐标轴小标记
          length: 4,
          lineStyle: {
            color: '#C9CDD4',
          },
        },
        splitLine: {
          // 分隔线
          length: 8,
          lineStyle: {
            color: '#86909C',
            width: 1,
          },
        },
        axisLabel: {
          color: '#86909C',
          fontSize: 12,
        },
        detail: {
          formatter: '{value}分',
          fontSize: 20,
          offsetCenter: [0, '60%'],
        },
        anchor: {
          show: true,
          showAbove: true,
          size: 10,
          itemStyle: {
            borderColor: '#F53F3F',
            borderRadius: 10,
            borderWidth: 4,
          },
        },
        pointer: {
          icon: 'rect',
          width: 4,
          length: '40%',
          offsetCenter: [0, 0],
          itemStyle: {
            color: '#ff3040',
            borderRadius: 10,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowBlur: 8,
            shadowOffsetX: 2,
            shadowOffsetY: 4,
          },
        },
        data: props.echartsData,
      },
    ],
  }
  gaugeChart.value.setOption(option)
  observer.value = useEchartsResizeObserver(gaugeChart, gaugeChartRef).observer
}

function destroyEcharts() {
  if (gaugeChart.value) {
    gaugeChart.value.dispose()
    gaugeChart.value = null
  }
}

function getImgUrl() {
  return gaugeChart.value?.getDataURL({ type: 'png' })
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any[]) => {
      isEmpty.value = !val.length
      destroyEcharts()
      await sleep(500)
      if (!isEmpty.value && gaugeChartRef.value) initEcharts()
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
})

defineExpose({ getImgUrl })

defineOptions({ name: 'DashboardComp' })
</script>

<style module lang="scss"></style>
