<!--环形图-->
<template>
  <div v-if="!isEmpty" class="normal-pie w-full h-full" ref="radarChartRef"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { sleep } from '@/common/utils'
import Empty from '@/components/empty/index.vue'
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver'

const props = defineProps({
  echartsData: {
    type: Array,
    default: () => {
      return []
    },
  },
  indicator: {
    type: Array,
    default: () => {
      return []
    },
  },
})

const isEmpty = ref(false)
const radarChartRef = ref()
const myChart = ref<any>(null)
const observer = ref<ResizeObserver>()

function initEcharts(data: any[]) {
  if (myChart.value) destroyEcharts()
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(radarChartRef.value))
  const option = {
    tooltip: {
      show: true,
      trigger: 'item',
      confine: true,
    },
    radar: {
      indicator: props.indicator,
      triggerEvent: true,
      shape: 'circle',
      axisName: {
        color: '#000',
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
    },
    series: [
      {
        name: '',
        type: 'radar',
        symbolSize: 0,
        color: '#3AACFF',
        data,
      },
    ],
  }

  myChart.value.setOption(option)
  myChart.value.on('click', function (params: any) {
    console.log(params)
  })
  observer.value = useEchartsResizeObserver(myChart, radarChartRef).observer
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any[]) => {
      isEmpty.value = !val.length
      await sleep(500)
      if (!isEmpty.value && radarChartRef.value) initEcharts(val)
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect()
})

defineComponent({ name: 'RadarChartComp' })
</script>

<style scoped></style>
