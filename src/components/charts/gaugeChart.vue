<!--仪表盘-->
<template>
  <div v-if="!isEmpty" class="line-part w-full h-full" ref="gaugeChart"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { sleep } from '@/common/utils'
import Empty from '@/components/empty/index.vue'
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver'

const props = defineProps({
  echartsData: {
    type: Object,
    default: () => {
      return { data: [] }
    },
  },
  color: {
    type: String,
    default: () => {
      return 'rgba(31, 34, 37, 1)'
    },
  },
  graphicColor: {
    type: Array,
    default: () => {
      return ['#74A7FF', '#2273FF']
    },
  },
  //圈内字体大小
  detailFontSize: {
    type: Number,
    default: () => {
      return 18
    },
  },
  //粗细
  lineStyleWidth: {
    type: Number,
    default: () => {
      return 14
    },
  },
})

const isEmpty = ref(false)
const gaugeChart = ref()
const myChart = ref<any>(null)
const observer = ref<ResizeObserver>()

function initEcharts(echartsData: any) {
  if (myChart.value) destroyEcharts()
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(gaugeChart.value))
  const color = props.color
  const lineStyleWidth = props.lineStyleWidth
  const detailFontSize = props.detailFontSize
  const graphicColor: any[] = props.graphicColor || [[]]
  const data = echartsData.data || []
  const option = {
    series: [
      {
        type: 'gauge',
        startAngle: 90,
        endAngle: -270,
        pointer: {
          show: false,
        },
        progress: {
          show: true,
          overlap: false,
          roundCap: true,
          clip: false,
          itemStyle: {
            color: {
              type: 'linear',
              x: 1,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: graphicColor[0], // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: graphicColor[1], // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            },
          },
        },
        axisLine: {
          lineStyle: {
            width: lineStyleWidth,
          },
        },
        splitLine: {
          show: false,
          distance: 0,
          length: 10,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
          distance: 50,
        },
        data: data,
        title: {
          fontSize: 14,
        },
        detail: {
          fontSize: detailFontSize,
          color: color,
          formatter: '{value}%',
        },
      },
    ],
  }
  myChart.value.setOption(option)

  observer.value = useEchartsResizeObserver(myChart, gaugeChart).observer
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any) => {
      isEmpty.value = !val.data.length
      isEmpty.value = false
      await sleep(500)
      if (!isEmpty.value && gaugeChart.value) initEcharts(val)
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect()
})

defineComponent({ name: 'gaugeChart' })
</script>

<style scoped></style>
