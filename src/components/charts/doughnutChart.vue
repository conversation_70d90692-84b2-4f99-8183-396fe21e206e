<!--环形图-->
<template>
  <div v-if="!isEmpty" class="normal-pie w-full h-full" ref="doughnutChart"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import Empty from '@/components/empty/index.vue'
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver'
import { sleep } from '@/common/utils'
import * as echarts from 'echarts'
import { defineComponent, markRaw, onBeforeUnmount, onMounted, ref, watch } from 'vue'

defineComponent({ name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' })

const props = defineProps({
  echartsData: {
    type: Array,
    default: () => {
      return []
    },
  },
  color: {
    type: Array,
    default: () => {
      return ['#f55555', '#00BAFF', '#FF7F57', '#12C487', '#FFBE4C', '#18DDBF', '#429BFF', '#625EFF', '#D256F1']
    },
  },
  extra: {
    type: Object,
    default: () => {
      return { tooltip: {}, legend: {}, title: {}, series: {} }
    },
  },
  isShowLabel: {
    type: Boolean,
    default: false,
  },
  isNotshowName: {
    type: Boolean,
    default: false,
  },
})

const isEmpty = ref(false)
const doughnutChart = ref()
const myChart = ref<any>(null)
const observer = ref<ResizeObserver>()

function initEcharts(data: any[]) {
  if (data.length === 0) return
  if (myChart.value) destroyEcharts()
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(doughnutChart.value))
  const color: any[] = props.color.map((item) => {
    if (item instanceof Array) {
      return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: item[0] }, // 渐变色起始颜色
        { offset: 1, color: item[1] }, // 渐变色结束颜色
      ])
    } else {
      return item
    }
  })
  const option = {
    color: color,
    title: props.extra.title,
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,150,236,0.6)',
      formatter: '{b} : {c} ({d}%)',
      textStyle: {
        color: '#333',
      },
      ...props.extra?.tooltip,
    },
    legend: {
      // type: 'scroll',
      icon: 'circle',
      bottom: '10%',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 20,
      textStyle: {
        color: '#333',
        fontSize: '14',
      },
      backgroundColor: '#E7EDF6', // 设置图例背景色
      borderRadius: 10, // 设置图例的圆角
      padding: props.isShowLabel ? [10, 20, 10, 20] : [20, 20, 20, 20], // 设置图例的内边距
      ...props.extra?.legend,
    },
    series: [
      {
        name: '',
        type: 'pie',
        radius: props.extra?.series?.radius || ['30%', '50%'],
        center: props.extra?.series?.center || ['50%', '38%'],
        avoidLabelOverlap: props.isShowLabel,
        label: props.extra?.series?.label || {
          show: props.isShowLabel,
          formatter: props.isNotshowName ? '{c}' : '{b} {c}',
        },
        labelLine: props.extra?.series?.labelLine || {
          show: props.isShowLabel,
        },
        minAngle: props.extra?.series?.minAngle || null, //最小的扇区角度（0 ~ 360）
        data,
      },
    ],
  }

  myChart.value.setOption(option)
  observer.value = useEchartsResizeObserver(myChart, doughnutChart).observer
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any[]) => {
      // 空数组或数组内的value全为0时展示缺省
      isEmpty.value = !val.length || val.every((item) => item.value === 0)
      await sleep(500)
      if (!isEmpty.value && doughnutChart.value) initEcharts(val)
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect()
})
</script>

<style scoped></style>
