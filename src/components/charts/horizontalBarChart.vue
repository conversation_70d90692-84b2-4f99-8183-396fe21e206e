<!--仪表盘-->
<template>
  <div v-if="!isEmpty" class="w-full h-full" ref="horizontalBarRef"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { sleep } from '@/common/utils'
import Empty from '@/components/empty/index.vue'
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver'

const props = defineProps({
  echartsData: {
    type: Array,
    default: () => {
      return []
    },
  },
  graphicColor: {
    type: Array,
    default: () => {
      return ['#2B61DF', '#2796FD']
    },
  },
})

const isEmpty = ref(false)
const horizontalBarRef = ref()
const myChart = ref<any>(null)
const observer = ref<ResizeObserver>()

function initEcharts(data: any) {
  if (myChart.value) destroyEcharts()
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(horizontalBarRef.value))
  const graphicColor: any[] = props.graphicColor || [[]]
  const dataValue = data.map((item: any) => item.value)
  const max = Math.max(...dataValue)
  const maxData: number[] = dataValue.map(() => max)
  const option = {
    grid: {
      left: '5%',
      right: '5%',
      bottom: 0,
      top: '10',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'none',
      },
      formatter: function (params: any) {
        return (
          params[0].name +
          '<br/>' +
          "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#2796FD'></span>" +
          params[0].seriesName +
          Number(params[0].value.toFixed(4)).toLocaleString() +
          '<br/>'
        )
      },
    },
    xAxis: {
      type: 'value',
      minInterval: 1,
      axisLabel: {
        show: true,
        textStyle: {
          color: '#606366',
        },
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        axisLabel: {
          show: true,
          textStyle: {
            color: '#606366',
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#E0E0E6',
          },
        },
        data: data.map((item: any) => item.name),
      },
      {
        type: 'category',
        inverse: true,
        axisTick: 'none',
        axisLine: 'none',
        show: false,
        axisLabel: {
          color: '#333',
          fontSize: '16',
        },
        data: dataValue,
      },
    ],
    series: [
      {
        name: '',
        type: 'bar',
        zlevel: 1,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: graphicColor[0],
              },
              {
                offset: 1,
                color: graphicColor[1],
              },
            ]),
          },
        },
        barWidth: 10,
        data,
      },
      {
        name: '',
        type: 'bar',
        barWidth: 10,
        barGap: '-100%',
        data: maxData,
        itemStyle: {
          normal: {
            color: '#E5E9ED',
          },
        },
      },
    ],
  }
  myChart.value.setOption(option)

  observer.value = useEchartsResizeObserver(myChart, horizontalBarRef).observer
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any[]) => {
      isEmpty.value = !val.length
      await sleep(500)
      if (!isEmpty.value && horizontalBarRef.value) initEcharts(val)
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect()
})

defineComponent({ name: 'HorizontalBarChart' })
</script>

<style scoped></style>
