<template>
  <n-progress class="progressClass">
    <slot name="default" />
    <!--创建一个svg替换默认svg-->
    <svg class="absolute">
      <defs>
        <linearGradient id="gradient" x1="0" y1="0" x2="1" y2="0">
          <!--设置渐变-->
          <stop
            v-for="(item, i) in gradientColor"
            :key="i"
            :offset="item.offset"
            :style="{ 'stop-color': item.color }"
            stop-opacity="1"
          ></stop>
        </linearGradient>
      </defs>
    </svg>
  </n-progress>
</template>

<script setup lang="ts">
interface Props {
  gradientColor: {
    offset: string
    color: string
  }[]
}

withDefaults(defineProps<Props>(), {
  gradientColor: () => [
    {
      offset: '0%',
      color: '#14CF9B',
    },
    {
      offset: '100%',
      color: '#A4EE51',
    },
  ],
})

defineOptions({ name: 'ComProgress' })
</script>
<style scoped lang="scss">
// 找到第二个path并将他的stroke替换为我们创建的svg
.progressClass {
  position: relative;
  width: 146px;
  :deep(svg > g:nth-child(2) path) {
    stroke: url(#gradient) !important;
  }
}
</style>
