<!--折线图-->
<template>
  <div v-if="!isEmpty" class="line-part w-full h-full" ref="graphicChart"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import * as echarts from 'echarts'
import Empty from '@/components/empty/index.vue'
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver'

defineComponent({ name: 'LineChart' })

const props = defineProps({
  echartsData: {
    type: Object,
    default: () => {
      return { label: [], data: [] }
    },
  },
  color: {
    type: Array,
    default: () => {
      return []
    },
  },
  graphicColor: {
    type: Array,
    default: () => {
      return []
    },
  },
  extra: {
    type: Object,
    default: () => {
      return { smooth: false, yAxis: {}, maxLabel: 10, legend: {}, tooltip: {} }
    },
  },
})

const isEmpty = ref(false)
const graphicChart = ref()
const myChart = ref<any>(null)
const observer = ref<ResizeObserver>()

function initEcharts(echartsData: any) {
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(graphicChart.value))
  const data = echartsData.data || []
  const label = echartsData.label || []

  const option = {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      lineStyle: {
        color: '#459BF8',
      },
      bottom: 20,
      data: ['投诉数量'],
    },
    grid: {
      left: '4%',
      right: '8%',
      bottom: '18%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      axisTick: {
        show: false,
      },
      data: data,
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '投诉数量',
        type: 'line',
        stack: 'total',
        data: label,
      },
    ],
  }

  myChart.value.setOption(option)

  observer.value = useEchartsResizeObserver(myChart, graphicChart).observer
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

function getImgUrl() {
  return myChart.value?.getDataURL({ type: 'png' })
}

onMounted(() => {
  watch(
    () => props.echartsData,
    (val: any) => {
      destroyEcharts()
      initEcharts(val)
      // isEmpty.value = !val.length;
      // await sleep(200);
      // if (!isEmpty.value && graphicChart.value) initEcharts(val);
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect()
})

defineExpose({ getImgUrl })
</script>

<style scoped></style>
