<!--折线图-->
<template>
  <div v-if="!isEmpty" class="line-part w-full h-full" ref="graphicChart"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import * as echarts from 'echarts'
import Empty from '@/components/empty/index.vue'
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver'

defineComponent({ name: 'LineChart' })

const props = defineProps({
  echartsData: {
    type: Object,
    default: () => {
      return { name: [], dataFirst: [], dataSecond: [] }
    },
  },
  color: {
    type: Array,
    default: () => {
      return ['#399eff', '#4ee4ff']
    },
  },
  graphicColor: {
    type: Array,
    default: () => {
      return []
    },
  },
  extra: {
    type: Object,
    default: () => {
      return { smooth: false, yAxis: {}, maxLabel: 10, legend: {}, tooltip: {} }
    },
  },
})

const isEmpty = ref(false)
const graphicChart = ref()
const myChart = ref<any>(null)
const observer = ref<ResizeObserver>()

function initEcharts(echartsData: any) {
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(graphicChart.value))
  const color = props.color
  const dataFirst = echartsData.dataFirst || []
  const dataSecond = echartsData.dataSecond || []
  const label = echartsData.name || []

  const option = {
    color: color,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // Use axis to trigger tooltip
        type: 'shadow', // 'shadow' as default; can also be 'line' or 'shadow'
      },
    },
    legend: {
      bottom: '4%',
      itemWidth: 20,
      itemHeight: 10,
      itemGap: 20,
      textStyle: {
        color: '#333',
        fontSize: '14',
      },
    },
    grid: {
      left: '4%',
      right: '8%',
      bottom: '18%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: label,
    },
    series: [
      {
        name: '未处置',
        type: 'bar',
        stack: 'total',
        // label: {
        //   show: true,
        // },
        // emphasis: {
        //   focus: 'series',
        // },
        data: dataFirst,
      },
      {
        name: '已处置',
        type: 'bar',
        stack: 'total',
        // label: {
        //   show: true,
        // },
        // emphasis: {
        //   focus: 'series',
        // },
        data: dataSecond,
      },
    ],
  }

  myChart.value.setOption(option)

  observer.value = useEchartsResizeObserver(myChart, graphicChart).observer
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

function getImgUrl() {
  return myChart.value?.getDataURL({ type: 'png' })
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any) => {
      destroyEcharts()
      isEmpty.value = !val.name?.length
      // await sleep(200);
      if (!isEmpty.value && graphicChart.value) initEcharts(val)
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect()
})

defineExpose({ getImgUrl })
</script>

<style scoped></style>
