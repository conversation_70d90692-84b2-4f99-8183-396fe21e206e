<!--环形图-->
<template>
  <div v-if="!isEmpty" class="normal-pie w-full h-full" ref="rosePieRef"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { sleep } from '@/common/utils'
import Empty from '@/components/empty/index.vue'
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver'

const props = defineProps({
  echartsData: {
    type: Array,
    default: () => {
      return []
    },
  },
  color: {
    type: Array,
    default: () => {
      return ['#f55555', '#00BAFF', '#FF7F57', '#12C487', '#FFBE4C', '#18DDBF', '#429BFF', '#625EFF', '#D256F1']
    },
  },
  extra: {
    type: Object,
    default: () => {
      return { title: {}, series: {}, legend: {} }
    },
  },
})

const isEmpty = ref(false)
const rosePieRef = ref()
const myChart = ref<any>(null)
const observer = ref<ResizeObserver>()

function initEcharts(data: any[]) {
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(rosePieRef.value))
  const color: any[] = props.color.map((item) => {
    if (item instanceof Array) {
      return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: item[0] }, // 渐变色起始颜色
        { offset: 1, color: item[1] }, // 渐变色结束颜色
      ])
    } else {
      return item
    }
  })
  const option = {
    color: color,
    title: {
      ...props.extra?.title,
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}<br /> {c}家，{d}%',
      ...props.extra?.tooltip,
    },
    legend: {
      y: 'center',
      right: '5%',
      orient: 'vertical',
      icon: 'circle',
      itemWidth: 10,
      backgroundColor: '#E7EDF6',
      borderRadius: 10,
      padding: [20, 20, 20, 20],
      data: data.map((item) => item.name),
      ...props.extra?.legend,
    },
    calculable: true,
    series: [
      {
        name: '',
        type: 'pie',
        radius: ['50%', '75%'],
        center: ['30%', '50%'],
        roseType: 'radius',
        label: {
          show: false,
        },
        ...props.extra?.series,
        data,
      },
    ],
  }

  myChart.value.setOption(option)
  observer.value = useEchartsResizeObserver(myChart, rosePieRef).observer
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

function getImgUrl() {
  return myChart.value?.getDataURL({ type: 'png' })
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any[]) => {
      // 空数组或数组内的value全为0时展示缺省
      isEmpty.value = !val.length || val.every((item) => item.value === 0)
      destroyEcharts()
      await sleep(500)
      if (!isEmpty.value && rosePieRef.value) initEcharts(val)
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect()
})

defineExpose({ getImgUrl })

defineComponent({ name: 'RosePieChart' })
</script>

<style scoped></style>
