<!--热力图-->
<template>
  <div v-if="!isEmpty" class="grid-chart w-full h-full" ref="heatmapChart"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { sleep } from '@/common/utils'
import Empty from '@/components/empty/index.vue'
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver'

defineComponent({ name: 'HeatmapChart' })

const props = defineProps({
  echartsData: {
    type: Object,
    default: () => {
      return { hours: [], data: [] }
    },
  },
})

const isEmpty = ref(false)
const heatmapChart = ref()
const myChart = ref<any>(null)
const observer = ref<ResizeObserver>()

function initEcharts(item: any) {
  if (myChart.value) destroyEcharts()
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(heatmapChart.value))
  const hours = item.hours || []
  const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'].reverse()

  const data = item.data || []
  const nums = data.map((item: any[][]) => (typeof item[2] === 'number' ? item[2] : 0))
  const max = Math.max(...nums)

  const option = {
    tooltip: {
      position: 'top',
      backgroundColor: 'rgba(0,150,236,0.6)',
      textStyle: {
        color: '#333',
      },
      formatter: function (d: any) {
        return `${d.name}  ${d.value[2]}起`
      },
    },
    grid: {
      top: '60',
      bottom: '40',
      left: '50',
      right: '30',
    },
    xAxis: {
      type: 'category',
      splitArea: {
        show: true,
      },
      axisLabel: {
        fontSize: 10,
        color: '#6E7079',
      },
      data: hours,
    },
    yAxis: {
      type: 'category',
      splitArea: {
        show: true,
      },
      axisLabel: {
        fontSize: 10,
        color: '#6E7079',
      },
      data: days,
    },
    visualMap: {
      min: 0,
      max: max,
      calculable: true,
      orient: 'horizontal',
      right: '20',
      top: '5',
    },
    series: [
      {
        name: '',
        type: 'heatmap',
        data: data,
        label: {
          show: true,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  }
  myChart.value.setOption(option)
  observer.value = useEchartsResizeObserver(myChart, heatmapChart).observer
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any) => {
      isEmpty.value = !val.data.length
      await sleep(500)
      if (!isEmpty.value && heatmapChart.value) initEcharts(val)
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect()
})
</script>

<style scoped></style>
