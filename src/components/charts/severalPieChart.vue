<!--多个饼图-->
<template>
  <div v-if="!isEmpty" class="w-full h-full" ref="severalChart"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { sleep } from '@/common/utils'
import Empty from '@/components/empty/index.vue'
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver'

const props = defineProps({
  echartsData: {
    type: Array,
    default: () => {
      return []
    },
  },
  extra: {
    type: Object,
    default: () => {
      return { tooltip: {}, legend: {}, title: {}, series: {} }
    },
  },
})

const isEmpty = ref(false)
const severalChart = ref()
const myChart = ref<any>(null)
const observer = ref<ResizeObserver>()

function initEcharts() {
  if (myChart.value) destroyEcharts()
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(severalChart.value))
  const option = {
    title: props.extra.title,
    tooltip: {
      ...props.extra?.tooltip,
    },
    legend: {
      icon: 'circle',
      bottom: '10%',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 20,
      textStyle: {
        color: '#333',
        fontSize: '14',
      },
      backgroundColor: '#E7EDF6', // 设置图例背景色
      borderRadius: 10, // 设置图例的圆角
      ...props.extra?.legend,
    },
    series: props.extra?.series,
  }

  myChart.value.setOption(option)
  observer.value = useEchartsResizeObserver(myChart, severalChart).observer
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

function getImgUrl() {
  return myChart.value?.getDataURL({ type: 'png' })
}

onMounted(() => {
  watch(
    () => props.extra.series,
    async (val: any[]) => {
      isEmpty.value = !val.length
      await sleep(500)
      if (!isEmpty.value && severalChart.value) initEcharts()
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect()
})

defineExpose({ getImgUrl })

defineComponent({ name: 'SeveralPieChart' })
</script>

<style scoped></style>
