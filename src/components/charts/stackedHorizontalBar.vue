<!--堆叠横向柱状图-->
<template>
  <div v-if="!isEmpty" class="w-full h-full" ref="horizontalBarRef"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { sleep } from '@/common/utils'
import Empty from '@/components/empty/index.vue'
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver'

const props = defineProps({
  echartsData: {
    type: Object,
    default: () => {
      return { label: [], data: [] }
    },
  },
  color: {
    type: Array,
    default: () => {
      return []
    },
  },
})

const isEmpty = ref(false)
const horizontalBarRef = ref()
const myChart = ref<any>(null)
const observer = ref<ResizeObserver>()

function initEcharts() {
  if (myChart.value) destroyEcharts()
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(horizontalBarRef.value))
  const data = props.echartsData.data || []
  const label = props.echartsData.label || []
  const color = props.color || []
  const option = {
    legend: {
      icon: 'circle',
      itemWidth: 6,
      itemHeight: 6,
      itemGap: 26,
      data: data.map((item: any) => item.name),
      top: 16,
      right: 12,
      textStyle: {
        fontSize: 14,
        color: '#333',
      },
    },
    grid: {
      top: '40',
      left: '20',
      right: '20',
      bottom: '20',
      containLabel: true,
    },
    tooltip: {
      trigger: 'item',
      axisPointer: {
        type: 'none',
      },
    },
    xAxis: {
      type: 'value',
      minInterval: 1,
      axisLabel: {
        show: true,
        textStyle: {
          color: '#606366',
        },
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        axisLabel: {
          show: true,
          textStyle: {
            color: '#606366',
          },
          formatter: function (params: string) {
            let newParamsName = ''
            const paramsNameNumber = params.length
            const provideNumber = 12 // 超过12个字省略
            if (paramsNameNumber > provideNumber) {
              newParamsName = params.substring(0, provideNumber) + '...'
            } else {
              newParamsName = params
            }
            return newParamsName
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#E0E0E6',
          },
        },
        data: label,
      },
    ],
    series: data.map((item: any, index: number) => {
      return {
        name: item.name,
        type: 'bar',
        stack: 'total',
        label: {
          show: true,
          formatter: function (params: any) {
            // 值为0时不展示
            return params.value === 0 ? '' : params.value
          },
        },
        barMaxWidth: 36,
        emphasis: {
          focus: 'series',
        },
        itemStyle: {
          color: color[index],
        },
        data: item.value,
      }
    }),
  }
  myChart.value.setOption(option)

  observer.value = useEchartsResizeObserver(myChart, horizontalBarRef).observer
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

onMounted(() => {
  watch(
    () => props.echartsData.data,
    async (val: any[]) => {
      isEmpty.value = !val.length
      await sleep(500)
      if (!isEmpty.value && horizontalBarRef.value) initEcharts()
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect()
})

defineComponent({ name: 'HorizontalBarChart' })
</script>

<style scoped></style>
