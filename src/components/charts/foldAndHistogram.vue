<!--仪表盘-->
<template>
  <div v-if="!isEmpty" class="w-full h-full" ref="foldHorizontalRef"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { sleep } from '@/common/utils'
import Empty from '@/components/empty/index.vue'
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver'
import Line from './assets/line.png'

const props = defineProps({
  echartsData: {
    type: Object,
    default: () => {
      return { label: [], data: [] }
    },
  },
  graphicColor: {
    type: Array,
    default: () => {
      return [
        ['#2B61DF', '#41A3FF'],
        ['#09ACE2', '#59D4FD'],
      ]
    },
  },
})

const isEmpty = ref(false)
const foldHorizontalRef = ref()
const myChart = ref<any>(null)
const observer = ref<ResizeObserver>()

function initEcharts(data: any) {
  if (myChart.value) destroyEcharts()
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(foldHorizontalRef.value))
  const graphicColor: any[] = props.graphicColor || [[]]
  const option = {
    grid: {
      left: '5%',
      right: '5%',
      bottom: '2%',
      top: '12%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
    },
    legend: {
      data: [{ name: data.legend[0] }, { name: data.legend[1] }, { name: data.legend[2], icon: 'image://' + Line }],
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 26,
      top: 0,
      right: 0,
    },
    xAxis: [
      {
        type: 'category',
        data: data.label,
        axisPointer: {
          type: 'shadow',
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#6E7079',
        },
        axisLine: {
          lineStyle: {
            color: '#E0E0E6',
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        min: 0,
        minInterval: 1,
      },
      {
        type: 'value',
        name: '',
        position: 'right',
        min: 0,
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          show: true,
        },
      },
    ],
    series: [
      {
        name: data.legend[0],
        type: 'bar',
        data: data.data[0],
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: graphicColor[0][0],
              },
              {
                offset: 1,
                color: graphicColor[0][1],
              },
            ]),
          },
        },
      },
      {
        name: data.legend[1],
        type: 'bar',
        data: data.data[1],
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: graphicColor[1][0],
              },
              {
                offset: 1,
                color: graphicColor[1][1],
              },
            ]),
          },
        },
      },
      {
        name: data.legend[2],
        type: 'line',
        yAxisIndex: 1,
        icon: 'roundRect',
        itemHeight: 2,
        data: data.data[2],
        itemStyle: {
          color: '#F09B07',
        },
      },
    ],
  }
  myChart.value.setOption(option)

  observer.value = useEchartsResizeObserver(myChart, foldHorizontalRef).observer
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any) => {
      isEmpty.value = !val.label.length
      await sleep(500)
      if (!isEmpty.value && foldHorizontalRef.value) initEcharts(val)
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect()
})

defineComponent({ name: 'foldAndHistogramChart' })
</script>

<style scoped></style>
