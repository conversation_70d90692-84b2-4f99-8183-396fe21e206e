<template>
  <div :class="$style['score-eval-chart']">
    <div :class="$style['container']">
      <img :src="getImg(score)" alt="" />
      <div :class="$style['score']">
        <div class="text-[36px] font-[600] mt-[35px]">{{ score }}</div>
        <div class="mt-[4px]">{{ getDesc(score) }}</div>
      </div>
    </div>
    <div class="leading-[20px] mt-[16px] text-[#303133]" v-if="title">{{ title }}</div>
  </div>
</template>
<script setup lang="ts">
import icon1 from './assets/yx.png'
import icon2 from './assets/lh.png'
import icon3 from './assets/jg.png'
import icon4 from './assets/wx.png'

defineOptions({ name: 'ScoreEvalChart' })

withDefaults(defineProps<{ score: number; title?: string }>(), {
  score: 0,
  title: '',
})

const getImg = (val: number) => {
  if (val < 60) return icon4
  if (val < 80) return icon3
  if (val < 90) return icon2
  return icon1
}

const getDesc = (val: number) => {
  if (val < 60) return '危险'
  if (val < 80) return '及格'
  if (val < 90) return '良好'
  return '优秀'
}
</script>
<style module lang="scss">
.score-eval-chart {
  @apply text-center;
  .container {
    @apply w-[135px] h-[135px] relative text-[#343434];
    .score {
      @apply absolute h-full;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
</style>
