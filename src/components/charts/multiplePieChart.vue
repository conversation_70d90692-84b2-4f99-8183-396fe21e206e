<!--多个饼图-->
<template>
  <div v-if="!isEmpty" class="w-full h-full" ref="multipleChart"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { sleep } from '@/common/utils'
import Empty from '@/components/empty/index.vue'
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver'

const props = defineProps({
  echartsData: {
    type: Array,
    default: () => {
      return []
    },
  },
  color: {
    type: Array,
    default: () => {
      return ['#f55555', '#00BAFF', '#FF7F57', '#12C487', '#FFBE4C', '#18DDBF', '#429BFF', '#625EFF', '#D256F1']
    },
  },
  extra: {
    type: Object,
    default: () => {
      return { tooltip: {}, legend: {}, title: {}, series: {} }
    },
  },
  isShowLabel: {
    type: Boolean,
    default: false,
  },
})

const isEmpty = ref(false)
const multipleChart = ref()
const myChart = ref<any>(null)
const observer = ref<ResizeObserver>()

function initEcharts(data: any[]) {
  if (myChart.value) destroyEcharts()
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(multipleChart.value))
  const color: any[] = props.color.map((item) => {
    if (item instanceof Array) {
      return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: item[0] }, // 渐变色起始颜色
        { offset: 1, color: item[1] }, // 渐变色结束颜色
      ])
    } else {
      return item
    }
  })
  const option = {
    color: color,
    title: props.extra.title,
    tooltip: {
      ...props.extra?.tooltip,
    },
    legend: {
      // type: 'scroll',
      icon: 'circle',
      bottom: '10%',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 20,
      textStyle: {
        color: '#333',
        fontSize: '14',
      },
      backgroundColor: '#E7EDF6', // 设置图例背景色
      borderRadius: 10, // 设置图例的圆角
      ...props.extra?.legend,
    },
    dataset: {
      source: data,
    },
    series: props.extra?.series,
  }

  myChart.value.setOption(option)
  observer.value = useEchartsResizeObserver(myChart, multipleChart).observer
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

function getImgUrl() {
  return myChart.value?.getDataURL({ type: 'png' })
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any[]) => {
      isEmpty.value = !val.length
      await sleep(500)
      if (!isEmpty.value && multipleChart.value) initEcharts(val)
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect()
})

defineExpose({ getImgUrl })

defineComponent({ name: 'MultiplePieChart' })
</script>

<style scoped></style>
