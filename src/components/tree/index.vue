<template>
  <div class="org-tree">
    <titleTag title="所属单位" />
    <el-input
      v-model="filterText"
      style="width: 100%; margin: 0px 0px 15px; height: 40px"
      placeholder="请输入组织名称"
      :suffix-icon="Search"
      v-if="props.collapsed"
      clearable
    />
    <div class="tree-w overflow-hidden">
      <el-scrollbar>
        <div class="h-full">
          <el-tree
            ref="treeRef"
            :data="data"
            :props="{ children: 'children', label: 'text' }"
            @node-click="handleNodeClick"
            :highlight-current="true"
            default-expand-all
            node-key="id"
            :expand-on-click-node="true"
            :current-node-key="ui.unitId"
            :filter-node-method="filterNode"
            :render-content="renderContent"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node" :title="node.label">
                <span>{{ data.text }}</span>
              </div>
            </template>
            <template #empty>
              <div class="flex flex-col items-center justify-center h-full">
                <el-image :src="defaultPng"></el-image>
                <div class="not-description">暂无数据</div>
              </div>
            </template>
          </el-tree>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>
<script setup lang="ts">
import dept from '@/assets/image/dept.png'
import yewu from '@/assets/image/menu-child.png'
import jianguan from '@/assets/image/menu-father.png'
import titleTag from '@/components/TitleTag/titleTag.vue'
import { useUserInfo } from '@/store'
import defaultPng from '@/view/staging/assets/default.png'
import { Search } from '@element-plus/icons-vue'
import { ElTree } from 'element-plus'
import { onMounted, ref, watch } from 'vue'
import $API from '~/common/api'

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: true,
  },
  extraParams: {
    type: Object,
    default: () => ({}),
  },
})
const ui = useUserInfo()
const filterText = ref('')
const treeRef = ref<InstanceType<typeof ElTree>>()
const id = ref<string>(ui.value.unitId) // 树形结构id
const isAdd = ref<boolean>(false) // 是否新增
const orgCode = ref<string>(ui.value.unitId)
const orgName = ref<string>(ui.value.unitName)
const erecordUnitId = ref<string>(ui.value.erecordUnitId)
const data = ref<any>([]) // 树形结构数据
const emits = defineEmits(['serach'])

const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.text.includes(value)
}
const handleNodeClick = (data) => {
  orgCode.value = data.id
  id.value = data.id
  erecordUnitId.value = data.attributes.erecordUnitId
  let list = treeRef.value!.getCurrentNode()
  if (list.attributes.orgType === '1') {
    isAdd.value = true
  } else {
    isAdd.value = false
  }
  let treeInfo = {
    id: id.value,
    orgName: orgName.value,
    orgCode: orgCode.value,
    erecordUnitId: erecordUnitId.value,
    isAdd: isAdd.value,
  }
  emits('serach', treeInfo)
}
/**
 * 顶层为集团 则显示对应系统图标
 * 不为集团 为监管则显示监管 业务显示业务
 */
function getIcon() {
  return ui.value.zhLogoUrl + ui.value.zhLogo + '.png'
}
// 获取目录树
function getTree() {
  return new Promise((resolve, reject) => {
    const params = {
      orgCode: ui.value.unitId,
      needChildUnit: '1',
      needself: '',
      ...props.extraParams,
    }
    $API
      .get({
        url: 'atomic-upms-service/org/v1/getOrgTree',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 200) {
          if (res.data[0] && res.data[0].attributes.orgType === '1') isAdd.value = true
          let treeInfo = {
            orgName: orgName.value,
            orgCode: orgCode.value,
            erecordUnitId: erecordUnitId.value,
            isAdd: isAdd.value,
          }
          emits('serach', treeInfo)
          data.value = res.data
          resolve(res.data)
        }
      })
      .catch((err) => {
        reject(err)
      })
  })
}
function renderContent(h, { data }) {
  let prefixIcon = data.attributes.orgType === '2' ? jianguan : data.attributes.orgType === '1' ? yewu : dept
  if (data.parentId === '-1') prefixIcon = getIcon()

  return h('div', { class: 'w-0 flex-1 flex items-center pl-10px', title: data.text }, [
    h('img', {
      src: prefixIcon,
      class: 'w-[20px] h-[20px] inline-block mr-10px',
    }), // 自定义前缀图标
    h('span', { class: 'truncate' }, data.text), // 显示节点的标签
  ])
}

watch(filterText, (val) => {
  treeRef.value!.filter(val)
})

onMounted(() => {
  getTree()
})

// 创建计划
defineOptions({ name: 'trainingPlanIndex' })
</script>

<style scoped lang="scss">
.org-tree {
  // min-width: 310px;
  background-color: #eef6ff;
  // height: calc(100vh - 124px);
  height: 100%;
  overflow: hidden;

  .topUnit {
    height: 37px;

    .unit-name {
      cursor: pointer;
      font-family:
        Alibaba PuHuiTi 2,
        Alibaba PuHuiTi 20;
      font-weight: 400;
      font-size: 14px;
      // color: #222222;
    }
  }

  .tree-w {
    height: calc(100vh - 240px);
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }

  :deep(.el-tree) {
    height: 100%;
    background-color: rgba(238, 247, 255, 1) !important;

    .el-tree-node__content {
      margin-top: 4px;
      border-radius: 6px;
      height: 44px;
      padding: 5px 0;
      white-space: pre-line;

      .el-tree-node__label {
        font-size: 16px;
      }

      .el-tree-node__expand-icon {
        font-size: 15px;

        &:first-child {
          display: none;
        }

        svg {
          width: 15px;
          height: 15px;
        }
      }
    }

    .el-tree-node__children .el-tree-node__expand-icon:first-child {
      display: block;
    }
  }

  :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    background-color: rgba(82, 124, 255, 0.1);
    color: #527cff;
  }
}

:deep(.el-card.is-always-shadow) {
  box-shadow: none !important;
  border: none;
}

:deep(.el-scrollbar__view) {
  height: 100%;
}

:deep(.el-card__body) {
  @apply h-full;
  padding: 0;
}
</style>
