<template>
  <div id="map"></div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import { GisService } from './assets/GisServer.js'
import { useRoute } from 'vue-router'

const route = useRoute()

let pointList = [{ x: 13692157.028274, y: 4998209.931119, z: 0 }]
/**
 * 初始化gis
 */
function initGis(type: any) {
  GisService.init(type)
  if (window.lmsDisplay) {
    GisService.addPoint(pointList)
  }
}

onMounted(() => {
  setTimeout(() => {
    initGis(route?.query?.type)
  }, 1000)
})

watch(route, (value) => {
  if (value?.query?.type !== 'gisMain') {
    setTimeout(() => {
      pointList = [{ x: 13692157.028274, y: 4998209.931119, z: 0 }]
      initGis(undefined)
    }, 1000)
  }
})

/**
 * 获取矿场的列表（包涵经纬度）
 */
function getKCList() {
  pointList = [{ x: 13692157.028274, y: 4998209.931119, z: 0 }]
}

getKCList()

onBeforeUnmount(() => {
  GisService.destroy()
})
defineComponent({
  name: 'MisGis', // 首页Gis
})
</script>

<style scoped lang="scss">
#map {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}
</style>
