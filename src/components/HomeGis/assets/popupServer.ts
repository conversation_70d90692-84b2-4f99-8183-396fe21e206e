/**
 * popup服务
 */
// import { createApp, h } from 'vue'
// import { Subject } from 'rxjs'
// import Popup from './popup.vue'
// import { ISelectFireLXCountByAreaRes } from '@/types/src/ihome'

export const subKey = {
  TIME_CHANGE: 'time_change',
}

export interface IPopup {
  Show: (coordinate: number[]) => void
  Hide: () => void
}

class PopupService {
  private popupIns!: IPopup | null
  private tplVue: any
  private static _ins: PopupService | null
  public static getIns(lmsDisplay?: any) {
    if (!this._ins || !this._ins.popupIns) {
      this._ins = new PopupService(lmsDisplay)
    }
    return this._ins
  }
  // public subject$ = new Subject()
  constructor(lmsDisplay?: any) {
    const options_popup = {
      element: '<div id="gis-popup"></div>',
    }
    this.popupIns = lmsDisplay ? lmsDisplay.m_ThreeMap.getView().getPopupManager().Add(options_popup) : null
  }

  // show(coordinate: number[], data: ISelectFireLXCountByAreaRes, dateType: number) {
  //   this.close() // 销毁上一个popup
  //   const PopupComp = h(Popup, {
  //     dateType: dateType,
  //     tplData: data,
  //     handleClose: this.close.bind(this),
  //   })
  //   this.tplVue = createApp(PopupComp)
  //   this.tplVue.mount('#gis-popup')

  //   this.popupIns?.Show(coordinate)

  //   return this.popupIns;
  // }

  close() {
    this.tplVue?.unmount()
  }

  clear() {
    PopupService._ins = null
    this.popupIns = null
  }
}
export { PopupService }
