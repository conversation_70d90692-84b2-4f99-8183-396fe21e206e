/**
 * GIS服务
 */
import { PopupService } from './popupServer'
import router from '@/router'
import rotationBorder1URL from './rotationBorder1.png'
import rotationBorder2URL from './rotationBorder2.png'
import kc from './kc.png'
import popup from './popup.png'
import { useRoute } from 'vue-router'

export class GisService {
  static lmsDisplay: any = null
  static popupIns: PopupService | null = null
  static CONST_LMSDataOptions_base_Shi = {
    worker: true,
    // polar: 1.0964240914871268,
    polar: 0.4659920035561641, // 角度
    // azimuth: 0.0,
    azimuth: 0, // 角度
    expand: 0.7, // 缩放
    dplane_img: [rotationBorder1URL, rotationBorder2URL],
    dataType: 2,
    bgTransparent: true, // 背景透明(为true需关闭泛光)
    closeFog: true, //雾
    closeBloom: true, // 泛光
    closeGridPoint: true, // 网格
    closeCubePlane: false, //关闭扩散波纹
    closeUpParticles: false, //上升粒子
    closeBaseMap: true, // 基础底图
    interval: 1000,
  }

  /**
   * 地图初始化
   */
  static init(gisType: any) {
    CameraControls.install({ THREE })
    const route = useRoute()
    if (gisType === 'gisMain') {
      this.lmsDisplay = null
      window.lmsDisplay = this.lmsDisplay
      return
    }
    if (gisType === undefined) {
      this.lmsDisplay = null
      window.lmsDisplay = this.lmsDisplay
    }
    if (this.lmsDisplay === null) {
      this.lmsDisplay = new IndoorThree.LMSDisplay({
        target: 'map',
        worker: true,
        polar: 0.6677391040765235, // 角度
        azimuth: 0.1279888133425885, // 角度
        dplane_img: [rotationBorder1URL, rotationBorder2URL],
        baseMap: CONST_GeoData_China,
        baseMap_line: CONST_GeoData_China_line,
        userMap: CONST_GeoData_Item,
        userMap_inline: CONST_GeoData_Item_inline,
        userMap_outline: CONST_GeoData_Item_outline,
        dataType: 0,
        bgTransparent: true, // 背景透明(为true需关闭泛光)
        closeFog: true, //雾
        closeBloom: true, // 泛光
        closeGridPoint: true, // 网格
        closeCubePlane: false, //关闭扩散波纹
        closeUpParticles: false, //上升粒子
        closeBaseMap: true, // 基础底图
      })
    }

    window.lmsDisplay = this.lmsDisplay

    // 背景透明
    window.lmsDisplay.m_ThreeMap.getView().getRenderer().setClearAlpha(0)

    // 气泡
    // this.popupIns = PopupService.getIns(this.lmsDisplay);
    // this.popupIns.subject$.subscribe((data: any) => {
    //   this.dateType = data.data;
    //   this.getGisData();
    // });

    // hover事件
    this.lmsDisplay.onADZoneActived = (data: any, e: any) => {
      console.log('onADZoneActived', data, e)
    }

    // click事件
    this.lmsDisplay.onADZoneSelected = function (data: any, e: any) {
      console.log('onADZoneSelected', data, e)
    }
  }

  /**
   * 更新地图区域
   */
  static UpdateLMSMap(root: string, adminCode: string) {
    const lmsDataOptions = window.GetLMSDataOptionsByAdminCode(root, adminCode, this.CONST_LMSDataOptions_base_Shi)
    if (lmsDataOptions) {
      window.lmsDisplay.m_ThreeMap.getView().getPopupManager().Clear()
      this.lmsDisplay.LoadLMSMap(lmsDataOptions)
    } else {
      alert(adminCode + '无图形数据！')
    }
  }

  /**
   * 添加点
   */
  static addPoint(data: any) {
    for (const datum of data) {
      const style = GISShare.SMap.ThreeJS.LMSDisplay.CreateImageStyle({
        src: kc,
        size: [70, 70],
        depthTest: false,
      })
      const layer = this.lmsDisplay.CreateCustomizeVectorLayer({})
      this.lmsDisplay.AddPointToCustomizeVectorLayer(
        layer,
        { x: datum.x, y: datum.y, z: datum.z },
        'x',
        'y',
        'z',
        style
      )
      this.lmsDisplay.onMouseClick = async function (e: any) {
        const obj = window.lmsDisplay.GetGeoObjectByClientXY(layer, e.getX(), e.getY())
        if (obj) {
          await router.push({ path: '/fieldOver', query: { type: 'gisMain' } })
        }
      }

      const strHTML = `<div style="display: flex;
    text-align: center;
    justify-content: center;
    position: absolute;
    bottom: 88px;
    font-weight: 500;
    left: -65px;height: 47px; width: 129px; background: url(${popup}) no-repeat; background-size: 100% 100%;">
    <div style="font-size: 16px;
          color: #FFFFFF;
          line-height: 61px;
          background: linear-gradient(0deg, #0EC5EC 0%, #31BEFF 0%, #EFFCFE 58.7646484375%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;">
    齐大山选矿场
</div>
</div>`
      window.lmsDisplay
        .getMap()
        .getView()
        .getPopupManager()
        .Add({
          coordinate: [datum.x, datum.y, datum.z],
          element: strHTML,
          visible: true,
        })
    }
    // 刷新，不然标点挂载不出来
    window.lmsDisplay.Refresh()
  }

  static addPopup() {}

  /**
   * 点击gis地图获情数据
   */
  static dateType = 2

  static _bak: { areaCode: string; coordinate: undefined | number[] } = {
    areaCode: '',
    coordinate: undefined,
  }

  /**
   * 获取gis数据
   * @param areaCode
   * @param coordinate
   */
  static getGisData = (areaCode?: string, coordinate?: number[]) => {
    console.log('getGisData', areaCode, coordinate)
  }

  static destroy() {
    // this.popupIns?.clear();
    this.lmsDisplay?.Release()
  }
}
