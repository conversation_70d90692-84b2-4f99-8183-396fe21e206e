<template>
  <el-empty :class="$style['empty']" :theme-overrides="overrideEmptyTheme()">
    <template #icon>
      <img src="@/assets/image/no-data-new.png" alt="no data" />
    </template>
  </el-empty>
</template>

<script lang="ts" setup>
import { overrideEmptyTheme } from './emptyTheme'

defineOptions({ name: 'ComWidgetEmpty' })
</script>

<style module lang="scss">
.empty {
  @apply h-full justify-center;
}
</style>
