vue 复制代码
<template>
  <div v-loading="isLoading">
    <div :id="container" :style="{ width: width, height: height }"></div>
    <!-- 借助自己写的input读取图片 -->
    <input
      :id="'file' + container"
      class="quill-file"
      type="file"
      accept="image/png, image/gif, image/jpeg, image/bmp, image/x-icon"
      @change="onFileChange"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
// import Quill from 'vue-quill-editor'

const props = defineProps({
  id: {
    type: String,
    default: 'quillElement',
  },
  width: {
    type: String,
    default: '400px',
  },
  height: {
    type: String,
    default: '300px',
  },
  value: {
    type: String,
    default: '',
  },
  obj: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['onEditorChange'])

const container = ref(null)
const quill = ref(null)
const isLoading = ref(false)

const editorOption = reactive({
  theme: 'snow',
  placeholder: '请输入正文',
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ header: 1 }, { header: 2 }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ script: 'sub' }, { script: 'super' }],
      [{ indent: '-1' }, { indent: '+1' }],
      [{ direction: 'rtl' }],
      [{ size: ['small', false, 'large', 'huge'] }],
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      [{ color: [] }, { background: [] }],
      [{ font: [] }],
      [{ align: [] }],
      ['clean'],
      ['image'],
    ],
  },
})

const init = () => {
  container.value = props.id
  nextTick(() => {
    quill.value = new Quill('#' + container.value, editorOption) // 初始化编辑器
    let toolbar = quill.value.getModule('toolbar') // 获取工具栏模块
    // 对工具栏的 image 添加回调函数覆盖原本的方法
    toolbar.addHandler('image', () => {
      document.getElementById('file' + container.value).click()
    })
    quill.value.enable(false)
    if (props.value) {
      quill.value.pasteHTML(props.value)
    }
    quill.value.on('text-change', handler)
    quill.value.blur()
    quill.value.enable(true)
  })
}

const handler = () => {
  const html = document.querySelector('#' + container.value).children[0].innerHTML
  let text = quill.value.getText()
  emit('onEditorChange', html, props.obj, text)
}

const onFileChange = (event) => {
  let tar = event.target
  let file = tar.files[0]
  if (file.size > 1024 * 1024 * 2) {
    this.$message.error('图片大小不能超过2M')
    return false
  }
  let formData = new FormData() // 创建form对象
  formData.append('file', file, file.name)
  isLoading.value = true
  this.$axios
    .POSTFile({
      serviceName: 'service_url',
      apiUrl: 'file/upload',
      fb: formData,
    })
    .then((res) => {
      isLoading.value = false
      if (res.data.code === 'success') {
        // 将图片插入需要获取当前光标的位置，参数为是否要求焦点在编辑器上
        let selection = quill.value.getSelection(true)
        // 调用insertEmbed 将图片插入到编辑器
        quill.value.insertEmbed(selection.index, 'image', res.data.data.url)
        quill.value.setSelection(selection.index + 1)
      } else {
        this.$message.error(res.msg || '图片上传失败')
      }
    })
}

onMounted(() => {
  init()
})
</script>

<style scoped>
.quill-file {
  display: none;
}
</style>
