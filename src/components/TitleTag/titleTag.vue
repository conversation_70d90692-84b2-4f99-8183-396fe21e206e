<!--
 * @Author: jingjf <EMAIL>
 * @Date: 2024-07-10 15:04:52
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-16 20:35:22
 * @FilePath: \angang-edu-web\src\view\examBank-management\comp\titleTag.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="title2">
    <div class="border"></div>
    <div class="text">{{ props.title }}</div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
})
</script>

<style lang="scss" scoped>
.title2 {
  // width: 326px;
  height: 31px;
  // line-height: 51px;
  display: flex;
  align-items: center;
  // border-bottom: 1px solid #ebeef5;
  margin-bottom: 10px;
  // padding-bottom: 5px;

  .border {
    width: 18px;
    height: 12px;
    background: url('@/assets/image/drawer_bg.png') no-repeat;
    background-size: 100% 100%;
    border: none;
    margin-right: 10px;
    margin-top: 6px;
  }

  .text {
    height: 19px;
    // font-family:
    //   Alibaba PuHuiTi 2,
    //   Alibaba PuHuiTi 20;
    font-size: 16px;
    font-weight: 700;
    color: #242526;
  }
}
</style>
