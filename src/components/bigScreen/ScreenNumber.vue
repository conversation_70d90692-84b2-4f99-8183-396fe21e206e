<template>
    <div class="_flip_number">
        <div class="_flip_num">
            <div class="_fl_con">
                <span>鞍钢矿业安全生产天数</span>
                <span>2022-07-21起</span>
            </div>
            <FlipNumber :value="days"/>
        </div>
    </div>
    
</template>
<script setup lang="ts">
    const props = defineProps({
        days: {
            type: String,
            default: '003651'
        }
    })
</script>

<style scoped lang="scss">
    ._flip_number {
        ._flip_num {
            position: absolute;
            left: 50%;
            top: 14%;
            transform: translate(-50%, -50%);

            ._fl_con {
                display: flex;
                align-items: flex-end;
                justify-content: space-between;
                margin-bottom: 4px;

                span:nth-child(1) {
                    width: 100%;
                    height: 100%;
                    display: block;
                    font-weight: 600;
                    font-size: 22px;
                    font-synthesis: style;
                    font-style: italic;
                    color: #b4d9f3;
                    text-shadow: 3px 3px 5px rgba(0,0,0,0.25);
                    font-synthesis: style;
                    font-style: italic;
                }
                
                span:nth-child(2) {
                    font-size: 16px;
                    color: #fff;
                    width: 180px;
                }
            }
        }
    }
</style>
