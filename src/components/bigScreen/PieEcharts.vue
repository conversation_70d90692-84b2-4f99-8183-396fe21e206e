<template>
  <div class="line-part w-full h-full" ref="pieChart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, ref } from 'vue'
import * as echarts from 'echarts'

defineComponent({ name: 'pieChart' })
const props = defineProps({
  legend: {
    type: Object,
    default: () => {
      return {
        left: 50,
        top: 0,
        width: 100,
        icon: 'square',
        itemGap: 15,
        itemWidth: 25,
        itemHeight: 14,
        // backgroundColor: 'rgba(24, 62, 105, 1)',
      }
    },
  },
  series: {
    type: Object,
    default: () => {
      return {
        radius: [35, '60%'],
        center: ['70%', '40%'],
        warpRadius: ['60%', '61%'],
      }
    },
  },
  data: {
    type: Array,
    default: () => [
      '动火作业',
      '吊装作业',
      '动土作业',
      '高处作业',
      '盲板抽堵作业',
      '临时用电作业',
      '受限空间作业',
      '断路作业',
    ],
  },
  currentTab: {
    type: String,
    default: '1',
  },
})
const pieChart = ref()
const myChart = ref<any>(null)

function initEcharts() {
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(pieChart.value))
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b} {c} {d}%',
      backgroundColor: 'rgba(2, 13, 26, .8)',
      textStyle: {
        color: '#fff',
      },
    },
    legend: {
      left: props.legend.left,
      top: props.legend.top,
      width: props.legend.width,
      padding: 12,
      borderRadius: 4,
      itemGap: props.legend.itemGap,
      itemWidth: props.legend.itemWidth,
      itemHeight: props.legend.itemHeight,
      icon: props.legend.icon,
      backgroundColor: props.legend.backgroundColor,
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
      data: props.data,
    },
    series: [
      {
        name: 'Area Mode',
        type: 'pie',
        radius: props.series.radius,
        center: props.series.center,
        roseType: 'area',
        itemStyle: {
          borderRadius: 2,
          borderWidth: 2,
          borderColor: '#183E69', // 设置间隔的颜色
        },
        label: {
          show: false,
        },
        data: [
          {
            value: props.currentTab === '1' ? 30 : 50,
            name: props.data[0],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#1BC8E2' },
                  { offset: 1, color: '#008295' },
                ]),
              },
            },
          },
          {
            value: props.currentTab === '1' ? 28 : 45,
            name: props.data[1],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#EDB921' },
                  { offset: 1, color: '#B49600' },
                ]),
              },
            },
          },
          {
            value: props.currentTab === '1' ? 26 : 40,
            name: props.data[2],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#4179F7' },
                  { offset: 1, color: '#205CE3' },
                ]),
              },
            },
          },
          {
            value: props.currentTab === '1' ? 24 : 35,
            name: props.data[3],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#FF9829' },
                  { offset: 1, color: '#B05E02' },
                ]),
              },
            },
          },
          {
            value: props.currentTab === '1' ? 22 : 28,
            name: props.data[4],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#0D83E5' },
                  { offset: 1, color: '#0058AA' },
                ]),
              },
            },
          },
          {
            value: props.currentTab === '1' ? 20 : '',
            name: props.data[5],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#BCD1E7' },
                  { offset: 1, color: '#879AAF' },
                ]),
              },
              opacity: 0,
            },
          },
          {
            value: props.currentTab === '1' ? 18 : '',
            name: props.data[6],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#01C971' },
                  { offset: 1, color: '#01A058' },
                ]),
              },
              opacity: 0,
            },
          },
          {
            value: props.currentTab === '1' ? 16 : '',
            name: props.data[7],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#7F8FF1' },
                  { offset: 1, color: '#5061CD' },
                ]),
              },
              opacity: 0,
            },
          },
        ],
      },
      {
        name: '外圆',
        type: 'pie',
        radius: props.series.warpRadius, // 外圆大小设置
        center: props.series.center,
        label: {
          show: false, // 不显示标签
        },
        labelLine: {
          show: false, // 不显示标签线
        },
        data: [
          {
            value: 100,
            name: 'placeholder',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(76, 178, 255, 1)' },
                  { offset: 0.5, color: 'rgba(18, 62, 96, 1)' },
                  { offset: 1, color: 'rgba(76, 178, 255, 1)' },
                ]),
              },
            },
          }, // 使用占位数据
        ],
        tooltip: {
          show: false, // 不显示提示框
        },
      },
    ],
  }
  myChart.value.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart.value) myChart.value.resize()
  })
}

onMounted(() => {
  initEcharts()
})
</script>

<style scoped></style>
