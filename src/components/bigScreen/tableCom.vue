<template>
    <div class="science-other">
      <div class="data pl-[10px]">
        <div class="table-wrap">
          <div class="table-header">
            <div class="table-td">矿厂</div>
            <div class="table-td">已培训总次数</div>
            <div class="table-td">平均签到率</div>
            <div class="table-td">考试通过率</div>
          </div>
          <div class="table-body">
            <div class="table-th" v-for="(item, index) in rowList" :key="index">
              <div class="table-td">{{ item.name }}</div>
              <div class="table-td">{{ item.num }}</div>
              <div class="table-td">{{ item.num }}</div>
              <div class="table-td">22</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import {ref} from 'vue';
  import { defineComponent } from 'vue';
 let rowList = ref([
   {
    name: '东鞍山铁矿',
    num: 82,
   },
   {
    name: '眼前山井矿',
    num: 72,
   },
   {
    name: '齐大山选矿',
    num: 62,
   },
   {
    name: '齐大山矿场',
    num: 32,
   },
   {
    name: '大孤山球团厂',
    num: 12,
   },
   {
    name: '眼前山井矿',
    num: 12,
   },
   {
    name: '齐大山选矿',
    num: 12,
   },
   {
    name: '齐大山矿场',
    num: 12,
   },
 ])
  defineComponent({ name: 'ScienceOther' });
  </script>
  
  <style scoped lang="scss">
  .science-other {
    @apply w-full h-auto flex flex-col justify-between;
  
    .title {
      @apply w-full h-[57px];
      background: url('./assets/title.png') no-repeat center;
    }

    .data {
      @apply w-full h-[410px] mx-auto;
      //background: url('./assets/data-bg.png') no-repeat center;
      .table-wrap {
        height: 410px;
        .table-header {
          border-bottom: 1px solid #4699f1;
          background: linear-gradient(0deg, #022E5B 0%, #0B63AE 100%);
        }
        .table-body {
          height: 410px;
          overflow: auto;
          &::-webkit-scrollbar {
            width: 0;
            background: transparent;
          }
        }
        .table-header,
        .table-th {
          display: flex;
          height: 42px;
          line-height: 42px;
          text-align: center;
          .table-td {
            box-sizing: border-box;
            padding: 0 10px;
            text-align: center;
            white-space: nowrap;
            font-size: 12px;
            color: #e9eff4;
            &:nth-of-type(1) {
              margin-left: 5px;
              padding-left: 0;
            }
            $widthList: (
              1: 250,
              2: 300,
              3: 150,
              4: 150,
              5: 150,
              6: 150,
              7: 150,
              8: 150,
            );
            @each $key, $width in $widthList {
              &:nth-of-type(#{$key}) {
                width: $width + px;
              }
            }
          }
        }
        .table-th {
          &:nth-of-type(2n) {
            background: #0f2135;
          }
          .table-td {
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            &:nth-of-type(8) {
              span {
                color: #19aedd;
              }
            }
          }
        }
      }
    }
  }

  :deep(.el-progress__text){
    color: #fff;
  }
  </style>
  