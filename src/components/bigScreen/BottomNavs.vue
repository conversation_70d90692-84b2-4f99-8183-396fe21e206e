<template>
  <div class="_navs_bottom">
    <div class="_navs" v-for="(item, index) in routeList" :key="index" @click="jump(item)">
      <img class="_sygl" :src="item.src" />
      <div>{{ item.value }}</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router'
import _sygl_icon_active from '@/assets/image/bigScreen/_sygl_icon_active.png'
import _sygl_icon from '@/assets/image/bigScreen/_sygl_icon.png'
import _jypx_icon from '@/assets/image/bigScreen/_jypx_icon.png'
import _jypx_icon_active from '@/assets/image/bigScreen/_jypx_icon_active.png'
import _xgfgl_icon from '@/assets/image/bigScreen/_xgfgl_icon.png'
import _xgfgl_icon_active from '@/assets/image/bigScreen/_xgfgl_icon_active.png'
import _yhpczl_icon from '@/assets/image/bigScreen/_yhpczl_icon.png'
import _yhpczl_icon_active from '@/assets/image/bigScreen/_yhpczl_icon_active.png'
import _xczygl_icon from '@/assets/image/bigScreen/_xczygl_icon.png'
import _xczygl_icon_active from '@/assets/image/bigScreen/_xczygl_icon_active.png'

const router = useRouter()
defineProps({
  routeName: {
    type: String,
    default: 'index',
  },
  routeList: {
    type: <any>Array,
    default: () => {
      const routes = useRoute()
      return [
        {
          routeName: '/brainScreen',
          src: routes.path.includes('brainScreen') ? _sygl_icon_active : _sygl_icon,
          value: '首页概览',
        },
        {
          routeName: '/educationalTrain',
          src: routes.path.includes('educationalTrain') ? _jypx_icon_active : _jypx_icon,
          value: '教育培训',
        },
        {
          routeName: '/xgfScreen',
          src: routes.path.includes('xgfScreen') ? _xgfgl_icon_active : _xgfgl_icon,
          value: '相关方管理',
        },
        { routeName: '', src: routes.path.includes('cs') ? _yhpczl_icon_active : _yhpczl_icon, value: '隐患排查治理' },
        { routeName: '', src: routes.path.includes('cs') ? _xczygl_icon_active : _xczygl_icon, value: '现场作业管理' },
      ]
    },
  },
})

const jump = (item: any) => {
  router.push({
    path: item.routeName,
  })
}
</script>

<style scoped lang="scss">
._navs_bottom {
  z-index: 999;
  font-size: 14px;
  position: absolute;
  left: 51%;
  bottom: -1%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  color: #fff;

  ._navs {
    width: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;

    ._sygl {
      width: 80px;
      height: 80px;
    }
  }
}
</style>
