<template>
  <div class="line-part w-full h-full" ref="pieChart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, ref } from 'vue';
import * as echarts from 'echarts';

defineComponent({ name: 'pieChart' });
const props = defineProps({
  legend: {
    type: Object,
    default: () => {
      return {
        type: "plain",
        orient: 'horizontal',
        left: '5',
        top: 'bottom',
        itemGap: 30,
        textStyle: {
          color: '#fff',
          fontSize: 14
        },
        height: 100
      }
    },
  },
  series: {
    type: Object,
    default: () => {
      return {
        radius: [35, '60%'],
        center: ['70%', '40%'],
        warpRadius: ['60%', '61%'],
      };
    },
  },
  data: {
    type: Array,
    default: () => [
      { value: 310, legendname: '按期', name: "按期", itemStyle: { color: "#00a65c" } },
      { value: 135, legendname: '逾期', name: "逾期", itemStyle: { color: "#fd9b5d" } },
    ]
  },
  title: {
    type: Object,
    default: () => {
      return {
        text: 445,
        subtext: '已完成数'
      }
    },
  },
});
const pieChart = ref();
const myChart = ref<any>(null);

function initEcharts() {
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(pieChart.value));
  const option = {
    title: [{
        text: props.title.text + '起' ,
        subtext: props.title.subtext,
        textStyle: {
          fontSize: 20,
          color: "#fff"
        },
        subtextStyle: {
          fontSize: 16,
          color: 'fff'
        },
        textAlign: "center",
        x: '34.5%',
        y: '40%',
      }],
    tooltip: {
      axisPointer: {
        type: 'shadow',
      },
      textStyle: {
        color: '#fff',
      },
      trigger: 'item',
      backgroundColor: 'rgba(0,150,236,0.6)',
      formatter: function (parms) {
        let str = `${parms.seriesName}</br>${parms.marker}${parms.data.legendname}</br>
                  数量：${parms.data.value}</br>占比：${parms.percent}%`;
        return str;
      }
    },
    legend: props.legend,
    series: [
      {
        name: props.title.subtext,
        type: 'pie',
        center: ['35%', '50%'],
        radius: ['40%', '65%'],
        clockwise: false, //饼图的扇区是否是顺时针排布
        avoidLabelOverlap: false,
        label: {
          normal: {
            show: false,
            position: 'outter',
            formatter: function (parms) {
              return parms.data.legendname
            }
          }
        },
        labelLine: {
          normal: {
            length: 5,
            length2: 3,
            smooth: true,
          }
        },
        data: props.data
      }
    ]
  };
  myChart.value.setOption(option, true);
  window.addEventListener('resize', () => {
    if (myChart.value) myChart.value.resize();
  });
}

onMounted(() => {
  initEcharts();
});
</script>

<style scoped></style>
