<template>
  <div class="flip-number-container">
    <ul class="content">
      <li v-for="i of numArr" :key="i" ref="flipNumberRef" class="flip-number">
        <div class="upBox beforeTime screen_num" >0</div>
        <div class="downBox beforeTime screen_num">0</div>
        <div class="upBox afterTime screen_num">0</div>
        <div class="downBox afterTime screen_num">0</div>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, watch, ref, defineComponent, onMounted } from 'vue';

interface Props {
  value: string;
  bgColor?: string[];
}
const props = withDefaults(defineProps<Props>(), {
  value: '0',
  bgColor: () => ['#163b63', '#1f5890'],
});

let nums: string[] = String(props.value).split('');
let lastNums: string[] = [];
let numArr = Array(nums.length);
const flipNumberRef = ref<null | HTMLElement[]>(null);
const showNum = (num: string) => {
  nums = String(num).split('');
  numArr = Array(nums.length);
  nextTick(() => {
    nums.forEach((v, i) => {
      if (v !== lastNums[i]) {
        const numBox = flipNumberRef.value && flipNumberRef.value[i];
        const setSecondBox = numBox?.querySelectorAll('.afterTime');
        setSecondBox?.forEach((ele) => (ele.innerHTML = v));
        rotateUp(numBox, v);
      }
    });
    lastNums = nums;
  });
};

/**
 * 翻转前面下面的盒子向上180deg
 * @param numBox
 * @param val
 */
const rotateUp = (numBox: any, val: any) => {
  // numBox当前操作的数字卡片
  const ele = numBox.querySelectorAll('.beforeTime')[1]; // HTMLElement
  let rotateDeg = 0;
  ele.style.zIndex = '50';
  //这个是所有上面的盒子
  const allUpBox: any = numBox.querySelectorAll('.upBox');
  //所有前面的盒子
  const beforeTime = numBox.querySelectorAll('.beforeTime');
  // 让上面后面的盒子先不可见，然后设置为270°
  allUpBox[1].style.display = 'none';
  allUpBox[1].transform = `rotateX(270deg)`;
  const animation = () => {
    rotateDeg += 3;
    ele.style.transform = `perspective(160px) rotateX(${rotateDeg}deg)`;
    if (rotateDeg == 90) {
      //让它更新为最近时间后隐藏
      ele.innerHTML = val;
      ele.style.zIndex = '-1';
      //让刚刚上面隐藏的盒子重新显示出来并且完成90°-180°的旋转
      allUpBox[1].style.display = 'block';
      allUpBox[0].style.zIndex = '1';
      rotateDown(allUpBox[1]);
      allUpBox[1].style.zIndex = '1';
    }
    if (rotateDeg == 150) {
      beforeTime[0].innerHTML = val;
    }
    if (rotateDeg < 180) {
      requestAnimationFrame(animation);
    }
  };
  animation();
};

/**
 * @param ele
 */
const rotateDown = (ele: any) => {
  let rotateDeg = 270;
  const animation = () => {
    rotateDeg += 3;
    ele.style.transform = `perspective(500px) rotateX(${rotateDeg}deg)`;
    if (rotateDeg < 360) {
      requestAnimationFrame(animation);
    }
  };
  animation();
};

watch(
  () => props.value,
  (val) => {
    showNum(val);
  }
  // { immediate: true }
);

onMounted(() => {
  showNum(props.value);
});

defineComponent({
  name: 'MisHomeFlipNumberComp',
});
</script>
<style lang="scss" scoped>
.flip-number-container {
  @font-face {
    font-family: 'DS-Digital';
    src: url('../../assets/font/DS-DIGIB-2.TTF') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }
  .screen_num {
    color: #a3e5ff;
    font-family: 'DS-Digital', serif;
  }
  @apply text-white text-[72px] cursor-pointer;

  .content {
    @apply flex text-center;
    li {
      @apply relative w-[66px] h-[80px] leading-[80px] mr-[22px] bg-[#3b3d3b] rounded-[4px];

      &:last-child {
        @apply mr-0;
      }

      // border: 1px solid #00b4ff;

      .upBox,
      .downBox {
        @apply absolute left-0 right-0 overflow-hidden;
      }

      .upBox {
        @apply top-0 bottom-1/2 box-border origin-bottom rounded-t-[4px];
        background-color: v-bind('bgColor[0]');
        border-top: 1px solid #0499dd;
        border-left: 1px solid #0499dd;
        border-right: 1px solid #0499dd;
      }

      .downBox {
        @apply top-1/2 bottom-0 leading-[0] box-border overflow-hidden origin-top rounded-b-[4px];
        background-color: v-bind('bgColor[1]');
        border-bottom: 1px solid #0499dd;
        border-left: 1px solid #0499dd;
        border-right: 1px solid #0499dd;
      }
    }
  }
}
</style>
