<template>
  <div class="head_box h-56px">
    <div class="flex">
      <div
        v-for="(item, index) in changeTabs"
        :class="['head_tab', { w_active: current === item.value }, index == 0 ? 'w_font600' : '']"
        @click="changeTab(item)"
        :key="item.value"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps({
  changeTabs: {
    type: Array as any,
    default: () => [
      { label: '全部', value: '1' },
      { label: '第一', value: '2' },
    ],
  },
  type: {
    type: String,
    default: '0',
  },
})

const emit = defineEmits(['changeTab'])
const current = ref('1')

function changeTab(item: any) {
  current.value = item.value
  emit('changeTab', item)
}

watch(
  () => window.history.state.tab,
  (val = '1') => {
    current.value = val
  },
  { immediate: true }
)

watch(
  () => props.type,
  (val = '0') => {
    if (val === '1') {
      current.value = '0'
    }
  },
  { immediate: true }
)

defineOptions({ name: 'HeadTab' })
</script>

<style scoped lang="scss">
.head_box {
  font-weight: 400;
  font-size: 16px;
  background: linear-gradient(90deg, #e6ebf5 0%, #e0e7f7 100%);
  border-radius: 4px 4px 0 0;
  display: flex;
  padding: 0 24px;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;

  .head_tab {
    line-height: 54px;
    margin-right: 42px;
    color: #222222;
    font-weight: 400;

    &:hover {
      color: rgba(64, 112, 255, 1);
    }
  }

  .w_active {
    color: rgba(64, 112, 255, 1);
    border-bottom: 3px solid #4070ff;
  }
}
.w_font600 {
  font-weight: 600 !important;
}
</style>
