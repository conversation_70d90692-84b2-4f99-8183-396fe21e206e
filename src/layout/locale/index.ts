import defaultLocale from './default'
import tibetLocale from './tibet'
import { isTibetUnit } from '~/common/utils'

const translate = (locale) => {
  return (path) => {
    const paths = path.split('.')
    let val = locale
    for (let i = 0; i < paths.length; i++) {
      val = val[paths[i]]
    }

    return val
  }
}

export default function useLocale(unitId: string) {
  let _locale: any = defaultLocale
  if (isTibetUnit(unitId)) _locale = tibetLocale

  return {
    t: translate(_locale),
  }
}
