<template>
  <div class="">
    <breadcrumb-navigation :route="route" :router="router" :userInfo="ui" />
  </div>
</template>
<script setup lang="ts">
// import { useUserInfo } from '../store'

import { BreadcrumbNavigation as breadcrumbNavigation } from '@tanzerfe/tanzer-ui'
import { useUserInfo } from '~/store'
import { useRoute, useRouter } from 'vue-router'
import { watch } from 'vue'
const route = useRoute()
const router = useRouter()
const ui = useUserInfo()
watch(
  () => route.fullPath,
  () => {
    console.log('🚀 ~ route:', route)
  }
)
console.log('🚀 ~ sideMenu:', route)
</script>

<style scoped></style>
