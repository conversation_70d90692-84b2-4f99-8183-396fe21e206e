<template>
  <div class="h-full w-full global-header h-50px flex items-center justify-between">
    <div class="flex unit-name items-center mr-60px">
      <div class="img"></div>
      <span class="text-30px sys_title">中国外运长航资产安全管理平台</span>
    </div>
    <div class="flex items-center ml-60px">
      <div class="flex items-center w-160px justify-between mr-30px">
        <div class="flex items-center cursor-pointer" style="visibility: hidden">
          <svg-icon name="help" :size="26"></svg-icon>
          <span style="color: #607590">帮助</span>
        </div>
        <i class="block w-2px h-16px" style="background: rgba(234, 234, 234, 1); visibility: hidden"></i>
      </div>
      <div class="mr-8px message-box cursor-pointer" @click="goMessage">
        <svg-icon name="message" :size="28" color="#ffffff"></svg-icon>
        <!-- <div class="myborder" v-if="!!messageNum">
          {{ messageNum > 99 ? '99+' : messageNum }}
        </div> -->
      </div>
      <div class="user-info flex items-center" @click="showModifyPassword = true">
        <div class="img img-text">{{ userInitial }}</div>
        <span class="user-name">{{ ui.userName }}</span>
      </div>
      <div v-if="!isWYCH_LOCAL" class="exit flex items-center cursor-pointer" @click="exit" track>
        <span>退出</span>
      </div>
    </div>
  </div>
  <popup-side v-model="noticeShow" popupTitle="消息提醒" width="700px">
    <!-- <el-scrollbar>
      <div v-for="(item, index) in messageList" :key="index" class="content">
        <div class="img"></div>
        <div style="flex: 1">
          <div class="info">
            <div class="title">{{ item.messageTitle }}</div>
            <div class="time">接收时间：{{ item.pushTime }}</div>
          </div>
          <div class="text">
            {{ item.messageContent }}<span class="text-content">{{ item.strongMsgContent }}</span> ,请您尽快处理
          </div>
        </div>
      </div>
    </el-scrollbar>
    <div class="btn-page" v-if="total">
      <el-pagination layout="total, sizes, prev, pager, next, jumper" :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]" :total="total" v-model:current-page="curPage"
        @current-change="handleCurrentChange" @size-change="handleSizeChange">
      </el-pagination>
    </div> -->
    <div class="flex items-center justify-center h-full">
      <div class="flex flex-col items-center justify-center">
        <el-image :src="defaultPng"> </el-image>
        <div class="not-description text-[#969799] text-[14px] flex items-center justify-center mt-[10px]">
          暂无消息提醒
        </div>
      </div>
    </div>
  </popup-side>
</template>

<script lang="ts" setup>
import { computed, h, ref, provide, onMounted } from 'vue'
import { useUserInfo } from '@/store'
import defaultPng from '@/view/staging/assets/default.png'
import config from '@/config'
import { ElMessageBox } from 'element-plus'
import $API from '~/common/api'
import { useRouter } from 'vue-router'

const isWYCH_LOCAL = import.meta.env.VITE_WYCH_LOCAL === 'true'
const loginPath = import.meta.env.VITE_WYCH_LOCAL === 'true' ? '/nuc-login' : '/login'

const showPopover = ref(false)
provide('showMessage', showPopover)
const ui = useUserInfo()
const router = useRouter()
const messageNum = ref(0)
const noticeShow = ref(false)
const messageList = ref<any>([])

const userInitial = computed(() => {
  const name = ui.value.userName || ''
  return name.length >= 2 ? name.slice(-2) : name
})
const total = ref(0)
// const curPage = ref(1)
const pageSize = ref(10)
const pageNo = ref(1)

function handleSizeChange(val: number) {
  pageSize.value = val
  getTopNotice()
}
function handleCurrentChange(val: number) {
  pageNo.value = val
  getTopNotice()
}
function getNotReadNum() {
  // 获取未读消息数
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/queryMessageGroupMessageType`,
      params: {
        messageCategory: '4',
        messageClass: '2',
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        messageNum.value = res.data.length ? res.data[0]!.messageCount : 0
      }
    })
}

function getTopNotice() {
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/queryMessagePageList`,
      params: {
        messageCategory: '4',
        messageClass: '2',
        pageNo: 1,
        pageSize: 10,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        messageList.value = res.data.rows
        total.value = res.data.total
        pageNo.value = res.data.pageNo
        pageSize.value = res.data.pageSize
      }
    })
}

function goMessage() {
  noticeShow.value = true
  // getTopNotice()
}

function exit() {
  // const val = ui.value
  ElMessageBox({
    title: '提示',
    message: h('span', null, '确认退出登录吗?'),
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        $API
          .post({
            method: 'post',
            url: 'train-server/login/logOut',
            params: {
              client: 'WEB',
              orgCode: ui.value.orgCode,
              userId: ui.value.id,
              sysCode: ui.value.sysCode,
              userToken: ui.value.token,
            },
          })
          .then((res: any) => {
            instance.confirmButtonLoading = false
            instance.confirmButtonText = '确定'
            if (res && res.code === 'success') {
              ui.value = undefined as any
              // ElMessage.success('退出成功')
              sessionStorage.removeItem('@@web_userInfo')
              localStorage.removeItem('@@web_userInfo')
              done()
              router.replace(loginPath)
            }
          })
      } else {
        done()
      }
    },
  })
}

const showModifyPassword = ref(false)

onMounted(() => {
  // getNotReadNum()
})
</script>

<style lang="scss" scoped>
.global-header {
  background: rgba(37, 40, 67, 1);
  // padding-left: 60px;
  padding-right: 30px;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);

  svg {
    margin-right: 5px;
  }

  .sys_title {
    font-family: MyCustomFont, sans-serif;
    font-weight: 400;
    font-size: 30px;
    color: #ffffff;
    line-height: 29px;
    text-shadow: 0px 2px 8px rgba(41, 47, 58, 0.2016);
  }

  .message-badge {
    .el-badge__content {
      right: 22px;
    }
  }

  .notice {
    height: 36px;
    // line-height: 36px;
    padding: 0 20px;
    width: 50%;
    border-radius: 19px;
    background: #e7f4ff;
    box-sizing: border-box;
    width: 500px;
    // .el-carousel{
    //   line-height: 36px;
    // }
  }

  .unit-name {
    color: #333333;
    // border-left: 1px solid var(--el-border-color);
    height: 100%;
    padding-left: 20px;

    .img {
      width: 30px;
      height: 30px;
      background: url('@/assets/image/changhang.png') no-repeat;
      background-size: 100% 100%;
      margin-right: 10px;
    }
  }

  .exit {
    // background: rgba(236, 249, 254, 1);
    border-radius: 12px;
    padding: 0px 10px;
    height: 30px;
    color: #fd8595;
  }

  .message-box {
    position: relative;

    .myborder {
      position: absolute;
      right: -0px;
      top: -5px;
      width: 25px;
      height: 20px;
      // right: 5px;
      // top: 5px;
      // width: 5px;
      // height: 5px;
      border-radius: 100%;
      background: #f00;
      color: #ffffff;
      text-align: center;
      font-size: 12px;
      line-height: 20px;
    }
  }

  .user-info {
    margin-right: 20px;

    .user-name {
      color: #ffffff;
      margin-left: -5px;
    }

    .img {
      width: 40px;
      height: 40px;
      object-fit: cover;
      margin-right: 20px;
      border-radius: 50%;
      background-color: #527cff;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }
  }
}

.message_popover {
  width: 400px !important;
}
</style>
<style lang="scss" scoped>
.popup-side {
  .content {
    margin: 20px;
    display: flex;
    align-items: center;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #dfe6f2;
    margin-bottom: 10px;
    padding: 16px;

    .img {
      width: 36px;
      height: 36px;
      margin-right: 10px;
      background: url('@/assets/image/notice-list.png') no-repeat;
      background-size: 100% 100%;
    }

    .info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;

      .title {
        font-weight: 600;
        color: #222222;
        line-height: 20px;
        text-align: left;
        margin-bottom: 10px;
      }

      .time {
        font-weight: 400;
        color: #666666;
        line-height: 14px;
        text-align: right;
      }
    }

    .text {
      font-weight: 400;
      font-size: 14px;
      color: #222222;
      line-height: 20px;
      text-align: left;

      .text-content {
        color: rgba(82, 124, 255, 1);
      }
    }

    .item {
      font-weight: 400;
      font-size: 16px;
      color: #061032;
      line-height: 20px;
      margin-bottom: 12px;

      .title {
        color: rgba(82, 124, 255, 1);
      }
    }
  }

  .btn-page {
    width: 100%;
    // margin-top: 18px;
    padding-right: 20px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
