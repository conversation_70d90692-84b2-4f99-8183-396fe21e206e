<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-04 02:28:38
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-16 19:55:11
 * @FilePath: /angang-platform/src/layout/default.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="main">
    <div class="common-layout">
      <el-container>
        <el-header>
          <headerComponent></headerComponent>
        </el-header>
        <el-container class="main-page">
          <el-aside :width="isCollapse ? '48px' : '240px'">
            <sideMenu></sideMenu>
          </el-aside>
          <!--          面包屑导航 首页特殊处理-->
          <template v-if="router.fullPath !== '/staging'">
            <div class="flex-1 w-0 safety_bg">
              <div
                class="h-50px flex items-center pl-15px"
                v-if="!(router.path === '/noticeDetail' || router.path === '/issueReport')"
              >
                <breadcrumbNavigation></breadcrumbNavigation>
              </div>
              <el-main class="w-full">
                <div class="h-full w-full flex-1">
                  <router-view :key="router.fullPath"></router-view>
                </div>
              </el-main>
            </div>
          </template>
          <div v-else class="flex-1 w-0 safety_bg">
            <router-view :key="router.fullPath"></router-view>
          </div>
        </el-container>
      </el-container>
    </div>
  </div>

  <popup-wrap popup-title="" v-model="showMaybeAlarm">
    <div class="w-400px h-150px relative">
      <div class="flex items-center">
        <div class="img"></div>
        <div>{{ webSocketText.msgTitle }}</div>
      </div>
      <div class="text mt-8px ml-35px" v-if="webSocketText.msgContent">
        {{ webSocketText.msgContent
        }}<span class="text-content">{{ webSocketText.strongMsgContent }}</span> ,请您尽快处理
      </div>
      <div class="btn" v-if="webSocketText.routePath">去查看</div>
    </div>
  </popup-wrap>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import sideMenu from '@/layout/sideMenu/index.vue'
import headerComponent from '@/layout/headerComponent.vue'
import { useRoute } from 'vue-router'
import breadcrumbNavigation from './breadcrumbNavigation.vue'
import { useCounterStore, useUserInfo } from '@/store'
import { Socket, Message } from '@/types'
import SocketUtil from '@/common/socket'
import { ElNotification } from 'element-plus'

const router = useRoute()
console.log('🚀 ~ sideMenu:', router)

const counterStore = useCounterStore()
const ui = useUserInfo()
const isCollapse = computed(() => counterStore.isCollapse)

const showMaybeAlarm = ref<boolean>(false)
const webSocketText = ref<any>({})

// const socket = new SocketUtil({
//   topic: '/topic/' + ui.value.id,
//   headers: {
//     userId: ui.value.userId,
//     userName: ui.value.userName,
//     terminal: 'web',
//   },
// })

// socket.onmessage = handleMessage

// function handleMessage(body: any) {
//   console.log('🚀 ~ handleMessage:', body)
//   //通知对象类型 1-按人 2-按角色 3-按部门 空不发送
//   webSocketText.value = body
//   switch (body.objType) {
//     case '1':
//       if (body.objValue === ui.value.userId) showMaybeAlarm.value = true
//       break
//     case '2':
//       if (ui.value.roleIds.join(',').indexOf(body.objValue) > -1) showMaybeAlarm.value = true
//       break
//     default:
//       break
//   }
// }

// import ''
</script>

<style scoped lang="scss">
.main,
.common-layout {
  height: 100%;

  .main-page {
    .el-aside {
      --el-aside-width: auto;
      transition: all 0.3s;
    }
  }
}

.img {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  background: url('@/assets/image/notice.png') no-repeat;
  background-size: 100% 100%;
}

.text {
  font-weight: 400;
  font-size: 14px;
  color: #222222;
  line-height: 20px;
  text-align: left;

  .text-content {
    color: rgba(82, 124, 255, 1);
  }
}

.btn {
  width: 80px;
  height: 32px;
  background: #527cff;
  border-radius: 4px 4px 4px 4px;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
  position: absolute;
  bottom: 0;
  right: 0;
}

.el-header {
  padding: 0;
  height: auto;
}

.el-main {
  padding: 0 15px;
  height: calc(100vh - 105px);
}

.el-container {
  height: 100%;
  min-width: 1280px;
}

.safety_bg {
  background-color: rgba(200, 213, 255, 1);
}

:deep(.breadcrumb-navigation) {
  padding-left: 0px !important;
}

:deep(.breadcrumb-navigation .el-breadcrumb) {
  font-size: 16px;
}

:deep(.popup-wrap) {
  position: fixed;
  right: 10px;
  bottom: 20px;

  .sub-title {
    font-weight: 700;
    font-size: 14px;
    color: #303133;
  }
}

:deep(.popup-wrap header) {
  height: 30px;
}

:deep(.popup-wrap .content) {
  padding: 0 24px 17px;
  margin-top: -10px;
}
</style>
