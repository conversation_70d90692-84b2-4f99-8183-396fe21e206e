import { Injection<PERSON><PERSON>, provide, inject } from 'vue'
import { MenuProvider } from 'element-plus'

interface Resource {
  id: string
  resName: string
  resIcon: string
  resAlias: string
  resUrl: string
  resType: string
  children: Resource[]
}
interface MenuContextProps {
  handlMenuItemClick: (item: Resource) => void
  setRootMenu: (menu: MenuProvider) => void
}

const MenuContextKey: InjectionKey<MenuContextProps> = Symbol('menuContextKey')

export function useProvideMenu(props: MenuContextProps) {
  provide(MenuContextKey, props)
}

export function useMenuContext() {
  return inject(MenuContextKey)!
}
