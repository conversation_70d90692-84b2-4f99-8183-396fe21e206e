<template>
  <div class="flex h-full w-full side-menu flex-col items-center overflow-hidden">
    <!-- <div v-if="systemName && systemName.trim().length > 0" class="flex items-center logo-info cursor-pointer"
      @click="router.push(menuList[0].resType == '0' ? `${menuList[0].children[0].resUrl}` : menuList[0].resUrl)">
      <div class="title-icon" v-if="!isCollapse">
        <div class="title ml-10px">{{ systemName }}</div>
      </div>
    </div>

    <div v-else class="flex items-center logo-info2 cursor-pointer" @click="
      router.push(
        menuList[0].resType == '0'
          ? menuList[0].children.length != 0
            ? menuList[0].children[0].resUrl
            : menuList[0].resUrl
          : menuList[0].resUrl
      )
      ">
      <img :src="logo" class="" alt="" />
    </div> -->

    <div class="menu-wrap w-full" :class="isCollapse ? 'collapse' : 'notCollapse'">
      <el-menu :collapse="isCollapse" unique-opened ref="menu">
        <side-menu-item
          v-for="item in menuList"
          :menu-data="item"
          :key="item.id"
          :activeColor="activeColor"
          :defaultColor="defaultColor"
        ></side-menu-item>
      </el-menu>
    </div>
    <div
      class="toggle"
      :style="{
        top: '20px',
        left: counterStore.isCollapse ? '10px' : '20px',
      }"
      @click="setCollapse"
    >
      <svg-icon v-if="isCollapse" name="expand_1" :size="18" color="#ffffff"></svg-icon>
      <svg-icon v-else name="fold_1" :size="20" color="#ffffff"></svg-icon>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { toRaw, ref, watch, onMounted, computed, nextTick } from 'vue'
import { useUserInfo, useCounterStore } from '~/store'
import { useRouter, useRoute } from 'vue-router'
import { MenuProvider } from 'element-plus'
import { useProvideMenu } from './useMenuContext'
import useLocale from '../locale'
import sideMenuItem from './menuItem.vue'
import config from '@/config'
import icon from './assets/icon.png'
import $API from '@/common/api'
import defaultAvatar from '@/assets/image/default_avatar.png'
const ui = useUserInfo()

interface Resource {
  id: string
  resName: string
  resIcon: string
  resAlias: string
  resUrl: string
  resType: string
  children: Resource[]
}
interface MenuMap {
  [key: string]: Resource
}

const counterStore = useCounterStore()
const userInfo: any = useUserInfo()
const router = useRouter()
const route = useRoute()
const menu = ref()
const menuIdMap: MenuMap = {}
const menuPathMap: MenuMap = {}
let menuList = ref([] as Resource[])
let rootMenu: MenuProvider | null = null
const activeColor = 'rgba(64, 112, 255, 1)' // 选中状态颜色
const defaultColor = '#000000' // 默认颜色

const logo = computed(() => {
  const logo_url = userInfo.value.systemLogo
  if (logo_url) {
    return config.image_url + logo_url
  }

  return ''
})

const avatar = computed(() => {
  const photoUrl = ui.value.photoUrl
  if (photoUrl) {
    return config.image_url + photoUrl
  }
  return defaultAvatar
})

const systemName = computed(() => userInfo.value.systemName)
const isCollapse = computed(() => counterStore.isCollapse)
watch(route, handleRouteChange)

useProvideMenu({
  handlMenuItemClick: (item) => {
    router
      .push({
        path: item.resUrl,
      })
      .catch((err) => {
        window.location.reload()
      })
  },

  setRootMenu(menu) {
    rootMenu = menu
  },
})
function setCollapse() {
  counterStore.isCollapse = !counterStore.isCollapse
}
let menuIdArr: Resource[] = []
function menuToMap(mList: Resource[]) {
  mList.forEach((item) => {
    menuIdArr.push(item)
    menuIdMap[item.id] = item
    menuPathMap[item.resUrl] = item
    if (!item.children) item.children = []
    item.children.length > 0 && menuToMap(item.children)
  })
  localStorage.setItem('menuIdArr', JSON.stringify(menuIdArr))
  localStorage.setItem('menuIdMap', JSON.stringify(menuIdMap))
  localStorage.setItem('menuPathMap', JSON.stringify(menuPathMap))
  sessionStorage.setItem('menuIdArr', JSON.stringify(menuIdArr))
  sessionStorage.setItem('menuIdMap', JSON.stringify(menuIdMap))
  sessionStorage.setItem('menuPathMap', JSON.stringify(menuPathMap))
}

function handleRouteChange() {
  const { path } = route
  const paths = path.split('/')
  // parentPathName 父级路由名称不能传错,否则查询不到
  const { isChildComponents, parentPathName } = route.meta

  while (paths.length > 0) {
    const item = menuPathMap[paths.join('/')]

    if (item) {
      rootMenu!.activeIndex = item.id
      if (isChildComponents && parentPathName) {
        const curItem: any = item.children.find((child) => {
          return child.resUrl.includes(parentPathName as string)
        })
        if (curItem) rootMenu!.activeIndex = curItem.id
      }
      break
    } else {
      paths.pop()
    }
  }
}

const NINE_MENU_ID = '4da5c689986a4ca9b7fe196d1b289c04'

onMounted(async () => {
  const ui = userInfo.value
  const { t } = useLocale(ui.unitId)
  //sta 如果使用动态路由请放开下面代码
  const resourceList = Object.assign([], toRaw(ui.resourceList))
  //end

  //如果静态路由请放开下面代码
  // const resourceList = Object.assign([], toRaw(menuData))
  //静态路由
  const index = resourceList.findIndex((item: any) => item.id === NINE_MENU_ID)
  const item: Resource = resourceList[index]
  if (item) {
    item.resName = t('val.nineMenuName')
  }
  if (ui.hasSupervise !== '1') {
    if (index !== -1) resourceList.splice(index, 1)
  }

  menuList.value = resourceList
  // menuList.value = menuData;

  menuToMap(menuList.value)
  await nextTick()
  handleRouteChange()
})
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'MyCustomFont';
  src: url('../../assets/font/YouSheBiaoTiHei-Bold.TTF') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

$menuHeight: 48px;

.side-menu {
  background: rgba(37, 40, 67, 1);
  position: relative;

  .toggle {
    // background: #fff;
    color: #fff;
    position: absolute;
    transform: translate(0%, -50%);
    transition: all 0.5s;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .el-icon {
      width: 30px;
      height: 30px;

      svg {
        width: 100%;
        height: 100%;
      }
    }
  }

  .logo-info {
    height: 80px;
    box-sizing: border-box;
    color: white;

    .title {
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: MyCustomFont, sans-serif;
      font-weight: 400;
      font-size: 22px;
      color: #ffffff;
      line-height: 29px;
      text-shadow: 0 2px 8px rgba(41, 47, 58, 0.2016);
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }

  .logo-info2 {
    width: 100%;
    height: 80px;
    padding: 0 20px;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .menu-wrap {
    width: 100%;
    position: relative;
    // height: calc(100% - 140px);
    overflow-x: scroll;
    height: 100%;
    margin-top: 40px;

    &::-webkit-scrollbar {
      display: none;
    }

    .el-menu {
      background: rgba(37, 40, 67, 1);
      border: none;

      .el-sub-menu__title,
      .el-menu-item {
        color: white;
        opacity: 0.8;
        height: $menuHeight;

        &:hover {
          background: rgba(54, 61, 100, 1);
          opacity: 1;
        }
      }

      .el-sub-menu .el-menu-item {
        height: $menuHeight;
      }

      .el-menu-item.is-active {
        color: #fff;
        background: rgba(82, 120, 239, 1) !important;
        opacity: 1;
        // width: 95%;
        // border-radius: 5px;
        // margin-left: 5px;
      }
    }
  }

  .notCollapse {
    min-width: 300px;

    .el-icon.el-sub-menu__icon-arrow {
      right: 30px;
    }
  }

  .notCollapse {
    min-width: 208px;

    .el-icon.el-sub-menu__icon-arrow {
      right: 30px;
    }
  }

  .el-menu--collapse {
    width: 100%;

    .mue-btn {
      text-align: center;
      display: flex;
      padding: 0 10px !important;
    }
  }

  .collapse {
    // min-width: unset;
    // width: 40px;

    .el-sub-menu__title {
      text-align: center;
      display: flex;
      padding: 0 11px !important;
    }
  }
}

.title-icon {
  display: flex;
}

.user-info-bj {
  position: absolute;
  left: 6px;
  bottom: 6px;
  width: 228px;
  height: 202px;
  background: url('./assets/user-info-bj.png') no-repeat;
  background-size: 100% 100%;

  .top {
    display: flex;

    .user-name {
      display: flex;
      align-items: end;
      height: 50%;
      font-weight: 500;
      font-size: 16px;
      color: #2b2d33;
    }

    .login-name {
      display: flex;
      align-items: start;
      height: 50%;
      font-weight: 400;
      font-size: 14px;
      color: #2b2d33;
    }
  }

  .unit-name {
    padding-left: 12px;
    display: flex;
    align-items: start;
  }

  .de-pet-name {
    display: flex;
    align-items: start;
    font-weight: 400;
    font-size: 16px;
    color: #2b2d33;
    line-height: 20px;
  }

  .select_dept {
    // width: 200px;
    padding-top: 5px;

    :deep(.el-select) {
      .el-select__wrapper {
        background-color: transparent !important;
        box-shadow: none;
      }

      .el-select__placeholder {
        font-weight: 400;
        font-size: 16px;
        color: #2b2d33;
      }
    }

    :deep(.el-select__caret) {
      color: #00030a;
    }
  }
}

:deep(.el-sub-menu__title) {
  color: white;
  opacity: 0.8;
  height: $menuHeight;

  &:hover {
    background: rgba(54, 61, 100, 1);
    opacity: 1;
  }
}

:deep(.el-menu-item) {
  background: rgba(37, 40, 67, 1);
  color: white;

  &:hover {
    background: rgba(54, 61, 100, 1);
    opacity: 1;
  }

  &.is-active {
    color: #fff;
    background: rgba(82, 120, 239, 1) !important;
    opacity: 1;
  }
}

.collapse {
  :deep(.el-sub-menu__title) {
    text-align: center;
    display: flex;
    padding: 0 10px !important;
  }
}
</style>
