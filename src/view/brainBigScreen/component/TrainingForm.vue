<template>
    <div class="_emer">
        <TitleBar :title="dataObj.title"/>
        <div class="_emer_con">
            <img class="_yjyltj_bg" src="@/assets/image/bigScreen/_yjyltj_bg.png">
            <div class="_emer_left">
                <span class="_size">{{ dataObj.leftNum}}</span>
                <span>{{ dataObj.leftTitle }}</span>
            </div>
            <div class="_chartGre">
                <span class="_size">{{ dataObj.totalNum}}</span>
                <span>{{ dataObj.totalTitle }}</span>
            </div>
            <div class="_emer_right">
                <span class="_size">{{ dataObj.rightNum}}</span>
                <span>{{ dataObj.rightTitle }}</span>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
    import TitleBar from './TitleBar.vue';

    const props = defineProps({
        dataObj: {
            type: Object,
            default: () => {
                return {
                    title: '培训形式分布',
                    leftTitle: '线下培训',
                    leftNum: '235',
                    rightTitle: '线上培训',
                    rightNum: '221',
                    totalTitle: '总培训次数',
                    totalNum: '312',
                }
            }
        }
    })
</script>

<style scoped lang="scss">
    ._emer_con {
        color: #fff;
        position: relative;
        display: flex;
        justify-content: flex-end;
        font-size: 14px;

        ._yjyltj_bg {
            width: 418px;
            height: 160px;
        }

        ._emer_left {
            position: absolute;
            left: 60px;
            top: 55px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        ._size {
            font-size: 20px;
            font-weight: 600;
        }

        ._emer_right {
            position: absolute;
            right: 40px;
            top: 55px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        ._chartGre {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-43%, -17%);
            width: 160px;
            height: 160px;
        }
    }
</style>
