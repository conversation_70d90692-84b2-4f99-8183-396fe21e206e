<template>
    <div class="evolution_trends">
        <TitleBar title="年度培训计划类型"/>
        <PieEcharts :data="List" :legend="l" :series="s" />
    </div>
  </template>
  
  <script setup lang="ts">
  import PieEcharts from '@/components/bigScreen/PieEcharts.vue';
  import { defineComponent } from 'vue';
  import TitleBar from './TitleBar.vue';

  const List = ['岗位规程 3%', '事故案例 16%', '消防安全 22%', '安全生产 10%', '四新培训 33%'];
  const s = {
    radius: [35, '60%'],
    center: ['70%', '33%'],
    warpRadius: ['60%', '61%'],
  };
  const l = {
    left: 50,
    top: 10,
    width: 100,
    icon: 'square',
    itemGap: 15,
    itemWidth: 25,
    itemHeight: 14
  };
  defineComponent({ name: 'EvolutionComp' });
  </script>
  
  <style scoped lang="scss">
  .evolution_trends {
    height: 310px;
  }
  </style>
  