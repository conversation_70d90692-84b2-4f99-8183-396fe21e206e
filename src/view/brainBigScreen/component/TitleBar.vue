<template>
    <div>
        <div class="_yinhuan">
            <div class="_title">{{ title }}</div>
            <img class="_xczytj" src="@/assets/image/bigScreen/_xczytj_bg.png" />
            <div class="_tab" v-if="showTab">
                <div v-for="(item, index) in tab" :key="index" @click="clickTab(index)">
                    <span :class="tIndex === index ? '_s' : ''">{{ item }}</span>
                    <img class="_fx" v-if="tIndex === index" src="@/assets/image/bigScreen/_fx_bg_active.png" />
                    <img class="_fx" v-else src="@/assets/image/bigScreen/_fx_bg.png" />
                </div>
            </div>
            <div class="_select" v-if="showSelect">
                <el-select v-model="value" :teleported="false" style="width: 150px">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
    title: {
        type: String,
        default: '',
    },
    showTab: {
        type: Boolean,
        default: false,
    },
    showSelect: {
        type: Boolean,
        default: false,
    },
})

const tab = ref(['履职分析', '整改分析'])
const tIndex = ref(0)
const value = ref('1')
const options = ref([
    {
        value: '1',
        label: '东鞍山采矿场',
    },
])

const clickTab = (i: number) => {
    tIndex.value = i
}
</script>

<style scoped lang="scss">
._yinhuan {
    position: relative;
    color: #fff;

    ._title {
        font-family: sans-serif;
        font-synthesis: style;
        font-style: italic;
        font-size: 24px;
        text-shadow: 0 2px 8px rgba(41, 47, 58, .05);
        background: linear-gradient(180deg, #b4d9f3, #e9f8ff 60%, #b4d9f3);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: 600;
        position: absolute;
        top: -6px;
        left: 9%;
    }

    ._xczytj {
        width: 100%;
        height: 53px;
    }

    ._tab {
        display: flex;
        align-items: center;
        position: absolute;
        right: 2px;
        top: 1px;

        div {
            position: relative;
            font-size: 14px;
            width: 74px;
            height: 32px;
            color: #5c758d;
            cursor: pointer;

            span {
                position: absolute;
                top: 5px;
                left: 9px;
            }

            ._s {
                color: #fff;
            }

            img {
                width: 74px;
                height: 32px;
            }
        }
    }

    ._select {
        display: flex;
        align-items: center;
        position: absolute;
        right: 50px;
        top: 1px;
    }
}

/* 添加自定义类名以指定样式 */
:deep(.el-select__wrapper) {
    background-color: #062948; /* 修改背景色 */
    box-shadow: 0 0 0 1px #1b589e inset; /* 修改边框颜色 */
    --el-select-input-color: #fff;
}

:deep(.el-select__popper) {
    background-color: #062948 !important; /* 修改背景色 */
    border: 1px solid #062948; /* 修改边框颜色 */
}

:deep(.el-select__placeholder) {
    color: #fff;
}

:deep(.el-select__popper.el-popper[data-popper-placement^=bottom] .el-popper__arrow:before) {
    background-color: #062948;
    border-color: #062948;
}

:deep(.el-select__popper.el-popper[data-popper-placement^=left] .el-popper__arrow:before) {
    background-color: #062948;
    border-color: #062948;
}

:deep(.el-select-dropdown__item) {
    color: #fff;
}

:deep(.el-select-dropdown__item.is-hovering) {
    background-color: rgba(60, 181, 251, .2)
}
</style>
