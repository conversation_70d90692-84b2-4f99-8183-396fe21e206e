<template>
    <div>
        <TitleBar title="全员教育培训统计" />
        <div class="_yin_content">
            <div class="_yin_air">
                <img class="_pxrwzs" src="@/assets/image/bigScreen/_pxrwzs.png">
                <div class="_yin_statics">
                    <span>200</span>
                    <span>培训任务总数</span>
                </div>
            </div>
            <div class="_yin_air">
                <img class="_ywcrwzs" src="@/assets/image/bigScreen/_ywcrwzs.png">
                <div class="_yin_statics">
                    <span>200</span>
                    <span>完成任务总数</span>
                </div>
            </div>
            <div class="_yin_air">
                <img class="_yqwwc" src="@/assets/image/bigScreen/_yqwwc.png">
                <div class="_yin_statics">
                    <span>184</span>
                    <span>逾期未完成</span>
                </div>
            </div>
            <div class="_gre_con">
                <ProgressChart :bgimg="false" :pgsStyle="true" :echartsData="ecData" :showText="false" :borderColor="colorGress"/>
                <div class="_con_ge">
                    <span>{{ ecData }}<span>%</span></span>
                    <span>按期完成率</span>
                </div>
            </div>
        </div>
        <div class="_y_check">(按期完成率)</div>
        <div style="height: 180px;">
            <ComPaiMing :graphic-color="['rgba(43, 97, 223, 1)', 'rgba(39, 150, 253, 1)']" />
        </div>
    </div>
</template>
<script setup lang="ts">
    import { ref } from 'vue';
    import ComPaiMing from '@/components/bigScreen/PaiMing.vue';
    import TitleBar from './TitleBar.vue';

    const ecData = 76;
    const colorGress = ['rgba(153, 42, 116, 0.5)', 'rgba(153, 42, 116, 0.8)'];
</script>

<style scoped lang="scss">
    ._yin_content {
        width: 100%;
        color: #fff;
        display: flex;
        justify-content: space-around;
        margin-top: -20px;

        ._gre_con {
            display: flex;
            align-items: center;
            font-size: 12px;
            width: 120px;
            height: 120px;
            position: relative;
            margin-left: -20px;
            margin-right: 35px;

            ._con_ge {
                position: absolute;
                right: -50px;
                top: 35px;
                width: 70px;
                display: flex;
                flex-direction: column;

                span:nth-child(1) {
                    font-size: 20px;
                    font-weight: 600;
                }
            }
        }
        
        ._yin_air {
            display: flex;
            align-items: center;
            margin-left: 10px;

            img {
                width: 69px;
                height: 69px;
            }

            ._yin_statics {
                font-size: 12px;
                width: 50px;
                display: flex;
                flex-direction: column;

                span:nth-child(1) {
                    font-weight: 800;
                    font-size: 16px;
                }
            }
        }
    }

    ._y_check {
        color: #96d4ec;
        font-size: 14px;
        text-align: right;
        margin-top: -20px;
    }
</style>
