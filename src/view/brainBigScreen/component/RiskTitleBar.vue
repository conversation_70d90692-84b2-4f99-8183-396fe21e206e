<template>
  <div>
    <div class="_risk">
      <div class="_title">风险点总体分布</div>
      <img class="_xczytj" src="@/assets/image/bigScreen/_risk_point_bg.png" />
      <div class="_tab">更多></div>
      <div class="_risk_block">
        <div class="_r_b_item" v-for="(item, index) in riskList" :key="index">
          <img class="_risk_imgs" :src="item.src" />
          <div class="_risk_num">
            <span>{{ item.num }}</span>
            <span>{{ item.name }}</span>
          </div>
        </div>
      </div>
      <img class="_demo_image" src="@/assets/image/bigScreen/_fly_img.png" />
    </div>
  </div>
</template>
<script setup lang="ts">
import _fx from '@/assets/image/bigScreen/_fx.png'
import _shebei from '@/assets/image/bigScreen/_shebei.png'
import _bw from '@/assets/image/bigScreen/_bw.png'
import _zy from '@/assets/image/bigScreen/_zy.png'
import _zd from '@/assets/image/bigScreen/_zd.png'
import _jd from '@/assets/image/bigScreen/_jd.png'
import _yb from '@/assets/image/bigScreen/_yb.png'
import _dfx from '@/assets/image/bigScreen/_dfx.png'
import { ref } from 'vue'

const riskList = ref([
  { name: '风险点总数', num: '21', src: _fx },
  { name: '设备设施', num: '13', src: _shebei },
  { name: '部位场所', num: '12', src: _bw },
  { name: '作业活动', num: '5', src: _zy },
  { name: '重大风险', num: '31', src: _zd },
  { name: '较大风险', num: '11', src: _jd },
  { name: '一般风险', num: '5', src: _yb },
  { name: '低风险', num: '8', src: _dfx },
])
</script>

<style scoped lang="scss">
._risk {
  position: absolute;
  left: 52%;
  top: 60%;
  transform: translate(-50%, -50%);
  color: #fff;

  ._title {
    font-family: sans-serif;
    font-size: 24px;
    text-shadow: 0 2px 8px rgba(41, 47, 58, 0.05);
    background: linear-gradient(180deg, #bbe7fe, #e9f8ff 60%, #f8fdff);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: absolute;
    top: 5px;
    left: 9%;
  }

  ._xczytj {
    width: 787;
    height: 56px;
  }

  ._tab {
    position: absolute;
    right: 27%;
    top: 21px;
    color: #0cedf5;
  }

  ._risk_block {
    display: flex;
    flex-wrap: wrap;
    padding-left: 38px;

    ._r_b_item {
      width: 168px;
      height: 74px;
      position: relative;
      margin: 10px;

      img {
        width: 100%;
        height: 100%;
      }

      ._risk_num {
        width: 100%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 16px;

        span:nth-child(1) {
          font-weight: 600;
          font-size: 24px;
        }
      }
    }
  }

  ._demo_image {
    width: 866px;
    height: 550px;
  }
}
</style>
