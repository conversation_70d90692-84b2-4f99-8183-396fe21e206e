<template>
  <div class="_emer">
    <TitleBar title="应急演练统计" />
    <div class="_emer_con">
      <img class="_yjyltj_bg" src="@/assets/image/bigScreen/_yjyltj_bg.png" />
      <div class="_emer_left">
        <span class="_size">230</span>
        <span>应急演练计划</span>
      </div>
      <div class="_chartGre">
        <ProgressChart
          :bgimg="false"
          :pgsStyle="true"
          :echartsData="74"
          :borderColor="colorGress"
          :titleValueLeft="p.titleValueLeft"
          :titleValueTop="p.titleValueTop"
          :titleTextLeft="p.titleTextLeft"
          :titleTextTop="p.titleTextTop"
        />
        <span class="_ch_con">完成率</span>
      </div>
      <div class="_emer_right">
        <span class="_size">242</span>
        <span>实际演练计划</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import TitleBar from './TitleBar.vue'
import ProgressChart from '@/components/bigScreen/ProgressChart.vue'

const p = {
  titleValueLeft: '32%',
  titleValueTop: '40%',
  titleTextLeft: '53%',
  titleTextTop: '44%',
}
const colorGress = ['rgba(176,232,195, 0.5)', 'rgba(176,232,195, 0.8)']
</script>

<style scoped lang="scss">
._emer_con {
  color: #fff;
  position: relative;
  display: flex;
  justify-content: flex-end;
  font-size: 14px;
  margin-top: -6px;

  ._yjyltj_bg {
    width: 438px;
    height: 170px;
  }

  ._emer_left {
    position: absolute;
    left: 84px;
    top: 58px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  ._size {
    font-size: 20px;
    font-weight: 600;
  }

  ._emer_right {
    position: absolute;
    right: 24px;
    top: 58px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  ._chartGre {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-32%, -49%);
    width: 160px;
    height: 160px;

    ._ch_con {
      position: absolute;
      bottom: 51px;
      left: 58px;
    }
  }
}
</style>
