<template>
    <div class="evolution_trends">
      <TitleBar title="事故管理统计"/>
        <div class="_fi_con">
          <div class="_con" v-for="(item, index) in tList" :key="index">
            <div>
              <span>{{ item.num }}</span>
              <span>{{ item.value }}</span>
            </div>
            <img class="_gltj" v-if="index === 0" src="@/assets/image/bigScreen/_gltj_bg_another.png">
            <img class="_gltj" v-else src="@/assets/image/bigScreen/_gltj_bg.png">
          </div>
        </div>
      <div class="charts_value" ref="barCharts"></div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { defineComponent, ref, onMounted, markRaw } from 'vue';
  import TitleBar from './TitleBar.vue';
  import * as echarts from 'echarts';
  
  const barCharts = ref();
  const myChart = ref<any>(null);
  const tList = [
    { num: 235, value: '未遂事件' },
    { num: 17, value: '人身伤害类' },
    { num: 89, value: '财产损失类' },
    { num: 26, value: '环保类' },
    { num: 37, value: '其他' },
  ]

  function drawCharts() {
    myChart.value = markRaw(echarts.init(barCharts.value));
    const option = {
      // grid: {
      //   left: '50',
      //   right: '50',
      // },
      // legend: {
      //   textStyle: {
      //     color: '#fff',
      //   }
      // },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        textStyle: {
          color: '#fff',
        }
      },
      yAxis: [
        {
          type: 'value',
          textStyle: {
            color: '#fff',
          }
        },
        {
          type: 'value',
          textStyle: {
            color: '#fff',
          }
        },
      ],
      series: [
        {
          name: '警情总数（起）',
          type: 'bar',
          barWidth: 22,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#15a9e1' },
              { offset: 0.5, color: '#138dd1' },
              { offset: 1, color: '#138dd1' },
            ]),
          },
          data: [35, 75, 33, 66, 40, 65, 80, 55, 76, 19, 23, 44],
          z: 2,
        },
        {
          z: 2,
          name: '',
          type: 'pictorialBar',
          data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
          symbol: 'diamond',
          symbolOffset: [0, '50%'],
          symbolRotate: 90,
          symbolSize: [12, 22],
          itemStyle: {
            color: '#188df0',
          },
        },
        {
          z: 3,
          name: '警情总数（起）',
          type: 'pictorialBar',
          // 柱子顶部
          symbolPosition: 'end',
          data: [35, 75, 33, 66, 40, 65, 80, 55, 76, 19, 23, 44],
          symbol: 'diamond',
          symbolOffset: [0, -27],
          symbolRotate: 90,
          symbolSize: [12, 23],
          itemStyle: {
            normal: {
              borderWidth: 20,
              borderColor: '#176FBA',
              color: '#000',
            },
          },
          tooltip: {
            show: false,
          },
        },
        {
          name: '环比',
          type: 'line',
          yAxisIndex: 1,
          itemStyle: {
            normal: {
              color: '#d06c68',
              borderColor: '#fff',
              borderWidth: 2,
            },
          },
          symbol: 'diamond', // 设置拐点为菱形
          symbolSize: 11,
          // data: [45, 67, 34, 28, 76, 19, 23, 43, 99, 100, 104, 108],
        },
        {
          name: '同比',
          type: 'line',
          yAxisIndex: 1,
          itemStyle: {
            normal: {
              color: '#d4d545',
              borderColor: '#fff',
              borderWidth: 2,
            },
          },
          symbol: 'diamond', // 设置拐点为菱形
          symbolSize: 11,
          // data: [38, 45, 65, 23, 56, 67, 88, 54, 67, 34, 28, 76],
        },
      ],
    };
    myChart.value.setOption(option);
    window.addEventListener('resize', () => {
      if (myChart.value) myChart.value.resize();
    });
  }
  onMounted(() => {
    drawCharts();
  });
  defineComponent({ name: 'EvolutionTrendsComp' });
  </script>
  
  <style scoped lang="scss">
  .evolution_trends {
    width: 100%;
    
    .charts_value {
      width: 570px;
      height: 280px;
    }

    ._fi_con {
      color: #fff;
      display: flex;
      align-items: center;
      padding-left: 20px;
      margin-bottom: -40px;
      margin-top: 5px;
      
      ._con {
        position: relative;
        margin-left: 5px;

        ._gltj {
          width: 95px;
          height: 67px;
        }
        

        div {
          width: 100%;
          position: absolute;
          top: -10px;
          left: 50%;
          transform: translate(-50%, 0);
          display: flex;
          flex-direction: column;
          align-items: center;
          font-size: 14px;

          span:nth-child(1) {
            font-weight: 600;
            font-size: 16px;
          }
        }
      }
    }
  }
  </style>
  