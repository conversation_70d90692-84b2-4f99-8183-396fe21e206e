<template>
    <div>
        <TitleBar title="培训计划落实情况" />
        <div class="_con_pie">
            <div><PieEchartsBasics /></div>
            <div><PieEchartsBasics :title="titleObj" :data="data"/></div>
        </div>
    </div>
</template>
<script setup lang="ts">
    import PieEchartsBasics from '@/components/bigScreen/PieEchartsBasics.vue';
    import TitleBar from './TitleBar.vue';

    const titleObj = {
        text: 158,
        subtext: '未完成数'
    }
    const data = [
      { value: 35, legendname: '按期', name: "按期", itemStyle: { color: "#00a65c" } },
      { value: 123, legendname: '逾期', name: "逾期", itemStyle: { color: "#fd9b5d" } },
    ]
</script>

<style scoped lang="scss">
    ._con_pie {
        display: flex;
        margin-top: -20px;
        margin-bottom: 25px;
    
        div {
            height: 250px;
            width: 260px;
            // background-color: #fff;
            padding-left: 20px;
        }
    }
</style>
