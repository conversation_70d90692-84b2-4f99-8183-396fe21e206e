<template>
  <div class="evolution_trends">
    <TitleBar title="证书管理统计" />
    <div class="_fi_con">
      <div style="width: 120px; height: 120px">
        <InstrumentPanel />
      </div>
      <div class="_con" v-for="(item, index) in tList" :key="index">
        <div>
          <span>{{ item.num }}</span>
          <span>{{ item.value }}</span>
        </div>
        <img class="_gltj" src="@/assets/image/bigScreen/_gltj_bg.png" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import InstrumentPanel from '@/components/bigScreen/InstrumentPanel.vue'
import { defineComponent, ref } from 'vue'
import TitleBar from './TitleBar.vue'

const barCharts = ref()
const myChart = ref<any>(null)
const tList = [
  { num: 235, value: '正常' },
  { num: 17, value: '即将过期' },
  { num: 89, value: '已过期' },
]

defineComponent({ name: 'CerComp' })
</script>

<style scoped lang="scss">
.evolution_trends {
  width: 100%;

  .charts_value {
    width: 100%;
    height: 280px;
  }

  ._fi_con {
    color: #fff;
    display: flex;
    align-items: center;
    padding-left: 20px;
    margin-bottom: 6px;
    margin-top: 10px;

    ._con {
      position: relative;
      margin-left: 5px;

      ._gltj {
        width: 110px;
        height: 95px;
      }

      div {
        width: 100%;
        position: absolute;
        top: -10px;
        left: 50%;
        transform: translate(-50%, 0);
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 14px;

        span:nth-child(1) {
          font-weight: 600;
          font-size: 16px;
        }
      }
    }
  }
}
</style>
