<template>
  <div class="header_con">
    <div class="_t">鞍钢矿业智慧监管安全大脑</div>
    <img class="_h" src="@/assets/image/header_bg.png" />
    <div class="_h_l_b" @click="jump('/brainScreen')">
      <img src="@/assets/image/bigScreen/_header_left_block.png" />
      <span :class="{ active_span: routes.path.includes('brainScreen') }" v-if="!routes.path.includes('fieldOver')"
        >安全履职</span
      >
      <span class="active_span" v-else>返回</span>
    </div>
    <div class="_h_r_b" @click="jump('/monitorWarn')">
      <img src="@/assets/image/bigScreen/_header_right_block.png" />
      <span :class="{ active_span: routes.path.includes('monitorWarn') }">监测预警</span>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const routes = useRoute()
const router = useRouter()
const jump = (routeName: string) => {
  router.push({
    path: routeName,
  })
}
</script>

<style scoped lang="scss">
.header_con {
  color: #fff;
  position: relative;
  z-index: 99999;

  ._t {
    position: absolute;
    left: 50%;
    top: 30%;
    transform: translate(-50%, -50%);
    font-family: MyCustomFont, sans-serif;
    font-size: 36px;
    text-shadow: 0 2px 8px rgba(41, 47, 58, 0.05);
    background: linear-gradient(180deg, #a2e4ff, #e9f8ff 60%, #f8fdff);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  ._h {
    width: 100%;
    height: 125px;
  }

  ._h_l_b {
    position: absolute;
    top: 18%;
    left: 16%;
    width: 249px;
    height: 50px;
    cursor: pointer;

    span {
      display: block;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-family: YouSheBiaoTiHei;
      font-weight: 600;
      font-size: 22px;
      font-synthesis: style;
      font-style: italic;
      color: #b4d9f3;
      text-shadow: 3px 3px 5px rgba(0, 0, 0, 0.25);
    }

    .active_span {
      color: #fff;
    }
  }

  ._h_r_b {
    position: absolute;
    top: 18%;
    right: 16%;
    width: 249px;
    height: 50px;
    cursor: pointer;

    span {
      display: block;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-family: YouSheBiaoTiHei;
      font-weight: 600;
      font-size: 22px;
      font-synthesis: style;
      font-style: italic;
      color: #b4d9f3;
      text-shadow: 3px 3px 5px rgba(0, 0, 0, 0.25);
    }

    .active_span {
      color: #fff;
    }
  }

  ._header_r_tab {
    position: absolute;
    right: 22%;
    top: 0;
    width: 540px;
    height: 64px;
    line-height: 64px;
    display: flex;
    align-items: center;
    cursor: pointer;

    div {
      text-align: center;
      width: 180px;
      height: 64px;
      line-height: 64px;
      position: relative;

      img {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 82px;
      }

      span {
        width: 100%;
        height: 100%;
        display: block;
        font-family: MyCustomFont, sans-serif;
        font-size: 28px;
        text-shadow: 0 2px 8px rgba(41, 47, 58, 0.05);
        background: linear-gradient(180deg, #559dcd, #e9f8ff 60%, #f8fdff);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      ._header_r_span {
        background: linear-gradient(180deg, #fff, #fff 60%, #fff);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}
</style>
