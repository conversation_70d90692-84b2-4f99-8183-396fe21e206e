<template>
  <div>
    <TitleBar title="隐患排查治理统计" :showTab="true" />
    <div class="_yin_content">
      <div class="_yin_air">
        <img class="_jcrwzs" src="@/assets/image/bigScreen/_jcrwzs.png" />
        <div class="_yin_statics">
          <span>200</span>
          <span>检查任务总数</span>
        </div>
      </div>
      <div class="_gre_con">
        <ProgressChart :bgimg="false" :pgsStyle="true" :echartsData="ecData" :showText="false" />
        <div class="_con_ge">
          <span>{{ ecData }}<span>%</span></span>
          <span>按期检查率</span>
        </div>
      </div>
      <div class="_yin_air">
        <img class="_jcrwzs" src="@/assets/image/bigScreen/_ywcrwzs.png" />
        <div class="_yin_statics">
          <span>184</span>
          <span>已完成</span>
        </div>
      </div>
      <div class="_yin_air">
        <img class="_jcrwzs" src="@/assets/image/bigScreen/_jxz.png" />
        <div class="_yin_statics">
          <span>16</span>
          <span>进行中</span>
        </div>
      </div>
    </div>
    <div class="_y_check">(检查完成率)</div>
    <div style="height: 180px">
      <ComPaiMing :graphic-color="['rgba(43, 97, 223, 1)', 'rgba(39, 150, 253, 1)']" :echartsData="datas" />
    </div>
  </div>
</template>
<script setup lang="ts">
import ComPaiMing from '@/components/bigScreen/PaiMing.vue'
import ProgressChart from '@/components/bigScreen/ProgressChart.vue'
import TitleBar from './TitleBar.vue'

const datas = [
  {
    name: '一选作业区',
    value: 98,
  },
  {
    name: '二选作业区',
    value: 96,
  },
  {
    name: '破碎作业区',
    value: 92,
  },
  {
    name: '运维事业部',
    value: 88,
  },
  {
    name: '尾矿作业区',
    value: 1,
  },
]
const ecData = 47
</script>

<style scoped lang="scss">
._yin_content {
  width: 100%;
  color: #fff;
  display: flex;
  justify-content: space-around;
  margin-top: -28px;

  ._gre_con {
    display: flex;
    align-items: center;
    font-size: 12px;
    width: 120px;
    height: 120px;
    position: relative;
    margin-left: -20px;
    margin-right: 35px;

    ._con_ge {
      position: absolute;
      right: -50px;
      top: 35px;
      width: 70px;
      display: flex;
      flex-direction: column;

      span:nth-child(1) {
        font-size: 20px;
        font-weight: 600;
      }
    }
  }

  ._yin_air {
    display: flex;
    align-items: center;
    margin-left: 10px;

    img {
      width: 69px;
      height: 69px;
    }

    ._yin_statics {
      font-size: 12px;
      width: 50px;
      display: flex;
      flex-direction: column;

      span:nth-child(1) {
        font-weight: 800;
        font-size: 16px;
      }
    }
  }
}

._y_check {
  color: #96d4ec;
  font-size: 14px;
  text-align: right;
  margin-top: -20px;
}
</style>
