<template>
  <div class="_flip_number">
    <div class="_flip_num flex w-[850px]">
      <div class="w-[210px] h-[70px] text-[#fff] pt-[15px] pl-[100px]" :class="'img'+index" v-for="item,index in imgList">
        <div class="tt">{{item.num}}</div>
        <div class="_name">{{item.name}}</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'

let imgList = ref([
    {
    name: '培训任务总数',
    num: 354
}, {
    name: '已完成任务总数',
    num: 235
}, {
    name: '逾期未完成',
    num: 56
}, {
    name: '按期完成率',
    num: 96
},
])
</script>

<style scoped lang="scss">
  .img0 {
    background: url(../../../assets/image/xgfBigScree/b0.png) no-repeat center center;
    background-size: cover;
  }

  .img1 {
    background: url(../../../assets/image/xgfBigScree/b1.png) no-repeat center center;
    background-size: cover;
  }

  .img2 {
      background: url(../../../assets/image/xgfBigScree/b2.png) no-repeat center
      center;
    background-size: cover;
  }

  .img3 {
      background: url(../../../assets/image/xgfBigScree/b3.png) no-repeat center
      center;
    background-size: cover;
  }

  .tt{
    font-family: D-DIN-PRO;
    font-weight: bold;
    font-size: 30px;
    color: #4CCAFF;
    line-height: 30px;
    background: linear-gradient(0deg, #FFFFFF 0%, #44BFFF 53.759765625%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  ._name {
    font-size: 14px;
  }
</style>
