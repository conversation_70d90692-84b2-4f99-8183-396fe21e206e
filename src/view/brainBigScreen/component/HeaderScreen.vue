<template>
  <div class="header_con">
    <div class="_t">鞍钢矿业安全大脑</div>
    <img class="_h" src="@/assets/image/header_bg1.png" />
    <div class="_navs">
      <span
        class="_nav_item"
        :class="{ _nav_item_active: routes.path.includes(item.routeName) }"
        v-for="(item, index) in navList"
        :key="index"
        @click="jump(item.routeName)"
        >{{ item.name }}</span
      >
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const navList = ref([
  { routeName: 'brainScreen', name: '安全总览' },
  { routeName: 'educationalTrain', name: '教育培训' },
  { routeName: 'xgfScreen', name: '相关方管理' },
  { routeName: 'cs', name: '隐患排查治理' },
  { routeName: 'cs', name: '现场作业管理' },
])
const routes = useRoute()
const router = useRouter()
const jump = (routeName: string) => {
  if (routeName === 'cs') {
    return
  }
  router.push({
    path: `/${routeName}`,
  })
}
</script>

<style scoped lang="scss">
.header_con {
  color: #fff;
  position: relative;
  z-index: 99999;

  ._t {
    letter-spacing: 2px;
    position: absolute;
    left: 12%;
    top: 30%;
    transform: translate(-50%, -50%);
    font-family: MyCustomFont, sans-serif;
    font-size: 38px;
    text-shadow: 0 2px 8px rgba(41, 47, 58, 0.05);
    background: linear-gradient(180deg, #a2e4ff, #e9f8ff 60%, #f8fdff);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  ._h {
    width: 100%;
    height: 125px;
  }

  ._navs {
    letter-spacing: 1px;
    position: absolute;
    right: 1%;
    top: 30%;
    transform: translate(-10%, -50%);
    display: flex;
    align-items: center;

    ._nav_item,
    ._nav_item_active {
      cursor: pointer;
      margin-right: 40px;
      font-family: MyCustomFont, sans-serif;
      font-size: 28px;
      text-shadow: 0 2px 8px rgba(41, 47, 58, 0.05);
      background: linear-gradient(180deg, #ffffff, #a2cbde 60%, #f8fdff);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    ._nav_item_active {
      text-shadow: 0 2px 8px rgba(41, 47, 58, 0.05);
      background: linear-gradient(180deg, #ffffff, #ffffff 60%, #f8fdff);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 30px;
    }
  }
}
</style>
