<template>
    <div>
        <TitleBar title="岗前培训落实情况" />
        <div class="_yin_content">
            <div class="_yin_air">
                <img class="_pxrwzs" src="@/assets/image/bigScreen/_pxrwzs.png">
                <div class="_yin_statics">
                    <span>200</span>
                    <span>岗前培训次数</span>
                </div>
            </div>
            <div class="_yin_air">
                <img class="_yqwwc" src="@/assets/image/bigScreen/_yqwwc.png">
                <div class="_yin_statics">
                    <span>184</span>
                    <span>完成率</span>
                </div>
            </div>
            <div class="_gre_con">
                <ProgressChart :bgimg="false" :pgsStyle="true" :echartsData="ecData" :showText="false" :borderColor="colorGress"/>
                <div class="_con_ge">
                    <span>{{ ecData }}<span>%</span></span>
                    <span>通过率</span>
                </div>
            </div>
        </div>
        <div class="_y_check">(按期完成率)</div>
        <div style="height: 310px;">
            <ComPaiMing :echartsData="echartsData" :graphic-color="['rgba(43, 97, 223, 1)', 'rgba(39, 150, 253, 1)']" />
        </div>
    </div>
</template>
<script setup lang="ts">
    import { ref } from 'vue';
    import ComPaiMing from '@/components/bigScreen/PaiMing.vue';
    import TitleBar from './TitleBar.vue';

    const echartsData = ref([
        {
          name: '东鞍山铁矿',
          value: 98,
        },
        {
          name: '眼前山井矿',
          value: 96,
        },
        {
          name: '齐大山选矿',
          value: 92,
        },
        {
          name: '齐大山矿场',
          value: 88,
        },
        {
          name: '大孤山球团厂',
          value: 1,
        },
        {
          name: '眼前山井矿',
          value: 1,
        },
        {
          name: '齐大山矿场',
          value: 88,
        },
        {
          name: '齐大山选矿',
          value: 38,
        },
    ]);
    const ecData = 76;
    const colorGress = ['rgba(153, 42, 116, 0.5)', 'rgba(153, 42, 116, 0.8)'];
</script>

<style scoped lang="scss">
    ._yin_content {
        width: 100%;
        color: #fff;
        display: flex;
        justify-content: space-around;
        margin-top: -20px;

        ._gre_con {
            display: flex;
            align-items: center;
            font-size: 12px;
            width: 120px;
            height: 120px;
            position: relative;
            margin-left: -20px;
            margin-right: 35px;

            ._con_ge {
                position: absolute;
                right: -50px;
                top: 35px;
                width: 70px;
                display: flex;
                flex-direction: column;

                span:nth-child(1) {
                    font-size: 20px;
                    font-weight: 600;
                }
            }
        }
        
        ._yin_air {
            display: flex;
            align-items: center;
            margin-left: 10px;

            img {
                width: 69px;
                height: 69px;
            }

            ._yin_statics {
                font-size: 12px;
                width: 100px;
                display: flex;
                flex-direction: column;

                span:nth-child(1) {
                    font-weight: 800;
                    font-size: 16px;
                }
            }
        }
    }

    ._y_check {
        color: #96d4ec;
        font-size: 14px;
        text-align: right;
        margin-top: -20px;
    }
</style>
