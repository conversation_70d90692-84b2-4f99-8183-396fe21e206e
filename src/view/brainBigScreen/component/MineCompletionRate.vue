<template>
    <div class="_annual">
        <TitleBar title="各矿厂完成率分布"/>
        <div class="_y_check">(按期完成率)</div>
        <div style="height: 280px;">
            <ComPaiMing :echartsData="echartsData" :graphic-color="['rgba(43, 97, 223, 1)', 'rgba(39, 150, 253, 1)']" />
        </div>
    </div>
    
</template>
<script setup lang="ts">
    import { ref } from 'vue';
    import ComPaiMing from '@/components/bigScreen/PaiMing.vue';
    import TitleBar from './TitleBar.vue';

    const echartsData = ref([
        {
          name: '东鞍山铁矿',
          value: 98,
        },
        {
          name: '眼前山井矿',
          value: 96,
        },
        {
          name: '齐大山选矿',
          value: 92,
        },
        {
          name: '齐大山矿场',
          value: 88,
        },
        {
          name: '大孤山球团厂',
          value: 1,
        },
        {
          name: '眼前山井矿',
          value: 1,
        },
        {
          name: '齐大山矿场',
          value: 88,
        },
        {
          name: '齐大山选矿',
          value: 68,
        },
    ]);
</script>

<style scoped lang="scss">
    ._annual {
        color: #fff;

        ._y_check {
            color: #96d4ec;
            font-size: 14px;
            text-align: right;
        }
    }
</style>
