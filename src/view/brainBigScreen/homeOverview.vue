<template>
  <div id="screen" class="screen">
    <div class="bodys">
      <ScreenNumber :days="'000277'" />
      <HeaderScreen />
      <HomeGis />
      <div class="_screen_content">
        <div class="_screen_left">
          <HiddenDangerStatistics />
          <FieldOperationStatistics />
          <AccidentStatistics />
        </div>
        <div class="_screen_right">
          <WholeStaffStatistics />
          <CertificateStatistics />
          <HealthStatistics />
          <EmergencyStatistics />
        </div>
      </div>
      <!-- <BottomNavs /> -->
    </div>
  </div>
</template>
<script setup lang="ts">
import HomeGis from '~/components/HomeGis/HomeGis.vue'
import HeaderScreen from './component/HeaderScreen.vue'
import HiddenDangerStatistics from './component/HiddenDangerStatistics.vue'
import FieldOperationStatistics from './component/FieldOperationStatistics.vue'
import AccidentStatistics from './component/AccidentStatistics.vue'
import WholeStaffStatistics from './component/WholeStaffStatistics.vue'
import CertificateStatistics from './component/CertificateStatistics.vue'
import HealthStatistics from './component/HealthStatistics.vue'
import EmergencyStatistics from './component/EmergencyStatistics.vue'
import ScreenNumber from '@/components/bigScreen/ScreenNumber.vue'
// import BottomNavs from '@/components/bigScreen/BottomNavs.vue'
import { onMounted } from 'vue'

//数据大屏自适应函数
const handleScreenAuto = (): void => {
  const designDraftWidth = 1920 //设计稿的宽度
  const designDraftHeight = 1080 //设计稿的高度
  //根据屏幕的变化适配的比例
  const scale =
    document.documentElement.clientWidth / document.documentElement.clientHeight < designDraftWidth / designDraftHeight
      ? document.documentElement.clientWidth / designDraftWidth
      : document.documentElement.clientHeight / designDraftHeight
  //缩放比例
  ;(document.querySelector('#screen') as any).style.transform = `scale(${scale}) translate(-50%)`
}

onMounted(() => {
  //初始化自适应 -刚显示的时候就开始适配一次
  handleScreenAuto()
  //绑定自适应函数 -防止浏览器栏变化后不再适配
  window.onresize = () => handleScreenAuto()
})
</script>

<style scoped lang="scss">
.screen {
  display: inline-block;
  width: 1920px; //设计稿的宽度
  height: 1080px; //设计稿的高度
  transform-origin: 0 0;
  position: absolute;
  left: 50%;
  background: url('@/components/HomeGis/assets/bg.png') no-repeat #030910;
  background-size: cover;
  overflow: hidden !important;
  letter-spacing: 1px;

  .bodys {
    ._screen_content {
      display: flex;
      justify-content: space-between;

      ._screen_left {
        width: 27%;
      }

      ._screen_right {
        width: 27%;
        padding-right: 20px;
      }
    }
  }
}
</style>
