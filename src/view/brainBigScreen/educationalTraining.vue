<template>
  <div id="screen" class="screen">
    <div class="bodys">
      <HeaderScreen />
      <div class="_screen_content">
        <div class="_screen_left">
          <TrainingPlan />
          <Annual />
          <PlanType />
        </div>
        <div class="_screen_center">
          <div class="_center_select">
            <el-select :teleported="false" style="width: 200px" v-model="selectObj.company" placeholder="请选择">
              <el-option v-for="item in companyList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
            <el-select :teleported="false" style="width: 200px" v-model="selectObj.year" placeholder="请选择">
              <el-option v-for="item in yearList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <CenterNav />
          <div class="_center_con">
            <div style="width: 50%"><MineCompletionRate /></div>
            <div style="width: 50%"><TrainingTask /></div>
          </div>
          <div class="_center_con _center_se">
            <div style="width: 50%"><TrainingForm /></div>
            <div style="width: 50%"><TrainingForm :dataObj="botData" /></div>
          </div>
        </div>
        <div class="_screen_right">
          <ImplementationEffect />
          <OrientationPeriod />
        </div>
      </div>
      <!-- <BottomNavs /> -->
    </div>
  </div>
</template>
<script setup lang="ts">
import HeaderScreen from './component/HeaderScreen.vue'
import TrainingPlan from './component/TrainingPlan.vue'
import Annual from './component/Annual.vue'
import MineCompletionRate from './component/MineCompletionRate.vue'
import PlanType from './component/PlanType.vue'
import ImplementationEffect from './component/ImplementationEffect.vue'
import OrientationPeriod from './component/OrientationPeriod.vue'
import TrainingForm from './component/TrainingForm.vue'
import CenterNav from './component/CenterNav.vue'
import TrainingTask from './component/TrainingTask.vue'
// import BottomNavs from '@/components/bigScreen/BottomNavs.vue';
import { onMounted, reactive, Teleport } from 'vue'

const selectObj = reactive({
  company: '1',
  year: '2024',
})

const companyList = [
  { value: '1', label: '鞍钢矿业集团' },
  { value: '2', label: '东鞍山铁矿厂' },
]
const yearList = [
  { value: '2023', label: '2023年度' },
  { value: '2024', label: '2024年度' },
]

const botData = {
  title: '课程资源分布',
  leftTitle: '试题数',
  leftNum: '235',
  rightTitle: '课件数',
  rightNum: '221',
  totalTitle: '总资源总数',
  totalNum: '722',
}

//数据大屏自适应函数
const handleScreenAuto = (): void => {
  const designDraftWidth = 1920 //设计稿的宽度
  const designDraftHeight = 1080 //设计稿的高度
  //根据屏幕的变化适配的比例
  const scale =
    document.documentElement.clientWidth / document.documentElement.clientHeight < designDraftWidth / designDraftHeight
      ? document.documentElement.clientWidth / designDraftWidth
      : document.documentElement.clientHeight / designDraftHeight
  //缩放比例
  ;(document.querySelector('#screen') as any).style.transform = `scale(${scale}) translate(-50%)`
}

onMounted(() => {
  //初始化自适应 -刚显示的时候就开始适配一次
  handleScreenAuto()
  //绑定自适应函数 -防止浏览器栏变化后不再适配
  window.onresize = () => handleScreenAuto()
})
</script>

<style scoped lang="scss">
.screen {
  display: inline-block;
  width: 1920px; //设计稿的宽度
  height: 1080px; //设计稿的高度
  transform-origin: 0 0;
  position: absolute;
  left: 50%;
  background: url('@/components/HomeGis/assets/bg.png') no-repeat #030910;
  background-size: cover;
  overflow: hidden !important;
  letter-spacing: 1px;

  .bodys {
    ._screen_content {
      display: flex;
      justify-content: space-between;

      ._screen_left {
        width: 27%;
      }

      ._screen_center {
        width: 46%;

        ._center_select {
          width: 450px;
          display: flex;
          justify-content: space-between;
          padding-left: 25px;
          margin-bottom: 40px;
        }

        ._center_con {
          display: flex;
          margin-top: 60px;
          margin-bottom: 60px;
        }

        ._center_se {
          margin-top: -10px;
        }
      }

      ._screen_right {
        width: 27%;
        padding-right: 20px;
      }
    }
  }
}

/* 添加自定义类名以指定样式 */
:deep(.el-select__wrapper) {
  background-color: #062948; /* 修改背景色 */
  box-shadow: 0 0 0 1px #1b589e inset; /* 修改边框颜色 */
  --el-select-input-color: #fff;
}

:deep(.el-select__popper) {
  background-color: #062948 !important; /* 修改背景色 */
  border: 1px solid #062948; /* 修改边框颜色 */
}

:deep(.el-select__placeholder) {
  color: #fff;
}

:deep(.el-select__popper.el-popper[data-popper-placement^='bottom'] .el-popper__arrow:before) {
  background-color: #062948;
  border-color: #062948;
}

:deep(.el-select-dropdown__item) {
  color: #fff;
}

:deep(.el-select-dropdown__item.is-hovering) {
  background-color: rgba(60, 181, 251, 0.2);
}
</style>
