import { defineStore } from 'pinia'

export default defineStore('orgMgmtBizSelectUsers', {
  state() {
    return {
      refreshFlag: 1, //刷新标识，值变了就刷新页面数据
      refreshPerson: 2, //用户列表刷新
      refreshBumen: 3, //用户列表刷新
      selectedNode: null, //选中的树结点
      deptId: '', //组织机构编码
      currentCode: '', //当前默认的组织树
      deptName: '',
      parentId: '', //父节点编码
      leftFlag: 1, //鞍钢 和 合作企业
    } as any
  },
  actions: {
    updateRefreshPerson() {
      this.refreshPerson = -this.refreshPerson
    },
    updateRefreshBumen() {
      this.refreshBumen = -this.refreshBumen
    },
    upadateCurrentCode(val) {
      this.currentCode = val
    },
    updateSelectedNode(node: any) {
      if (!node) return
      this.selectedNode = node
      this.deptId = node.id
      this.orgCode = node.id
      this.deptName = node.text
      this.parentId = node.parentId
      // this.updateDeptInfo(node.nodeType == 'dept' ? node._dataObj : {})
      // let deptList = []
      // if (node.children.length > 0) {
      //   deptList = node.children.filter((e: any) => e.nodeType === 'dept')
      // }
      // this.updateDeptList(deptList)
    },
    updateRefreshFlag() {
      this.refreshFlag = -this.refreshFlag
    },
    updateDeptInfo(data?: any) {
      this.deptId = data.deptId
      const o = {
        deptName: data.deptName,
        deptId: data.deptId,
      }
      this.deptInfo = { ...o }
    },
    updateAllDeptsList(depts: any[]) {
      this.allDeptsList = depts
    },
    updateDeptList(list: any[]) {
      this.deptList = list
    },
    updateAllUserList(users: any[]) {
      if (!users) return
      this.allUserList = users
    },
    updateRoleOptions(options: any) {
      this.roleOptions = options
    },
    updateCrrDeptNodeKeyByClicked(key: string) {
      console.log('00000', key)
      this.crrDeptNodeKeyByClicked = key
    },
    updateCertificateList(list: any[]) {
      this.certificateList = list
    },
    updateDelBranch(val: any) {
      this.delBranch = val
    },
    cleanAll() {
      this.deptId = ''
      this.currentCode = ''
      this.deptName = ''
      this.parentId = ''
      this.selectedNode = null
    },
  },
})
