<template>
  <div class="tree_bgc">
    <div class="title">组织架构</div>
    <el-input
      v-model="filterText"
      style="width: 100%; margin: 0px 0px 15px; height: 35px"
      placeholder="请输入组织名称"
      :suffix-icon="Search"
      clearable
    />
    <div class="h-[calc(100%-54px)] overflow-hidden">
      <el-scrollbar max-height="100%">
        <el-tree
          :default-expand-all="true"
          ref="tree"
          :default-checked-keys="selectedKeys"
          :data="treeData"
          :highlight-current="true"
          :props="defaultProps"
          :current-node-key="currentCode"
          node-key="id"
          :expand-on-click-node="false"
          @node-click="handleSelect"
          :key="currentCode"
          :filter-node-method="filterNode"
          :render-content="renderContent"
          show-checkbox
          @check="handleCheck"
          :checkStrictly="true"
        >
          <template #default="{ node }">
            <div class="custom-tree-node">
              <div class="left-wrap">
                <span style="padding-right: 4px" :class="node.level === 1 ? 'examTitle-father' : 'examTitle-child'">{{
                  node.label
                }}</span>
                <!--                {{ '(' + node.childNodes.length + ')' }}人-->
              </div>
            </div>
          </template>
        </el-tree>
      </el-scrollbar>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dept from '@/assets/image/dept.png'
import yewu from '@/assets/image/examManagement/menu-child.png'
import jianguan from '@/assets/image/examManagement/menu-father.png'
import { useUserInfo } from '@/store'
import { Search } from '@element-plus/icons-vue'
import { nextTick, onMounted, ref, watch } from 'vue'
import $API from '~/common/api'
import store from './store'

const ui = useUserInfo()
const props = defineProps({
  superviseUnitId: {
    default: '',
    type: String,
  },
  tabOptionFlag: {
    default: () => false,
    type: Boolean,
  },
})

const emits = defineEmits(['selectDepts'])

const defaultProps = {
  children: 'children',
  label: 'text',
}

const useStore = store()
const tree: any = ref()
const treeData: any = ref([])
// const tabActive: any = ref('1')
const selectedKeys = ref(['-1'])
const filterText = ref('')
const currentCode = ref('')
const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.text.includes(value)
}
// 点击树节点
function handleSelect(_: any, e: any) {
  console.log(e, 'dianjile -----------------------')
  const node = e.data
  console.log(e.data, '*********----------nodecur-------')
  useStore.updateSelectedNode(node)
}

watch(
  () => useStore.refreshFlag,
  () => {
    findTreeData()
  }
)

// 树的列表
async function findTreeData() {
  const result: any = await $API.post({
    url: 'ehs-clnt-platform-service/workbench/msg/queryOrgTreeByTanzer',
    params: { type: '1' },
  })
  if (result && result.code === 'success') {
    const _data = result.data
    console.log(_data, '////////////////', _data[0].id)
    currentCode.value = _data[0].id
    useStore.upadateCurrentCode(currentCode.value)
    // treeData.value = setTreeData(_tableData, '', 'org', '')
    treeData.value = _data
    console.log('treeData.value =======', treeData.value)
    // useStore.updateSelectedNode(searchNodeByKey(selectedKeys.value.length === 0 ? '0' : selectedKeys.value[0]))
  }
}

function fetchTreeData() {
  console.log(props.superviseUnitId, '+++***----props----------------------')
  // nextTick(() => tree.value.setCurrentKey(props.superviseUnitId))
  nextTick(() => tree.value.setCurrentKey(-1))
}

function getIcon() {
  return ui.value.zhLogoUrl + ui.value.zhLogo + '.png'
}
function renderContent(h, { data }) {
  let prefixIcon = data.attributes.orgType === '2' ? jianguan : data.attributes.orgType === '1' ? yewu : dept
  if (data.parentId === '-1') prefixIcon = getIcon()

  return h('div', { class: 'flex items-center pl-10px', title: data.text }, [
    h('img', {
      src: prefixIcon,
      class: 'w-[20px] h-[20px] inline-block mr-10px',
    }), // 自定义前缀图标
    h('span', { class: 'truncate' }, data.text), // 显示节点的标签
  ])
}

// 处理复选框选中事件
function handleCheck(data, checkedInfo) {
  const { checkedNodes } = checkedInfo
  const selectedDepts = checkedNodes.map((node) => ({
    id: node.id,
    deptName: node.text,
    isDept: 1,
  }))
  // 发送选中的部门信息到父组件
  console.log('selectedDepts', selectedDepts)
  emits('selectDepts', selectedDepts)
}
function setChecked(deptId: any) {
  selectedKeys.value = deptId
  nextTick(() => {
    if (tree.value) {
      tree.value.setCheckedKeys(deptId)
      const checkedNodes = tree.value.getCheckedNodes()
      const arr = checkedNodes.map((item: any) => {
        return {
          id: item.id,
          deptName: item.text,
          isDept: 1,
        }
      })

      emits('selectDepts', arr)
    }
  })
}

function setCheckedKeys(deptId: any) {
  tree.value?.setCheckedKeys(deptId)
  nextTick(() => {
    if (tree.value) {
      const checkedNodes = tree.value.getCheckedNodes()
      const arr = checkedNodes.map((item: any) => {
        return {
          id: item.id,
          deptName: item.text,
          isDept: 1,
        }
      })
      emits('selectDepts', arr)
    }
  })
}

watch(filterText, (val) => {
  tree.value!.filter(val)
})

onMounted(async () => {
  await findTreeData()
  fetchTreeData()
  setChecked(selectedKeys.value)
})

defineExpose({
  tree,
  setChecked,
  setCheckedKeys,
})
</script>

<style lang="scss" scoped>
.tabs-wrap {
  text-align: center;
}

:deep(.el-input__inner) {
  font-size: 14px;
}

.tree_bgc {
  height: 100%;
  background: white;
  border-radius: 6px;
  box-sizing: border-box;

  .title {
    padding-left: 24px;
    display: flex;
    width: 100%;
    height: 54px;
    align-items: center;
    /* 垂直居中 */
    font-weight: 500;
    font-size: 16px;
    color: #262626;
    line-height: 17px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    border-bottom: 1px solid #ebeef5ff;
  }

  .custom-tree-node {
    flex: 1;
    padding: 5px 0 5px 10px;
    user-select: none;
  }

  :deep(.el-input) {
    height: 40px;
    line-height: 40px;
    margin-bottom: 15px;
  }

  :deep(.el-tree) {
    .el-tree-node__content {
      margin-top: 4px;
      border-radius: 6px;
      height: auto;
      padding: 5px 0;
      white-space: pre-line;

      .el-tree-node__label {
        font-size: 16px;
      }

      .el-tree-node__expand-icon {
        font-size: 15px;

        &:first-child {
          display: none;
        }

        svg {
          width: 15px;
          height: 15px;
        }
      }
    }

    .el-tree-node__children .el-tree-node__expand-icon:first-child {
      display: block;
    }
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;

  .left-wrap {
    flex: 1;
    position: relative;

    span {
      padding-left: 25px;
    }

    .examTitle-father::before {
      content: '';
      margin-right: 5px;
      width: 16px;
      height: 16px;
      background: url('../../../../assets/image/org/icon_01.png') no-repeat center/cover;
      display: block;
      position: absolute;
      left: -2px;
      top: 3px;
    }

    .examTitle-child::before {
      content: '';
      margin-right: 5px;
      width: 16px;
      height: 16px;
      background: url('../../../../assets/image/org/icon_02.png') no-repeat center/cover;
      display: block;
      position: absolute;
      left: -2px;
      top: 3px;
    }
  }

  .right-wrap {
    margin-right: 15px;
    color: #527cff;

    a {
      margin-left: 20px;
      display: inline-block;
    }
  }
}
</style>
