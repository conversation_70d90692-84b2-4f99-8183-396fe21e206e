<template>
  <table-list
    ref="tableListRef"
    @current-change="currentChange"
    @size-change="handleSizeChange"
    :data="tableDataList"
    :pageModel="pagination"
    :loading="loading"
    @selection-change="handleSelectionChange"
    :pageNumber="5"
    :pageFlag="true"
    :showHeader="false"
    :userDefined="false"
    :columns="[]"
    stripe
  >
    <!--  @select="handleSelect"   @selection-change="handleSelectionChange"-->
    <el-table-column type="selection" width="40" :reserve-selection="true" align="center"></el-table-column>
    <el-table-column
      v-for="item in tableColumns"
      :label="item.label"
      :key="item.id"
      :type="item.type"
      :width="item.width"
      :property="item.property"
      show-overflow-tooltip
    >
      <template v-if="item.type !== 'index'" #default="scope">
        <template v-if="item.key === 'personType'">
          <template v-if="scope.row[item.key] == '2'"> 相关方员工 </template>
          <template v-else-if="scope.row[item.key] == '1'"> 正式员工 </template>
          <template v-else>--</template>
        </template>
        <template v-else>
          {{ scope.row[item.key] !== 0 ? scope.row[item.key] || '--' : scope.row[item.key] }}
        </template>
      </template>
    </el-table-column>
  </table-list>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, watch } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import { tableColumnsArr } from '../constant'
import store from './store'

// defineProps({
//   deptId: {
//     type: String,
//     default: '',
//   },
// })

const ui = useUserInfo().value
const emits = defineEmits(['selectUser'])

const tableColumns = ref<any>(tableColumnsArr)
const useStore = store()
const tableListRef = ref<any>(null)
const tableDataList = ref<any>([])
const loading = ref<boolean>(false)
const pagination = reactive({
  total: 0,
  pageNo: 1,
  pageSize: 10,
  // showSizeChanger: true,
  // showTotal: (total: any) => `共${total}条`,
})
const filterForm = ref<any>({
  keywords: '',
})

// 列表
async function getListData() {
  loading.value = true
  const params: any = {
    orgCode: useStore.orgCode,
    pageNo: pagination.pageNo,
    pageSize: pagination.pageSize,
    ...filterForm.value,
  }
  try {
    const result: any = await $API.post({
      url: 'ehs-clnt-platform-service/workbench/msg/queryUserByDeptCode',
      params,
    })
    if (result && result.code === 'success') {
      tableDataList.value = result.data.rows.map((item: any) => ({
        ...item,
        isDept: 0,
      }))
      pagination.total = result.data.total

      // 数据加载完成后，检查是否有需要选中的行
      nextTick(() => {
        // 获取父组件传入的选中项
        const selectedUsers = userList.value?.filter((item) => !item.isDept) || []
        if (selectedUsers.length > 0) {
          tableListRef.value?.toggleSelectionChild(selectedUsers)
        }
      })
    }
  } catch (error) {
  } finally {
    loading.value = false
  }
}

// 分页切换
function currentChange(val: any) {
  pagination.pageNo = val
  getListData()
}

function handleSizeChange(val: any) {
  console.log(val)
  pagination.pageNo = 1
  pagination.pageSize = val
  getListData()
}

function getListDataWatch() {
  tableDataList.value = []
  pagination.pageNo = 1
  getListData()
}

function getListDataWatch2(filter: any) {
  filterForm.value = filter
  tableDataList.value = []
  pagination.pageNo = 1
  getListData()
}

function handleSelectionChange(val: any) {
  // let newArr = goDuplicateArray(val)
  console.log('resArr----------', val)
  emits('selectUser', val)
}

// 去除选择重复的数组
// function goDuplicateArray(val) {
//   let obj = {}
//   let arrNewSelection = val.reduce((a, b) => {
//     obj[b.id] ? '' : (obj[b.id] = true && a.push(b))
//     return a
//   }, [])
//   return arrNewSelection
// }

function toggleSelectionChild(rows: any) {
  if (rows) {
    tableListRef.value!.toggleSelectionChild(rows)
  }
}

// 添加给父组件使用的选中用户列表
const userList = ref<any>([])

// 监听父组件传入的选中用户
watch(
  () => userList.value,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      const users = newVal.filter((item) => !item.isDept)
      if (users.length > 0 && tableDataList.value.length > 0) {
        toggleSelectionChild(users)
      }
    }
  }
)

watch(
  () => useStore.orgCode,
  () => {
    console.log('++++++++++*第一变化*************')
    getListDataWatch()
  }
)

getListData()

defineExpose({
  toggleSelectionChild,
  getListDataWatch2,
  userList,
})
</script>

<style lang="scss" scoped>
:deep(.el-tooltip__popper) {
  font-size: 12px;
}

.action-label {
  color: #2f54eb;
}

.tag {
  background: rgb(51 92 255);
  color: white;
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  padding: 0 4px;
  margin-left: 10px;
}

.operate-item {
  font-size: 14px;
  color: #527cff;
  cursor: pointer;
}

:deep(.header-item_control) {
  width: 100% !important;
}

.pagination-wrap {
  display: flex;
  justify-content: center;
  padding-bottom: 0;

  .el-pagination {
    .el-input {
      .el-input__inner {
        padding-left: 0 !important;
      }
    }

    .el-select {
      height: 32px;
    }
  }
}

:deep(.el-table__header) {
  width: 100% !important;
}

:deep(.el-table__body) {
  width: 100% !important;
}

:deep(.table-list_wrap) {
  padding-bottom: 0 !important;
}

:deep(.el-pagination) {
  padding-right: 20px;
  margin-top: 20px;
}
</style>
