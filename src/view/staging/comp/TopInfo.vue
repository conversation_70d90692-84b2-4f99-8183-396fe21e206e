<template>
  <div class="top" v-loading="loading">
    <div class="left">
      <div class="img img-text">{{ userInitial }}</div>
      <div>
        <div class="left-name">{{ ui.unitName }}</div>
        <div class="left-dept">
          {{ ui.userName }} <span v-if="ui.loginName">/ {{ ui.loginName }}</span>
        </div>
        <div class="left-dept">
          {{ ui.deptName }} <span v-if="ui.postName">/ {{ ui.postName }}</span>
        </div>
        <div class="select_dept">
          <el-select v-model="model" @change="changeDept" placement="right-end" class="width-auto">
            <el-option v-for="item in deptList" :key="item.orgCode" :label="item.orgName" :value="item.orgCode">
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <div class="middle">
      <div class="today-weather">
        <div style="display: flex">
          <div class="name">今日天气</div>
          <div class="s-weather" v-if="weatherList[6]">
            <div class="triangle"></div>
            <div class="weather-notice truncate cursor-default" :title="weatherList[6]">{{ weatherList[6] }}</div>
          </div>
        </div>
        <div style="display: flex">
          <div class="middle-city">
            {{ cityInfo.city || '' }}
            <span v-if="cityInfo.county && cityInfo.city != cityInfo.county">/ {{ cityInfo.county || '' }}</span>
          </div>
          <div class="middle-city">{{ hours }}:{{ minutes }}更新</div>
        </div>
      </div>
      <div class="weather-info">
        <div class="weather-item">
          <div class="item-icon">
            <!-- <i :class="imgSrc" class="wt_icon"></i> -->
            <svg-icon :name="imgSrc" :size="16" color="#527CFF"></svg-icon>
            <div class="item">天气</div>
          </div>
          <div class="content">{{ weatherList[0] }}</div>
        </div>
        <div v-for="(item, index) in weatherInfo" :key="index" class="weather-item">
          <div class="item-icon">
            <!-- <i :class="imgArr[index]" class="wt_icon"></i> -->
            <svg-icon :name="imgArr[index]" :size="16"></svg-icon>
            <div class="item">{{ item || '--' }}</div>
          </div>
          <div class="content">{{ weatherList[index + 1] || '--' }}</div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="day">{{ safeDay }}<span class="day-num">天</span></div>
      <div class="satefy-day">已安全运行天数</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserInfo } from '~/store'
import config from '@/config'
import $API from '@/common/api'
import defaultAvatar from '@/assets/image/default_avatar.png'
import { useRouter } from 'vue-router'

const router = useRouter()
const ui: any = useUserInfo()
const weatherInfo = ['降水', '气温', '风向', '风速', '空气质量']
const weatherList = ref<any>([])
const imgArr = ['2121', '1009', '1702', '2208', '2413']
const imgSrc = ref<string>('')
const minutes = String(new Date().getMinutes()).padStart(2, '0')
const hours = String(new Date().getHours()).padStart(2, '0')
const loading = ref<boolean>(false)
const deptList = ref<any>([])
const cityInfo = ref<any>({})
const safeDay = ref<string>('0')
const userInitial = computed(() => {
  const name = ui.value.userName || ''
  return name.length >= 2 ? name.slice(-2) : name
})

const model = ref<string>('')
// 返回空气质量
function getAir(num: number) {
  return num < 10 ? '重度污染' : num < 20 ? '中度污染' : num < 30 ? '轻度污染' : num < 40 ? '良' : '优'
}
// 获取天气
function getWeather() {
  loading.value = true
  $API
    .get({
      url: 'ehs-clnt-platform-service/workbench/msg/nowWeather',
      params: {
        jdwd: '122.9941,41.1056',
      },
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        loading.value = false
        imgSrc.value = res.data.icon ? res.data.icon : '100'
        cityInfo.value = res.data
        weatherList.value = [
          // res.data.icon,
          res.data.text || '--',
          (res.data.precip || '--') + 'MM',
          (res.data.temp || '--') + '℃',
          res.data.windDir || '--',
          (res.data.windSpeed || '--') + 'KM/H',
          res.data.vis || '--',
          res.data.warnText,
        ]
        console.log('weatherList.value', weatherList.value)
      }
    })
}
// 获取安全天数
function getSafeDays() {
  $API
    .get({
      url: 'ehs-clnt-platform-service/workbench/msg/getSafeDays',
      params: { unitId: ui.value.unitId },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        safeDay.value = res.data.days || 0
      }
    })
}
// 获取部门列表
function getDeptList() {
  $API
    .post({
      url: 'ehs-clnt-platform-service/login/getUserAuthUnitDept',
    })
    .then(async (res: any) => {
      deptList.value = res.data
      model.value = ui.value.orgCode
      // changeDept(model.value)
    })
}
// 选择部门
function changeDept(value) {
  $API
    .post({
      url: 'ehs-clnt-platform-service/login/getUnitDetailInfoWithToken',
      params: {
        orgCode: value,
        client: 'platform',
      },
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        const d = res.data
        d.resourceVoList = res?.data?.resourceVoList.map((item) => {
          return {
            ...item,
            children: item.childrens,
          }
        })
        localStorage.removeItem('@@web_userInfo')
        sessionStorage.removeItem('@@web_userInfo')

        ui.value = d
        ui.value.resourceList = d?.resourceVoList
        ui.value.systemName = d?.systemName || '延长石油'
        localStorage.setItem('@@web_userInfo', JSON.stringify(ui.value))
        sessionStorage.setItem('@@web_userInfo', JSON.stringify({ userInfo: { ...ui.value } }))
        router.push('/staging')
        setTimeout(() => {
          window.location.reload()
        }, 300)
      }
    })
}

onMounted(() => {
  getWeather()
  getDeptList()
  getSafeDays()
})
</script>

<style lang="scss" scoped>
.top {
  width: 100%;
  height: 156px;
  // background: url('./assets/bj.png') no-repeat;
  // background-size: 100% 100%;
  display: flex;
  justify-content: space-between;

  .left {
    width: 27%;
    height: 100%;
    background: linear-gradient(139deg, #e9f1ff 0%, #eaf2ff 0%, #c7d4ff 100%);
    border: 0px solid #ffffff;
    border-radius: 10px;
    display: flex;
    padding-left: 33px;
    align-items: center;
    background: url('@/assets/image/user_bg.png') no-repeat;
    background-size: 100% 100%;

    .img {
      width: 72px;
      height: 72px;
      object-fit: cover;
      margin-right: 20px;
      border-radius: 50%;
      background-color: #527cff;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }

    .left-name {
      font-weight: 700;
      font-size: 20px;
      color: #222222;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 12px;
    }

    .left-dept {
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 28px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .select_dept {
      // width: 180px;
      margin-left: -13px;
      // padding-top: 5px;

      :deep(.el-select) {
        min-width: 100px;
        width: auto;

        //max-width: 212px;
        .el-select__wrapper {
          background-color: transparent !important;
          box-shadow: none;
        }

        .el-select__placeholder {
          font-weight: 400;
          font-size: 14px;
          color: #2b2d33;
        }
      }

      :deep(.el-select__caret) {
        color: #00030a;
      }
    }

    .img-text {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      background-color: #527cff;
    }
  }

  .right {
    width: 23%;
    height: 100%;
    border-radius: 10px;
    background: url('@/assets/image/safety-bg.png') no-repeat;
    background-size: 100% 100%;
    padding-left: 36px;
    padding-top: 32px;

    .day {
      font-family: D-DIN-PRO, D-DIN-PRO;
      font-weight: 600;
      font-size: 48px;
      color: #ffffff;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      text-transform: none;

      .day-num {
        font-size: 18px;
        margin-left: 10px;
      }
    }

    .satefy-day {
      font-family:
        Alibaba PuHuiTi 2,
        Alibaba PuHuiTi 20;
      font-weight: 400;
      font-size: 18px;
      color: #ffffff;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }

  .middle {
    flex: 1;
    margin: 0 10px;
    // border: 1px solid #300cc1;
    border-radius: 10px;
    background: url('@/assets/image/wehther.png') no-repeat;
    background-size: 100% 100%;
    padding: 18px 20px 20px 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .today-weather {
      display: flex;
      justify-content: space-between;

      .middle-city {
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-left: 10px;
      }

      .name {
        width: 4.5rem;
        margin-right: 14px;
        font-weight: 700;
        font-size: 18px;
        color: #222222;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .s-weather {
      display: flex;
      position: relative;
      flex: 1;
      width: 446px;
    }

    .weather-notice {
      // max-width: 276px;
      flex: 1;
      height: 23px;
      background: rgba(255, 25, 25, 0.2);
      border-radius: 3px 3px 3px 3px;
      padding: 0 8px;
      font-weight: 400;
      font-size: 12px;
      color: #e71616;
      line-height: 23px;
      text-align: left;
      font-style: normal;
    }

    .triangle {
      position: absolute;
      top: 5px;
      left: -7px;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 7px 7px 7px 0;
      border-color: transparent rgba(255, 25, 25, 0.2) transparent transparent;
    }

    .weather-info {
      display: flex;
      justify-content: space-between;

      .weather-item {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #ffffff;
        height: 76px;
        text-align: center;
        padding-top: 4px;
        flex: 1;
        margin-right: 10px;

        &:last-child {
          margin-right: 0;
        }

        .item-icon {
          display: flex;
          align-items: center;
          justify-content: center;

          .item {
            font-weight: 400;
            font-size: 14px;
            color: #4d4d4d;
            line-height: 24px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-left: 5px;
          }
        }

        .content {
          font-weight: 700;
          font-size: 16px;
          color: #222222;
          line-height: 24px;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }

        .wt_icon {
          color: rgba(108, 144, 255, 1);
        }
      }
    }
  }
}
</style>
