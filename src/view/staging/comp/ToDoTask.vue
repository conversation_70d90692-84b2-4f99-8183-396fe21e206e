<template>
  <div class="tasks">
    <div class="head_box h-56px">
      <div class="flex">
        {{ title }}
        <span v-if="title === '待办任务'"></span>
      </div>
      <div class="more" @click="goMore">更多</div>
    </div>
    <div class="content h-[352px]">
      <div v-if="title === '待办任务'" class="h-full w-full">
        <div class="h-full w-full">
          <div class="task-content grid-cols-6">
            <div
              v-for="(item, index) in list"
              :key="index"
              class="item-bg"
              :class="[index == curTabs ? 'w_active' : '']"
              @click="changeTab(item, index)"
            >
              <div class="num text-[#fff]">{{ item.messageCount || 0 }}</div>
              <div class="name">{{ item.typeName }}</div>
            </div>
          </div>
          <div class="info-content h-[calc(100%-120px)] scrollbar overflow-auto">
            <template v-if="info.length">
              <div v-for="(item, index) in info" :key="index" class="info-bg task" @click="jump(item)">
                <div class="img"></div>
                <div style="flex: 1; width: 0">
                  <div class="info mb-[10px]">
                    <div class="title truncate" :title="item.messageTitle">
                      {{ item.messageTitle }}
                    </div>
                    <div class="time">接收时间：{{ item.pushTime }}</div>
                  </div>
                  <div class="text truncate" :title="item.messageContent + item.strongMsgContent">
                    {{ item.messageContent }}<span class="text-content">{{ item.strongMsgContent }}</span>
                  </div>
                </div>
              </div>
            </template>
            <div v-else class="flex flex-1 items-center justify-center">
              <div class="flex flex-col align-center justify-center">
                <el-image :src="defaultPng"> </el-image>
                <div class="not-description text-[#969799] text-[14px] flex align-center justify-center">
                  暂无待办任务
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="h-full w-full">
        <div class="h-full w-full">
          <div class="task-content grid-cols-4">
            <div
              v-for="(item, index) in list"
              :key="index"
              class="event-bg"
              v-bind:class="item.class + (current == item.type ? ' active' : '')"
              :style="{ backgroundImage: `url(${getImg(item)})` }"
              @click="changeType(item)"
            >
              <div class="num">{{ item.messageCount || item.num || 0 }}</div>
              <div class="name">{{ item.typeName || item.name }}</div>
            </div>
          </div>
          <div class="info-content h-[calc(100%-120px)] scrollbar overflow-auto">
            <template v-if="info.length">
              <div
                v-for="(item, index) in info"
                :key="item.eventId || index"
                class="info-bg cursor-pointer"
                @click="goDetail(item)"
              >
                <div class="info-img"></div>
                <div style="flex: 1">
                  <div class="info mb-[10px]">
                    <div class="title truncate" :title="item.messageTitle">
                      {{ item.messageTitle || item.unitName }}
                    </div>
                    <div class="time">接收时间：{{ item.pushTime || item.iotReceiveTime || '' }}</div>
                  </div>
                  <div class="text truncate">
                    <myTooltip :str="getContentText(item)"></myTooltip>
                  </div>
                </div>
              </div>
            </template>
            <div v-else class="flex flex-1 items-center justify-center">
              <div class="flex flex-col align-center justify-center">
                <el-image :src="defaultPng"> </el-image>
                <div class="not-description text-[#969799] text-[14px] flex align-center justify-center">
                  暂无事件监测
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import firea from '@/assets/image/001-a.png'
import fire from '@/assets/image/001.png'
import warna from '@/assets/image/002-a.png'
import warn from '@/assets/image/002.png'
import faulta from '@/assets/image/003-a.png'
import fault from '@/assets/image/003.png'
import riska from '@/assets/image/004-a.png'
import risk from '@/assets/image/004.png'
import $API from '@/common/api'
import { ElMessage } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import config from '~/config'
import { useUserInfo } from '~/store'
import defaultPng from '../../staging/assets/default.png'

const bgObj = {
  1: risk,
  2: warn,
  3: fault,
  4: fire,
}
const aBgObj = {
  1: riska,
  2: warna,
  3: faulta,
  4: firea,
}
const userInfo = useUserInfo()

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  list: {
    type: Array as any,
    default: () => [],
  },
  info: {
    type: Array as any,
    default: () => [],
  },
  active: {
    type: String,
    default: '1',
  },
  infoActive: {
    type: Number,
    default: 0,
  },
})
const emits = defineEmits(['tabChange'])
const router = useRouter()
const current = computed(() => {
  return props.active
})

const curTabs = ref(props.infoActive)

function changeTab(item: any, index: number) {
  console.log(item, 'allala')
  curTabs.value = index
  emits('tabChange', item)
}

const goDetail = async (val) => {
  if (val.eventId) {
    const res = await $API.post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: 'iot_monitoring',
        userId: userInfo.value.id,
      },
    })

    let url = `${config.base_prefix}/ehs-internet-monitor/#/index?token=${res.data.token}&sysCode=iot_monitoring&eventType=${val.eventType}&eventId=${val.eventId}&page=/monitor/realTimeMonitorPage`
    window.open(url, '_blank')
  }
}
const changeType = (val) => {
  // current.value = val.type
  emits('tabChange', val)
}
const getContentText = (val) => {
  //
  if (val.messageContent || val.strongMsgContent)
    return val.messageContent + val.strongMsgContent + '，请控制安全风险。'
  return (
    val.buildingName +
    val.floorName +
    val.deviceAddress +
    val.deviceTypeName +
    '上报了' +
    val.description +
    '请及时前往查看, 请控制安全风险!'
  )
}

async function goMore() {
  if (props.title === '待办任务') {
    router.push({ name: 'notifyTask', state: { tab: '1' } })
  } else {
    // router.push({ name: 'notifyTask', state: { tab: '2' } })
    const res = await $API.post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: 'iot_monitoring',
        userId: userInfo.value.id,
      },
    })

    let url = `${config.base_prefix}/ehs-internet-monitor/#/index?token=${res.data.token}&sysCode=iot_monitoring&eventType=${current.value}&page=/monitor/realTimeMonitorPage`
    window.open(url, '_blank')
  }
}

// 读取消息
function readTask(item) {
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/readMessage`,
      params: {
        messageId: item.messageId,
        messageClass: '2',
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
      }
    })
}

function jump(item) {
  readTask(item)
  if (item.msgType == '9') {
    return ElMessage.warning('请使用招商随行APP或小程序进行学习')
  }
  if (!item.routePath) {
    return
  }
  if (item.messageTitle === '隐患复查') {
    ElMessage.warning('请前往移动端-隐患排查治理-隐患复查中处理')
    return
  }
  let goUrl
  $API
    .post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: item.sysCode,
        userId: userInfo.value.id,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        if (item.sysCode === 'scene_manage') {
          // 现场管理
          const path = item.routePath.split('#')[1]
          // 移除开头的所有/
          const cleanPath = path.replace(/^\/+/, '')
          goUrl =
            item.routePath.split('#')[0] + '?token=' + res.data.token + '&sysCode=' + item.sysCode + '#/' + cleanPath
        } else if (
          item.sysCode === 'risk_level' ||
          item.sysCode === 'hazard_inves' ||
          item.sysCode === 'safe-operation'
        ) {
          // 风险管控
          if (item.routePath.includes('?')) {
            goUrl = item.routePath + '&token=' + res.data.token + '&sysCode=' + item.sysCode
          } else {
            goUrl = item.routePath + '?token=' + res.data.token + '&sysCode=' + item.sysCode
          }
        } else {
          goUrl = item.routePath + '?token=' + res.data.token + '&sysCode=' + item.sysCode
        }
        window.open(goUrl, '_blank')
      }
    })
}

function getImg(item) {
  const type = item.type
  if (current.value == type) return aBgObj[type]
  return bgObj[type]
}

onMounted(() => {
  console.log('🚀 ~ onMounted ~ props.list:', props.list)
})

defineOptions({ name: 'HeadTab' })
</script>

<style scoped lang="scss">
.tasks {
  width: 49%;

  .head_box {
    background: linear-gradient(90deg, #e6ebf5 0%, #e0e7f7 100%);
    border-radius: 4px 4px 0 0;
    display: flex;
    padding: 0 24px;
    justify-content: space-between;
    align-items: center;

    .flex {
      font-family:
        Alibaba PuHuiTi 2,
        Alibaba PuHuiTi 20;
      font-weight: 600;
      font-size: 16px;
      color: #222222;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .more {
      font-weight: 400;
      font-size: 16px;
      color: #527cff;
      line-height: 24px;
      text-align: right;
      font-style: normal;
      text-transform: none;
      cursor: pointer;
    }
  }

  .content {
    background: rgba(238, 247, 255, 1);
    border-radius: 4px;
    padding: 24px;
    position: relative;
    min-height: 200px;

    .not {
      display: flex;
      align-items: center;
      justify-content: center;
      height: calc(100% - 48px);
      position: absolute;
      left: 50%;
      transform: translateX(-50%);

      .not-description {
        font-weight: 400;
        font-size: 14px;
        color: #969799;
        line-height: 22px;
        text-align: center;
      }
    }

    .task-content {
      display: grid;
      // grid-template-columns: repeat(5, 1fr);
      gap: 10px;

      .item-bg {
        height: 108px;
        background: linear-gradient(180deg, #ffffff 0%, #e5ecff 100%);
        box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
        border-radius: 10px 10px 10px 10px;
        border-image: linear-gradient(
            180deg,
            rgba(223.00000190734863, 230.00000149011612, 242.00000077486038, 1),
            rgba(255, 255, 255, 0)
          )
          1 1;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
        margin-bottom: 12px;
        cursor: pointer;

        .num {
          width: 60px;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
          // text-align: center;
          font-weight: 500;
          font-size: 20px;
          background: url('@/assets/image/task-bg.png') no-repeat;
          background-size: 115% 115%;
          background-position: center center;
        }

        .name {
          font-family:
            Alibaba PuHuiTi 2,
            Alibaba PuHuiTi 20;
          font-weight: 500;
          font-size: 14px;
          color: #222222;
          line-height: 20px;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }

      .event-bg {
        height: 108px;
        background: url('@/assets/image/event-bg.png') no-repeat;
        background-size: 100% 100%;
        border-radius: 8px 8px 8px 8px;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        margin-bottom: 12px;

        .num {
          font-family: D-DIN-PRO, D-DIN-PRO;
          font-weight: 500;
          font-size: 32px;
          color: #000;
          line-height: 32px;
        }

        .name {
          font-weight: 400;
          font-size: 16px;
          color: #000;

          line-height: 22px;
        }
      }

      .w_active {
        //background: red!important;
        background: #658afdff !important;

        .name {
          color: white !important;
        }
      }

      .active {
        .num {
          color: #fff;
        }

        .name {
          color: #fff;
        }

        &:active {
          box-shadow:
            0 0 0 rgba(0, 0, 0, 0.4),
            0 0 0 rgba(255, 255, 255, 0.9),
            inset -7px -7px 12px rgba(255, 255, 255, 0.9),
            inset 7px 7px 12px rgba(0, 0, 0, 0.4);
        }
      }
    }

    .info-content {
      display: flex;
      flex-direction: column;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar:horizontal {
        height: 6px;
      }

      &::-webkit-scrollbar-track {
        border-radius: 10px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: var(--el-border-color);
        border-radius: 10px;
        transition: all 0.2s ease-in-out;

        &:hover {
          cursor: pointer;
          background-color: var(--el-border-color);
        }
      }

      .task {
        cursor: pointer;
      }

      .info-bg {
        display: flex;
        align-items: center;
        height: 82px;
        background: #ffffff;
        border-radius: 10px 10px 10px 10px;
        border: 1px solid #dfe6f2;
        margin-bottom: 10px;
        padding: 16px;

        .img {
          width: 36px;
          height: 36px;
          margin-right: 10px;
          background: url('@/assets/image/task-list.png') no-repeat;
          background-size: 100% 100%;
        }

        .info-img {
          width: 36px;
          height: 36px;
          margin-right: 10px;
          background: url('@/assets/image/event-list.png') no-repeat;
          background-size: 100% 100%;
        }

        .info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;

          .title {
            flex: 1;
            width: 0;
            font-weight: 600;
            color: #222222;
            line-height: 20px;
            text-align: left;
          }

          .time {
            width: 220px;
            font-weight: 400;
            color: #666666;
            line-height: 14px;
            text-align: right;
          }
        }

        .text {
          font-weight: 400;
          font-size: 14px;
          color: #222222;
          line-height: 20px;
          text-align: left;

          .text-content {
            color: rgba(82, 124, 255, 1);
          }
        }
      }
    }
  }

  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: nowrap;
  }
}
</style>
