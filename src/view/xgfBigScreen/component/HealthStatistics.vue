<template>
    <div class="health">
        <TitleBar title="职业健康统计"/>
        <div class="_all_charts">
            <div class="_chartGre" v-for="(item, index) in chartList" :key="index">
                <ProgressChart 
                    :bgimg="false" 
                    :pgsStyle="true" 
                    :echartsData="item.data" 
                    :borderColor="item.colorGress"
                    :titleValueLeft="p.titleValueLeft"
                    :titleValueTop="p.titleValueTop"
                    :titleTextLeft="p.titleTextLeft"
                    :titleTextTop="p.titleTextTop"/>
                    <span class="_ch_con">{{item.title}}</span>
            </div>
        </div>
    </div>
    
</template>
<script setup lang="ts">
    import ProgressChart from '@/components/bigScreen/ProgressChart.vue';
    import TitleBar from '@/view/brainBigScreen/component/TitleBar.vue';

    const p = {
        titleValueLeft: '32%',
        titleValueTop: '40%',
        titleTextLeft: '53%',
        titleTextTop: '44%',
    }
    const chartList = [
        { colorGress: ['rgba(240,200,35, 0.5)', 'rgba(240,200,35, 0.8)'], title: '岗前体检完成率', data: 90 },
        { colorGress: ['rgba(35,146,244, 0.5)', 'rgba(35,146,244, 0.8)'], title: '在岗体检完成率', data: 84 },
        { colorGress: ['rgba(39,69,244, 0.5)', 'rgba(39,69,244, 0.8)'], title: '离岗体检完成率', data: 67 },
        { colorGress: ['rgba(234,15,43, 0.5)', 'rgba(234,15,43, 0.8)'], title: '体检异常', data: 16 },
        { colorGress: ['rgba(21,192,106, 0.5)', 'rgba(21,192,106, 0.8)'], title: '异常处理完成率', data: 70 },
    ]
</script>

<style scoped lang="scss">
    .health {
        color: #fff;

        ._all_charts {
            display: flex;
            align-items: center;
            margin-top: -25px;
            margin-bottom: 25px;

            ._chartGre {
                position: relative;
                width: 160px;
                height: 160px;
                margin-right: -65px;

                ._ch_con {
                    font-size: 14px;
                    position: absolute;
                    bottom: -10px;
                    left: 48px;
                    display: block;
                    width: 65px;
                    height: 38px;
                    text-align: center;
                }
            }
        }

        
    }
</style>
