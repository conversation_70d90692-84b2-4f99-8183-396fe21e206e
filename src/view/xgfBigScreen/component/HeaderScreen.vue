<template>
    <div class="header_con">
        <div class="_t"></div>
        <img class="_h" src="@/assets/image/header_bg.png">
        <div class="_header_r_tab">
            <div v-for="(item, index) in headList" :key="index" @click="handleTab(index)">
                <span :class="tIndex == index?'_header_r_span':''">{{ item }}</span>
                <img v-if="tIndex == index" src="@/assets/image/bigScreen/_tab_bg.png">
            </div>
        </div>

        <div class="back-cont">
            <div class="flex justify-center items-center cursor-pointer " @click="$router.go(-1)">
                <img src="@/assets/image/xgfBigScree/back.png" alt="" class="mr-[5px]"/>
                <span>返回工作台</span>
            </div>
          
        </div>
    </div>
</template>
<script lang="ts" setup>
    import { ref } from 'vue';

    const tIndex = ref(0);
    const headList = ref(['安全履职', '监测预警', '敏捷应急']);

    const handleTab = (i) => {
        tIndex.value = i;
    }
</script>

<style scoped lang="scss">
    .header_con {
        color: #fff;
        position: relative;
        z-index: 99999;
         
        .back-cont{
            position: absolute;
            top: 7%;
            right: 3%;

        }


        ._t {
            position: absolute;
            left: 1%;
            top: 10%;
            font-family: MyCustomFont, sans-serif;
            font-size: 36px;
            text-shadow: 0 2px 8px rgba(41,47,58,.05);
            background: linear-gradient(180deg,#a2e4ff,#e9f8ff 60%,#f8fdff);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        ._h {
            width: 100%;
            height: 121px;
        }

        ._header_r_tab {
            position: absolute;
            right: 22%;
            top: 0;
            width: 540px;
            height: 64px;
            line-height: 64px;
            display: flex;
            align-items: center;
            cursor: pointer;

            
            div {
                text-align: center;
                width: 180px;
                height: 64px;
                line-height: 64px;
                position: relative;

                img {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 82px;
                }

                span {
                    width: 100%;
                    height: 100%;
                    display: block;
                    font-family: MyCustomFont, sans-serif;
                    font-size: 28px;
                    text-shadow: 0 2px 8px rgba(41,47,58,.05);
                    background: linear-gradient(180deg,#559dcd,#e9f8ff 60%,#f8fdff);
                    -webkit-background-clip: text;
                    background-clip: text;
                    -webkit-text-fill-color: transparent;
                }

                ._header_r_span {
                    background: linear-gradient(180deg,#fff,#fff 60%,#fff);
                    -webkit-background-clip: text;
                    background-clip: text;
                    -webkit-text-fill-color: transparent;
                }
            }
        }
    }
</style>
