<template>
    <div class="evolution_trends">
        <TitleBar title="处罚企业情况"/>
        <div class="_fi_con flex justify-around">
          <div class="_con" v-for="(item, index) in tList" :key="index">
            <div>
              <span class="text-[30px]">{{ item.num }}</span>
              <span>{{ item.value }}</span>
            </div>
            <img class="_gltj" :src="arr[index]">
          </div>
        </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { defineComponent, ref } from 'vue';
  import TitleBar from '@/view/brainBigScreen/component/TitleBar.vue';
  import img0 from '@/assets/image/xgfBigScree/r0.png';
  import img1 from '@/assets/image/xgfBigScree/r1.png';

  let arr = [img0, img1];
  const barCharts = ref();
  const myChart = ref<any>(null);
  const tList = [
    { num: 235, value: '停用企业数' },
    { num: 17, value: '黑名单人员数' },
   
  ];

  defineComponent({ name: 'CerComp' });
  </script>
  
  <style scoped lang="scss">
  .evolution_trends {
    width: 100%;
    
    .charts_value {
      width: 100%;
      height: 280px;
    }

    ._fi_con {
      color: #fff;
      display: flex;
      align-items: center;
      padding-left: 20px;
      margin-bottom: 20px;
      margin-top: 20px;
      
      ._con {
        position: relative;
        margin-left: 5px;

        ._gltj {
          width: 164px;
          height: 95px;
        }
        

        div {
          width: 100%;
          position: absolute;
          top: -5px;
          left: 50%;
          transform: translate(-50%, 0);
          display: flex;
          flex-direction: column;
          align-items: center;
          font-size: 14px;

        
        }
      }
    }
  }
  </style>
  