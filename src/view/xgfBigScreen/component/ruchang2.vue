<template>
  <div class="_flip_number">
    <div class="_flip_num w-[850px]">
      <TitleBar2 title="培训课程类型分析" />
      <div class="w-[850px]">
        <div class="charts_value w-[850px]" ref="barCharts"></div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import TitleBar2 from './TitleBar2.vue'
import * as echarts from 'echarts'

import { ref, onMounted, markRaw } from 'vue'
const props = defineProps({
  days: {
    type: String,
    default: '003651',
  },
})

let imgList = ref([
  {
    name: '入场培训课程数',
    num: 365,
  },
  {
    name: '参训总人次',
    num: 1024,
  },
  {
    name: '已通过参训总人次',
    num: 365,
  },
  {
    name: '矩形 1185 拷贝 4',
    num: 365,
  },
])

const barCharts = ref()
const myChart = ref<any>(null)
const tList = [
  { num: 235, value: '未遂事件' },
  { num: 17, value: '人身伤害类' },
  { num: 89, value: '财产损失类' },
  { num: 26, value: '环保类' },
  { num: 37, value: '其他' },
]

function drawCharts() {
  myChart.value = markRaw(echarts.init(barCharts.value))
  const option = {
    // grid: {
    //   left: '50',
    //   right: '50',
    // },
    // legend: {
    //   textStyle: {
    //     color: '#fff',
    //   }
    // },
    xAxis: {
      type: 'category',
      data: [
        '东鞍山铁矿',
        '眼前山井矿',
        '齐大山选矿',
        '齐大山矿厂',
        '大孤山球团厂',
        '大孤山球团厂',
        '大孤山球团厂',
        '大孤山球团厂',
      ],
      textStyle: {
        color: '#FFFFFF',
      },
      axisLabel: {
        interval: 0,
        color: '#fff',
        // rotate: -15  //文字过多时，倾斜角度
      },
    },
    yAxis: [
      {
        type: 'value',
        textStyle: {
          color: '#FFFFFF',
        },
        axisLine: {
          lineStyle: {
            color: '#fff',
          },
        },
      },
      {
        type: 'value',
        textStyle: {
          color: '#FFFFFF',
        },
        axisLine: {
          lineStyle: {
            color: '#fff',
          },
        },
      },
    ],
    series: [
      {
        name: '警情总数（起）',
        type: 'bar',
        barWidth: 22,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#15a9e1' },
            { offset: 0.5, color: '#138dd1' },
            { offset: 1, color: '#138dd1' },
          ]),
        },
        data: [35, 75, 33, 66, 40, 65, 80, 55],
        z: 2,
      },
      {
        z: 2,
        name: '',
        type: 'pictorialBar',
        data: [1, 1, 1, 1, 1, 1, 1, 1],
        symbol: 'diamond',
        symbolOffset: [0, '50%'],
        symbolRotate: 90,
        symbolSize: [12, 22],
        itemStyle: {
          color: '#188df0',
        },
      },
      {
        z: 3,
        name: '警情总数（起）',
        type: 'pictorialBar',
        // 柱子顶部
        symbolPosition: 'end',
        data: [35, 75, 33, 66, 40, 65, 80, 55],
        symbol: 'diamond',
        symbolOffset: [0, -27],
        symbolRotate: 90,
        symbolSize: [12, 23],
        itemStyle: {
          normal: {
            borderWidth: 20,
            borderColor: '#176FBA',
            color: '#000',
          },
        },
        tooltip: {
          show: false,
        },
      },
      {
        name: '环比',
        type: 'line',
        yAxisIndex: 1,
        itemStyle: {
          normal: {
            color: '#d06c68',
            borderColor: '#fff',
            borderWidth: 2,
          },
        },
        symbol: 'diamond', // 设置拐点为菱形
        symbolSize: 11,
        // data: [45, 67, 34, 28, 76, 19, 23, 43, 99, 100, 104, 108],
      },
      {
        name: '同比',
        type: 'line',
        yAxisIndex: 1,
        itemStyle: {
          normal: {
            color: '#d4d545',
            borderColor: '#fff',
            borderWidth: 2,
          },
        },
        symbol: 'diamond', // 设置拐点为菱形
        symbolSize: 11,
        // data: [38, 45, 65, 23, 56, 67, 88, 54, 67, 34, 28, 76],
      },
    ],
  }
  myChart.value.setOption(option)
  window.addEventListener('resize', () => {
    if (myChart.value) myChart.value.resize()
  })
}
onMounted(() => {
  drawCharts()
})
</script>

<style scoped lang="scss">
._flip_number {
  ._flip_num {
    position: absolute;
    left: 50%;
    top: 80%;
    transform: translate(-50%, -50%);
  }
}

.img0 {
  background: url(../../../assets/image/xgfBigScree/bg.png) no-repeat center center;
  background-size: cover;
}

.tt {
  font-size: 30px;
  font-weight: bold;
  color: #fff;
  line-height: 30px;
}

.tt2 {
  font-size: 13px;
  color: #fff;
  line-height: 30px;
}

.noeadd {
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.charts_value {
  height: 280px;
}
</style>
