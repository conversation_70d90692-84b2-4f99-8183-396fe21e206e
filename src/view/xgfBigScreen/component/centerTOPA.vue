<template>
  <div class="_flip_number">
    <div class="_flip_num flex w-[850px]">
      <div class="w-[200px] h-[70px] text-[#fff] pt-[15px] pl-[80px]" :class="'img'+index" v-for="item,index in imgList">
        <div class="tt pl-[10px]">{{item.num}}</div>
        <div>{{item.name}}</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
// import img0 from '@/assets/image/xgfBigScree/b0.png'
// import img1 from '@/assets/image/xgfBigScree/b1.png'
// import img2 from '@/assets/image/xgfBigScree/b2.png'
// import img3 from '@/assets/image/xgfBigScree/b3.png'

import { ref } from 'vue'
const props = defineProps({
  days: {
    type: String,
    default: '003651'
  }
})

let imgList = ref([
    {
    name:'相关方企业总数',
    num:365
}, {
    name:'相关方累计人数',
    num:365
}, {
    name:'服务项目总数',
    num:365
}, {
    name:'在途项目数',
    num:365
},
])
</script>

<style scoped lang="scss">
._flip_number {
  ._flip_num {
    position: absolute;
    left: 50%;
    top: 14%;
    transform: translate(-50%, -50%);
  }
}

.img0 {
  background: url(../../../assets/image/xgfBigScree/b0.png) no-repeat center
    center;
  background-size: cover;
}
.img1 {
    background: url(../../../assets/image/xgfBigScree/b1.png) no-repeat center
    center;
  background-size: cover;
}
.img2 {
    background: url(../../../assets/image/xgfBigScree/b2.png) no-repeat center
    center;
  background-size: cover;
}
.img3 {
    background: url(../../../assets/image/xgfBigScree/b3.png) no-repeat center
    center;
  background-size: cover;
}
.tt{
  font-family: D-DIN-PRO;
font-weight: bold;
font-size: 38px;
color: #4CCAFF;
line-height: 30px;
background: linear-gradient(0deg, #44BFFF 0%, #FFFFFF 53.759765625%);
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;
}

</style>
