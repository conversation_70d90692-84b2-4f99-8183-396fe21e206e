<template>
    <div class="h-[350px]">
        <TitleBar title="相关方作业违规前五名" />
        <tableCom/>
    </div>
</template>
<script setup lang="ts">

    import TitleBar from './TitleBar.vue';
    import tableCom from './tableCom3.vue';

    const ecData = 76;
    const colorGress = ['rgba(153, 42, 116, 0.5)', 'rgba(153, 42, 116, 0.8)'];
</script>

<style scoped lang="scss">
    ._yin_content {
        width: 100%;
        color: #fff;
        display: flex;
        justify-content: space-around;
        margin-top: -20px;

        ._gre_con {
            display: flex;
            align-items: center;
            font-size: 12px;
            width: 120px;
            height: 120px;
            position: relative;
            margin-left: -20px;
            margin-right: 35px;

            ._con_ge {
                position: absolute;
                right: -50px;
                top: 35px;
                width: 70px;
                display: flex;
                flex-direction: column;

                span:nth-child(1) {
                    font-size: 20px;
                    font-weight: 600;
                }
            }
        }
        
        ._yin_air {
            display: flex;
            align-items: center;
            margin-left: 10px;

            img {
                width: 69px;
                height: 69px;
            }

            ._yin_statics {
                font-size: 12px;
                width: 50px;
                display: flex;
                flex-direction: column;

                span:nth-child(1) {
                    font-weight: 800;
                    font-size: 16px;
                }
            }
        }
    }

    ._y_check {
        color: #96d4ec;
        font-size: 14px;
        text-align: right;
        margin-top: -20px;
    }
</style>
