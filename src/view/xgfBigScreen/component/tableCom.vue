<template>
    <div class="science-other">
      <!-- <div class="main"></div> -->
      <div class="data pl-[10px]">
        <div class="table-wrap">
          <div class="table-header">
            <div class="table-td">矿厂</div>
            <div class="table-td"></div>
            <div class="table-td">人数占比</div>
            <div class="table-td">相关方人数</div>
         
            <!-- <div class="table-td">操作</div> -->
          </div>
          <div class="table-body ">
            <div class="table-th" v-for="(item, index) in rowList" :key="index">
              <div class="table-td">
                <!--              <marquee behavior="scroll" direction="up">西安威盛电子科技股份有限公司</marquee>-->
               东鞍山铁矿
              </div>
              <div class="table-td w-[300px]"> 
                <el-progress :percentage="item.num" :format="format"  :color="getColor"  class="pt-[10px] w-[220px]" />
              </div>
              <div class="table-td">{{item.num}}%</div>
              <div class="table-td">22</div>
            
              <!-- <div class="table-td"><span>查看详情</span></div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import {ref} from 'vue';
  import { defineComponent } from 'vue';
 function format(){
   return ''
 }

 function getColor(num:number){
   if(num>50){
     return '#FF9900'
   }else{
     return '#0099FF'
   }
 }


 let rowList = ref([
   {
   num:82,
   },
   {
   num:72,
   },
   {
   num:62,
   },
   {
   num:32,
   },
   {
   num:12,
   },
   {
   num:12,
   },
   {
   num:12,
   },
   {
   num:12,
   },
   {
   num:12,
   },
 ])
  defineComponent({ name: 'ScienceOther' });
  </script>
  
  <style scoped lang="scss">
  .science-other {
    @apply w-full h-auto flex flex-col justify-between;
  
    .title {
      @apply w-full h-[57px];
      background: url('./assets/title.png') no-repeat center;
    }
  
    // .main {
    //   @apply w-full h-[281px] mt-[22px];
    //   background: url('./assets/data-bg.png') no-repeat center;
    // }
    .data {
      @apply w-full h-[311px] mx-auto;
      //background: url('./assets/data-bg.png') no-repeat center;
      .table-wrap {
        height: 300px;
        .table-header {
          border-bottom: 1px solid #4699f1;
          background: linear-gradient(0deg, #022E5B 0%, #0B63AE 100%);
        }
        .table-body {
          height: 240px;
          overflow: auto;
          &::-webkit-scrollbar {
            width: 0;
            background: transparent;
          }
        }
        .table-header,
        .table-th {
          display: flex;
          height: 34px;
          line-height: 34px;
          text-align: center;
          .table-td {
            box-sizing: border-box;
            padding: 0 10px;
            text-align: center;
            white-space: nowrap;
            font-size: 12px;
            color: #e9eff4;
            &:nth-of-type(1) {
              margin-left: 5px;
              padding-left: 0;
            }
            $widthList: (
              1: 250,
              2: 350,
              3: 150,
              4: 150,
              5: 150,
              6: 150,
              7: 150,
              8: 150,
            );
            @each $key, $width in $widthList {
              &:nth-of-type(#{$key}) {
                width: $width + px;
              }
            }
          }
        }
        .table-th {
        //   &:nth-of-type(2n) {
        //     background: rgba(22, 49, 74, 0.4);
        //   }
          .table-td {
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            &:nth-of-type(8) {
              span {
                color: #19aedd;
              }
            }
          }
        }
      }
    }
  }
  </style>
  