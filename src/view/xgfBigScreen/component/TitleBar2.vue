<template>
    <div>
        <div class="_yinhuan2">
            <div class="_title">{{ title }}</div>
            <img class="_xczytj" src="@/assets/image/bigScreen/_xczytj_bg.png">
            <div class="_tab" v-if="showTab">
                <div v-for="(item, index) in tab" :key="index" @click="clickTab(index)">
                    <span :class="tIndex === index?'_s':''">{{ item }}</span>
                    <img class="_fx" v-if="tIndex === index" src="@/assets/image/bigScreen/_fx_bg_active.png">
                    <img class="_fx" v-else src="@/assets/image/bigScreen/_fx_bg.png">
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
    import { ref } from 'vue';

    const props = defineProps({
        title: {
            type: String,
            default: ''
        },
        showTab: {
            type: Boolean,
            default: false
        }
    });

    const tab = ref([]);
    const tIndex = ref(0);

    const clickTab = (i: number) => {
        tIndex.value = i;
    }
</script>

<style scoped lang="scss">
    ._yinhuan2 {
        position: relative;
        color: #fff;

        ._title {
            font-family: sans-serif;
            font-size: 24px;
            text-shadow: 0 2px 8px rgba(41,47,58,.05);
            background: linear-gradient(180deg,#bbe7fe,#e9f8ff 60%,#f8fdff);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            position: absolute;
            top: -6px;
            left: 9%;
            font-synthesis: style;
            font-style: italic;
        }

        ._xczytj {
            width: 100%;
            height: 53px;
        }

        ._tab {
            display: flex;
            align-items: center;
            position: absolute;
            right: 2px;
            top: 1px;

            div {
                position: relative;
                font-size: 14px;
                width: 74px;
                height: 32px;
                color: #5c758d;
                cursor: pointer;

                span {
                    position: absolute;
                    top: 5px;
                    left: 9px;
                }

                ._s {
                    color: #fff;
                }

                img {
                    width: 74px;
                    height: 32px;
                }
            }
        }
    }
</style>
