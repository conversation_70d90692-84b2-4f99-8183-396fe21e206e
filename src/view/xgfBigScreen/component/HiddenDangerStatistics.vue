<template>
  <div>
    <TitleBar title="在途相关方情况" :showTab="true" />
    <div class="_yin_content">
        <div class="h-[200px] flex justify-around  pt-[30px] title1">
            <div class="one-cont flex h-[80px] " v-for="item,index in 4">
                <img :src="imgArr[index].img" alt="">
                <div class="w-[120px] pt-[15px]">
                    <div class="text-[24px] font-bold">
                        <span>{{imgArr[index].num}}</span>
                        <span class="text-[12px]">{{imgArr[index].unit}}</span>
                    </div>
                    <div>{{imgArr[index].name}}</div>
                </div>
            </div>
        </div>
      </div>
  </div>
</template>
<script setup lang="ts">
import img0 from "@/assets/image/xgfBigScree/a1.png"
import img1 from "@/assets/image/xgfBigScree/a2.png"
import img2 from "@/assets/image/xgfBigScree/a3.png"
import img3 from "@/assets/image/xgfBigScree/a4.png"

import TitleBar from '@/view/brainBigScreen/component/TitleBar.vue'
let imgArr = [{
    img: img0,
    name: '在途企业数',
    unit:'',
    num:198
  },
  {
    img: img1,
    name: '在途员工数',
    unit:'',
    num:19
  },
  {
    img: img2,
    name: '人员占比',
    unit:'%',
    num:25
  },
  {
    img: img3,
    name: '在途项目数',
    unit:'',
    num:69
  },
 ]
const ecData = 47
</script>

<style scoped lang="scss">
._yin_content {
  width: 100%;
  color: #fff;
  display: flex;
  justify-content: space-around;
  margin-top: -28px;
   .title1{
    flex-wrap: wrap;
   }
  ._gre_con {
    display: flex;
    align-items: center;
    font-size: 12px;
    width: 120px;
    height: 120px;
    position: relative;
    margin-left: -20px;
    margin-right: 35px;

    ._con_ge {
      position: absolute;
      right: -50px;
      top: 35px;
      width: 70px;
      display: flex;
      flex-direction: column;

      span:nth-child(1) {
        font-size: 20px;
        font-weight: 600;
      }
    }
  }

  ._yin_air {
    display: flex;
    align-items: center;
    margin-left: 10px;

    img {
      width: 69px;
      height: 69px;
    }

    ._yin_statics {
      font-size: 12px;
      width: 50px;
      display: flex;
      flex-direction: column;

      span:nth-child(1) {
        font-weight: 800;
        font-size: 16px;
      }
    }
  }
}

._y_check {
  color: #96d4ec;
  font-size: 14px;
  text-align: right;
  margin-top: -20px;
}
</style>
