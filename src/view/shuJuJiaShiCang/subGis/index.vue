<template>
  <div class="yzt overflow-hidden">
    <div class="text-align-center topheader">
      <topHeader :isShowTab="false"></topHeader>
    </div>
    <div class="safeMap">
      <div class="echart-box">
        <gisPage ref="ElGisPage"></gisPage>
      </div>
      <div class="right" v-if="!nodetails">
        <div class="isShowBtn-show" v-if="showOverview">
          <!-- <img src="@/assets/image/unitMap/letft-btn.png" > -->
          <div class="img-bg-box" @click="showOverview = false"></div>
        </div>
        <div class="isShowBtn-hidden" v-if="!showOverview">
          <div class="img-bg-box" @click="showOverview = true"></div>
        </div>
        <Transition name="slide-fade">
          <div class="details-box" v-if="showOverview && !isShowDetails">
            <leftPopo @closePopor="closePopor"></leftPopo>
            <!-- <unitDetails></unitDetails> -->
          </div>
          <div class="details-box" v-else-if="showOverview && isShowDetails">
            <unitDetails></unitDetails>
          </div>
        </Transition>
      </div>
    </div>
    <!-- <div class="menu-box">
      <chartMenu></chartMenu>
    </div> -->
    <!-- <div class="floorMap-box">
      <floorMap></floorMap>
    </div> -->
    <!-- <toggleBtn /> -->
  </div>
</template>

<script lang="ts" setup name="fireremoteManage-unitmap">
import gisPage from './components/gisPage.vue'
import topHeader from '../components/header.vue'
import leftPopo from './components/leftPopo.vue'
import unitDetails from './components/unitDetails.vue'
// import chartMenu from '@/components/echartMap/mapMenu.vue'
// import toggleBtn from '../chartMap/toggleBtn.vue'
import { ref, computed, onMounted, watch, nextTick, onActivated } from 'vue'
import { useRoute } from 'vue-router'

import { unitMapStore } from '~/store'
const unitMapData = unitMapStore()

const route = useRoute()
const nodetails: any = route.path.includes('/fireRemoteManage/fireremoteManageUnitmap')

const showOverview = ref(true)

onActivated(() => {
  unitMapData.isShowMapDetail = false
})
// 地图参数
const ElGisPage = ref()

const currentGisUnit = computed(() => unitMapData.currentGisUnit)
const isShowDetails = computed(() => unitMapData.isShowMapDetail)
watch(isShowDetails, () => {
  showOverview.value = false
  setTimeout(() => {
    showOverview.value = true
  }, 500)
})
watch(currentGisUnit, (n: any) => {
  if (n.value && isShowDetails.value) {
    showOverview.value = false
    setTimeout(() => {
      showOverview.value = true
    }, 300)
  }
})
onMounted(() => {
  // console.log('unitMapData-----------',unitMapData)
  // console.log('unitMapData-----------',unitMapData.gisUnit)
  unitMapData.isShowMapDetail = false
  init()
  nextTick(() => {
    // console.log('route-----------',route.query)
    let unitdata = route.query
    if (unitdata && unitdata.unitId) {
      ElGisPage.value.showUnitPopup(unitdata)
    }
    // PubSub.publish('SAFEMAPTODETAILS', )
  })
})

const init = () => {
  // getdataOverview()
}
const closePopor = () => {
  // getdataOverview()
  showOverview.value = false
}
</script>

<style lang="scss" scoped>
.slide-fade-enter-active {
  transition: transform 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: transform 0.3s ease-out;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(100%);
}

.yzt {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #0c1836;

  .menu-box {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    height: 30px;
    width: calc(100% - 900px);
  }
}

.floorMap-box {
}

.mapTopCard-box {
  position: absolute;
  top: 115px;
  left: 50%;
  z-index: 3;
  transform: translateX(-50%);
}

.topheader {
  position: relative;
  z-index: 9;
}

.safeMap {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.alarm-list {
  flex-shrink: 0;
  height: 435px;
}

.right {
  position: absolute;
  right: 15px;
  top: 95px;
  min-width: 30px;
  z-index: 9;
  display: flex;
  flex-direction: column;
  height: 85%;

  .details-box {
    height: 100%;
  }

  .isShowBtn-show {
    position: absolute;
    left: 0px;
    top: 40%;
    width: 24px;
    transform: translateX(-100%);

    .img-bg-box {
      background-image: url(@/assets/image/unitMap/letft-btn.png);
      width: 24px;
      height: 38px;
      background-size: 100% 100%;
    }

    .img-bg-box:hover {
      background-image: url(@/assets/image/unitMap/letft-btn-active.png);
    }
  }

  .isShowBtn-hidden {
    position: absolute;
    right: 0px;
    top: 40%;
    width: 24px;
    transform: rotateY(180deg) translateX(-15px);

    .img-bg-box {
      background-image: url(@/assets/image/unitMap/letft-btn.png);
      width: 24px;
      height: 38px;
      background-size: 100% 100%;
    }

    .img-bg-box:hover {
      background-image: url(@/assets/image/unitMap/letft-btn-active.png);
    }
  }

  // &>div{
  //   flex: 1;
  // }
}

.indexRank-box {
  overflow: hidden;
  min-height: 280px;
}

.echart-box {
  // background-color: #eee;
  height: 100%;

  .gisMap {
    width: 100%;
    height: 100%;
  }
}
</style>
