<template>
  <div class="floorGis-box">
    <div class="main" :class="{ full: isFull }" v-loading="floorMapLoading">
      <div class="headder" v-if="!isShowFloorMap">
        <div class="header-left"></div>

        <div class="header-right">
          <div>
            <img v-if="!isFull" src="@/assets/image/unitMap/full1.png" @click="full()" />
            <img v-else src="@/assets/image/unitMap/exit-full1.png" @click="full()" />
          </div>

          <div class="close" @click="close">
            <img src="@/assets/image/unitMap/close-black.png" />
          </div>
        </div>
      </div>

      <div class="indoorContent h-[calc(100%)]">
        <div ref="ElIndoorMap" v-show="!isShowFloorMap && !isShowNoImg" style="height: 100%; width: 100%"></div>
        <div class="NoImg" v-show="isShowNoImg">
          <div>
            <img src="@/assets/image/unitMap/no_area_img.png" alt="" />
            <div class="text">单位尚未采集鸟瞰图信息</div>
          </div>
        </div>
        <!-- 鸟瞰图组件 -->

        <!-- 室内GIS组件 -->
        <floorMap
          v-if="isShowFloorMap"
          :isFull="isFull"
          :buildList="buildList"
          :currentBuildInfo="currentBuildInfo"
          :currentUnit="currentGisUnit"
          @goback="showIndoorMap"
          @close="close"
          @full="full"
          ref="ElfloorMap"
        ></floorMap>

        <!-- 加载 -->

        <!-- 无地图 -->

        <!-- 视频 -->
      </div>
    </div>

    <el-dialog
      lock-scroll
      :close-on-click-modal="false"
      align-center
      custom-class="custom-video-popup"
      v-model="videoVisible"
      :title="'地址：' + videoAddress"
      width="1000px"
      draggable
    >
      <video-component v-if="videoVisible" style="width: 1000px; height: 500px" :video-url="videoUrl"></video-component>
      <template #footer>
        <span class="dialog-footer">
          <!-- <el-button @click="videoVisible = false">取消</el-button> -->
          <el-button type="primary" @click="videoVisible = false"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- <div class="video-box"  v-if='isShowLinkage' @click="isShowLinkage = false">
      <div class="video-box-item">
        <linkage v-if='isShowLinkage' :isOnlyOne ='isOnlyOne'  :linkageData = 'linkageData'></linkage>
      </div>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import floorMap from './floorMap.vue'
import { setPopup } from '@/components/gisPage/unitTip/index.js'
import { unitMapStore } from '~/store'
import $API from '~/common/api'
import PubSub from 'pubsub-js'
import config from '~/config'
import { getVideoUrl } from '@/common/services'
import { gisIndoorMap, initDom, CONST_GSUrlHeader } from './gisGloba'

const unitMapData = unitMapStore()
defineProps({
  //地图初始参数
  options: {
    type: Object,
    default: () => {},
  },
  userInfo: {
    type: Object,
    default: () => {},
  },
  buildId: {
    type: String,
    default: '',
  },
  //默认加载鸟瞰图
  defaultShowOverview: {
    type: Boolean,
    default: true,
  },
})
const currentGisUnit = computed<any>(() => unitMapData.currentGisUnit)
const currentUnitInfo = ref<any>({})

// 鸟瞰图&楼层图 公共部分
let indoorMap: any = null
const videoVisible = ref(false)
const videoAddress = ref('')
const videoUrl: any = ref('')
const isFull = ref(false)
const isShowNoImg = ref(false)

// 画布
const ElIndoorMap = ref()
const floorMapLoading: any = ref(false)

// 楼栋

const currentBuildInfo = ref({
  ownerId: '',
  subCenterCode: '',
  buildId: '',
  ownerType: 0,
})
const buildList = ref<any[]>([])

// 楼层
const deviceInfo = ref<any>({})
const isShowFloorMap = ref(false)

const emits = defineEmits(['close'])

const close = () => {
  emits('close')
}
const full = () => {
  isFull.value = !isFull.value
}

const getMonitorBuildInfo = () => {
  // 获取楼栋数据
  const params = {
    ownerId: currentUnitInfo.value.ownerId,
    subCenterCode: currentUnitInfo.value.subCenterCode,
    modelType: 'gis_serve_url',
  }
  return $API.post({
    url: '/bitmap/queryDeviceNumsByUnitId',
    params,
  })
}

const mapOnLoad = async () => {
  let res = await getMonitorBuildInfo()
  buildList.value = res.data
  initLayer()
}

const initLayer = () => {
  indoorMap.setTileVisible(false)
  indoorMap.setBGVisible(true)

  indoorMap.showFloorData(
    currentUnitInfo.value.aerialMapType || -2, //数据类型
    // -3,
    currentUnitInfo.value.ownerId, //单位ID
    null, //楼栋ID（鸟瞰图不填）
    '', //楼层ID（鸟瞰图不填）
    CONST_GSUrlHeader.gis_image_url + currentUnitInfo.value.aerialviewImg, //鸟瞰图地址
    function (mapType, success) {
      floorMapLoading.value = false
      //加载完成事件
      //加载第三方数据
      if (mapType === IndoorMap.ViewType.OVUnitModel) {
        //模型请求成功
        indoorMap.showOVDataBuild(
          indoorMap.getIndoorDataState().source.unitModelInfo,
          'modelInfoPointX',
          'modelInfoPointY',
          'modelInfoPointZ',
          function (data, markFieldNameX, markFieldNameY, markFieldNameZ) {
            // ControlSky()
            if (
              typeof data[markFieldNameX] !== 'number' ||
              typeof data[markFieldNameY] !== 'number' ||
              typeof data[markFieldNameZ] !== 'number'
            )
              return true

            // 设置mark的样式
            let _buildData: any = {}
            buildList.value.forEach((i) => {
              i.isShowAll = false
              if (data.buildId == i.buildingId) {
                i.modelInfoPointX = data.modelInfoPointX
                i.modelInfoPointY = data.modelInfoPointY
                i.modelInfoPointZ = data.modelInfoPointZ
                _buildData = i
              }
            })

            data.imageStyle = ''
            if (_buildData.alarmNum > 0) {
              data.imageStyle = IndoorMap.StateType.Calltheplice //火警数
            } else if (_buildData.warningNum > 0) {
              data.imageStyle = IndoorMap.StateType.Warning //预警数
            } else if (_buildData.troubleNum > 0) {
              data.imageStyle = IndoorMap.StateType.Abnormal //故障数
            } else if (_buildData.faultNum > 0) {
              data.imageStyle = IndoorMap.StateType.Fault //隐患数
            } else if (_buildData.movingNum > 0) {
              data.imageStyle = IndoorMap.StateType.Action //动作数
            } else if (_buildData.offlineNum > 0) {
              data.imageStyle = IndoorMap.StateType.Offline //离线数
            } else {
              data.imageStyle = IndoorMap.StateType.Normal
            }
            addBuildTip(_buildData)
          },
          false,
          -1024326
        )
        //
        let _videoRes = indoorMap.showOVDataVideoOnLoadGeoData(
          mapType,
          success,
          CONST_GSUrlHeader.gis_serve_url + '/deviceEvent/queryVideoMonitorByPage', //3.0
          'ownerId=' + currentUnitInfo.value.ownerId + '&ownerType=' + '0' + '&pageNo=1&pageSize=-1', //3.0
          'POST',
          undefined,
          function (data, markFieldNameX, markFieldNameY) {
            if (markFieldNameX in data && markFieldNameY in data /*&& markFieldNameZ in data*/) {
              data.imageStyle = 'vline'
              if (typeof data[markFieldNameX] === 'string') data[markFieldNameX] = Number(data[markFieldNameX])
              if (typeof data[markFieldNameX] === 'string') data[markFieldNameY] = Number(data[markFieldNameY])
              // if(typeof data[markFieldNameX] === 'string') data[markFieldNameZ] = Number(data[markFieldNameZ]);
            } else {
              return true
            }
          }
        )
        _videoRes.onLoad = function () {
          console.log('_videoRes----_videoRes.onLoad')
        }
      } else if (mapType === IndoorMap.ViewType.OVUnitImage) {
        //模型请求失败，自动切换鸟瞰图
        let _videoRes = indoorMap.showOVDataVideoOnLoadGeoData(
          mapType,
          success,
          CONST_GSUrlHeader.gis_serve_url + '/deviceEvent/queryVideoMonitorByPage', //3.0
          'ownerId=' + currentUnitInfo.value.ownerId + '&ownerType=' + '0' + '&pageNo=1&pageSize=-1', //3.0
          'POST',
          undefined,
          function (data, markFieldNameX, markFieldNameY) {
            if (markFieldNameX in data && markFieldNameY in data /*&& markFieldNameZ in data*/) {
              data.imageStyle = 'vline'
              if (typeof data[markFieldNameX] === 'string') data[markFieldNameX] = Number(data[markFieldNameX])
              if (typeof data[markFieldNameX] === 'string') data[markFieldNameY] = Number(data[markFieldNameY])
              // if(typeof data[markFieldNameX] === 'string') data[markFieldNameZ] = Number(data[markFieldNameZ]);
            } else {
              return true
            }
          }
        )
        _videoRes.onLoad = function () {
          console.log('_videoRes---- _videoRes.onLoad')
        }

        // ownerId: currentUnitInfo.value.ownerId,
        // subCenterCode: currentUnitInfo.value.subCenterCode,
        indoorMap.showOVDataBuildOnLoadGeoData(
          mapType,
          success,
          CONST_GSUrlHeader.gis_serve_url + '/bitmap/queryDeviceNumsByUnitId',
          // 'http://www.gsafetycloud.com/api/v2/operation-management-service/plan/eventWL/getTAndAByUnitId',
          'subCenterCode=' + currentUnitInfo.value.subCenterCode + '&ownerId=' + currentUnitInfo.value.ownerId,
          'POST',
          undefined,
          function (data) {
            data.imageStyle = ''
            data.imageStyle = ''
            if (data.alarmNum > 0) {
              data.imageStyle = IndoorMap.StateType.Calltheplice //火警数
            } else if (data.warningNum > 0) {
              data.imageStyle = IndoorMap.StateType.Warning //预警数
            } else if (data.troubleNum > 0) {
              data.imageStyle = IndoorMap.StateType.Fault //隐患数
            } else if (data.faultNum > 0) {
              data.imageStyle = IndoorMap.StateType.Abnormal //故障数
            } else if (data.movingNum > 0) {
              data.imageStyle = IndoorMap.StateType.Action //动作数
            } else if (data.offlineNum > 0) {
              data.imageStyle = IndoorMap.StateType.Offline //离线数
            } else {
              data.imageStyle = IndoorMap.StateType.Normal
            }
            addBuildTip(data)
          }
        )
      } else if (mapType === window.IndoorMap.ViewType.OVBuildArea) {
        indoorMap.setTileVisible(true)

        indoorMap.showOVDataBuild(
          indoorMap.getIndoorDataState().source.ovBuildArea.map((i: any) => {
            return {
              ...i,
              mapX: i.modelInfoPointX || i.mapX,
              mapY: i.modelInfoPointY || i.mapY,
              mapZ: i.modelInfoPointZ || i.mapZ,
            }
          }),
          'mapX',
          'mapY',
          'mapZ',
          function (data: any, markFieldNameX: number, markFieldNameY: number, markFieldNameZ: number) {
            if (typeof data[markFieldNameX] !== 'number' || typeof data[markFieldNameY] !== 'number') return true
            // // 设置mark的样式
            // data.imageStyle = window.IndoorMap.StateType.Normal;
            // 设置mark的样式

            let _buildData: any = {}
            buildList.value.forEach((i) => {
              i.isShowAll = false
              if (data.buildId == i.buildingId) {
                i.modelInfoPointX = data.modelInfoPointX
                i.modelInfoPointY = data.modelInfoPointY
                i.modelInfoPointZ = data.modelInfoPointZ
                _buildData = { ...data, ...i }
              }
            })

            data.imageStyle = ''
            if (_buildData.alarmNum > 0) {
              data.imageStyle = IndoorMap.StateType.Calltheplice //火警数
            } else if (_buildData.warningNum > 0) {
              data.imageStyle = IndoorMap.StateType.Warning //预警数
            } else if (_buildData.troubleNum > 0) {
              data.imageStyle = IndoorMap.StateType.Abnormal //故障数
            } else if (_buildData.faultNum > 0) {
              data.imageStyle = IndoorMap.StateType.Fault //隐患数
            } else if (_buildData.movingNum > 0) {
              data.imageStyle = IndoorMap.StateType.Action //动作数
            } else if (_buildData.offlineNum > 0) {
              data.imageStyle = IndoorMap.StateType.Offline //离线数
            } else {
              data.imageStyle = IndoorMap.StateType.Normal
            }
            addBuildTip(_buildData)
          },
          false,
          4326
        )
      }
    }
  )
}

const showFloorMap = (data) => {
  currentBuildInfo.value.ownerId = data.unitId
  currentBuildInfo.value.buildId = data.buildId || data.buildingId
  isShowFloorMap.value = true
}

const addBuildTip = (data) => {
  let key = data.buildingId
  let _container = document.createElement('div')
  _container.classList.add('Build-popup')
  _container.innerHTML = `<div id="BuildPopup"></div>`
  _container.setAttribute('key', key)
  // latitude: 1231651.064118
  // longitude: 555140.42294

  if (gisIndoorMap.getViewType() == -3) {
    let pPoint = IndoorMap.projectXY_FromTo([data.mapX, data.mapY], 4326, IndoorThree.CONST_SRID_Default)
    // p.show([pPoint[0], pPoint[1], data.mapZ])
    data.modelInfoPointZ = data.mapZ

    data.gs_Popup = sceneAddBuildTip(
      {
        x: pPoint[0],
        y: pPoint[1],
        z: data.mapZ || 0,
      },
      _container,
      key,
      data
    )
    return
  }

  if (data.modelInfoPointX && data.modelInfoPointY) {
    data.gs_Popup = sceneAddBuildTip(
      {
        x: data.modelInfoPointX,
        y: data.modelInfoPointY,
        z: data.modelInfoPointZ || 0,
      },
      _container,
      key,
      data
    )
  } else {
    let pPoint = new GISShare.SMap.Geometry.Point(data.longitude, data.latitude)
    // GISShare.SMap.Fitting.BD09.BD09MC_To_BD09LL_Geo(pPoint)
    data.gs_Popup = sceneAddBuildTip(
      {
        x: pPoint.getX(),
        y: pPoint.getY(),
        z: data.modelInfoPointZ || 0,
      },
      _container,
      key,
      data
    )
  }
}
const AllPopup: any = {}
const sceneAddBuildTip = (coordinate, _temp, id, data) => {
  let p = gisIndoorMap.addPopup({
    width: 32,
    height: 32,
    custom: true,
    offset: [0, -50],
    positioning: 'bottom-center',
    element: _temp,
  })
  let _docu = p.getElement()
  setPopup({
    elment: _docu.firstChild,
    options: {
      onClick: setIsShow,
      data: data,
      hasBtn: true,
    },
  })
  AllPopup[data.buildingId] = p

  p.show([coordinate.x, coordinate.y, coordinate.z || 0])
  p.Update()
  // setTimeout(() => {
  //   gisIndoorMap.setCenter(gisIndoorMap.getCenter())
  // }, 500)

  // unitInfoMap.buildTipMap[id] = p;
  // bindTipEvent();
  return p
}
const setIsShow = (data, target) => {
  const elms = document.querySelectorAll('.Build-popup')
  console.log('🚀 ~ setIsShow ~ elms:', elms)

  for (let i = 0; i < elms.length; i++) {
    const ele: any = elms[i]
    const buildBodyEle: any = ele.querySelector('.gg-build-body')
    const unitpopupEle: any = ele.querySelector('.unitpopup')
    const downIcon: any = ele.querySelector('.down')
    const upIcon: any = ele.querySelector('.up')
    if (ele.getAttribute('key') != target.getAttribute('key')) {
      downIcon.style.display = 'none'
      upIcon.style.display = 'block'
      ele.parentNode.style.zIndex = 1
      unitpopupEle.classList.remove('showAll')
      buildBodyEle.style.display = 'none'
    } else {
      // console.log(upIcon, buildBodyEle, unitpopupEle)
      if (buildBodyEle.style.display == 'block') {
        downIcon.style.display = 'none'
        upIcon.style.display = 'block'
        ele.parentNode.style.zIndex = 1
        unitpopupEle.classList.remove('showAll')
        buildBodyEle.style.display = 'none'
      } else {
        downIcon.style.display = 'block'
        upIcon.style.display = 'none'
        ele.parentNode.style.zIndex = 10
        unitpopupEle.classList.add('showAll')
        buildBodyEle.style.display = 'block'
      }
    }
  }
  for (let i in AllPopup) {
    AllPopup[i].Update()
  }
}

const mapEvent = () => {
  //模型绑定加载事件（监听加载过）
  indoorMap.onOvUnitModelLoading = function (e) {
    const { count, num } = e
    if (count == num) {
      floorMapLoading.value = false
    }
  }
  //可用建筑模型 点击事件
  indoorMap.onOVUnitModelSelected = showFloorMap
  //楼栋点位图标 点击事件
  indoorMap.onOVBuildSelected = showFloorMap
  //视频点位图标 点击事件
  indoorMap.onOVVideoSelected = function (data) {
    openVideo(data)
    return false
  }
}
const _initMap = () => {
  currentUnitInfo.value.ownerId = currentGisUnit.value.value
  currentUnitInfo.value.subCenterCode = currentGisUnit.value.subCenterCode
  currentUnitInfo.value.aerialMapType = currentGisUnit.value.aerialMapType
  currentUnitInfo.value.aerialviewImg = currentGisUnit.value.aerialviewImg
  currentUnitInfo.value.ownerType = currentGisUnit.value.eventType
  currentBuildInfo.value.ownerId = currentUnitInfo.value.ownerId
  currentBuildInfo.value.ownerType = currentGisUnit.value.eventType
  currentBuildInfo.value.subCenterCode = currentGisUnit.value.subCenterCode
  if (currentUnitInfo.value.aerialMapType != -2 && !currentUnitInfo.value.aerialviewImg) {
    isShowNoImg.value = true
    return
  }
  floorMapLoading.value = true
  // initMap()
  indoorMap = gisIndoorMap
  ;(window as any).IndoorSceneMap = indoorMap
  nextTick(() => {
    initDom(ElIndoorMap.value)
  })
  mapEvent()
  // indoorMap.clearAll()
  indoorMap.clearPopup()
  // indoorMap.clearOVBuild()
  mapOnLoad()
}

//
//天空显示控制
//
const ControlSky = () => {
  var viewType = indoorMap.getViewType()
  if (
    viewType == IndoorMap.ViewType.IndoorAreaVector25D ||
    viewType == IndoorMap.ViewType.IndoorAreaVector3DM ||
    viewType == IndoorMap.ViewType.OVBuildArea ||
    viewType == IndoorMap.ViewType.OVUnitModel
  ) {
    indoorMap.setBGVisible(true) //室内GIS平面图2.5D和3DM以及鸟瞰图模型模式和白模模式打开天空
  } else {
    indoorMap.setBGVisible(false)
  }
}

const openVideo = async (rows: any) => {
  videoAddress.value = rows.deviceAddress
  videoUrl.value = ''
  try {
    let videoObj = await getVideoUrl(rows)
    videoUrl.value = videoObj
    videoVisible.value = true
  } catch (error) {
    console.error(error, '序列化userInfo报错')
  }
}

const showIndoorMap = () => {
  isShowFloorMap.value = false
  initdata()
  // initMap()
}
//外部可访问的函数

function initdata() {
  isShowNoImg.value = false
  isShowFloorMap.value = false
  _initMap()
}

onUnmounted(() => {
  try {
    PubSub.unsubscribe(SENDDEVICEINFO)
  } catch (e) {
    //TODO handle the exception
  }
})

onMounted(() => {
  initdata()
})

defineExpose({
  initdata,
})
</script>

<style lang="scss" scoped>
.indoorContent {
  position: relative;
  width: 100%;

  .NoImg {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;

    .text {
      color: rgba(0, 0, 0, 0.8);
      margin-top: 10px;
    }

    img {
      width: 400px;
    }
  }
}

.mapContainer {
  width: 100%;
  height: 100%;
}

.mapContainer-floor {
  width: 100%;
  height: 100%;
  padding-top: 39px;
}

.floorGis-box {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  // background-color: rgba(0,0,0,.1);
  z-index: 11;
  display: flex;
  align-items: center;
  justify-content: center;

  .close {
    padding: 5px 10px;
    // background-color: red;
  }

  .video-box {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: red;

    .video-box-item {
      width: 50%;
      height: 50%;
      background-color: #0000ff;
    }
  }

  .headder {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    left: 0;
    top: 0;
    height: 30px;
    width: 100%;
    background-color: transparent;
    z-index: 12;

    .header-left {
      min-width: 15px;
      height: 30px;
      display: flex;
      align-items: center;
      padding-left: 10px;

      img {
        width: 25px;
      }
    }

    .header-right {
      display: flex;
      justify-content: space-between;
      align-items: center;

      img {
        width: 25px;
        margin-right: 10px;
      }

      .close {
        cursor: pointer;

        img {
          width: 16px;
        }
      }
    }
  }
}

.main {
  width: 80%;
  height: 85%;
  background-color: #fff;
  position: relative;
}

.full {
  width: 100%;
  height: 100%;
}
</style>

<style>
/* .gs-build-monitor{
    position: relative;
    background: #fff;
    width: 210px;
    border: 5px solid #318ff8;
}
.gs-build-monitor::after{
    content: "";
    width: 0;
    height: 0;
    position: absolute;
    left: 50%;
    bottom: -29px;
    z-index: 10;
    transform: translateX(-50%);
    border: 11px solid transparent;
    border-width: 13px;
    border-top-color: #318ff8;
}
.gg-build-monitor .gg-build-head .build-head-name {
	display: inline-block;
	width: 165px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	text-align: center;
}

.gs-build-head{
    width: 100%;
    height: 38px;
    background: #318ff8;
    line-height: 38px;
    font-size: 16px;
    color: #fff;
    padding: 0 15px;
    position: relative;
}
.gsicon-arrow{
    position: absolute;
    right: 10px;
    top: 10px;
    height: 16px;
    width: 16px;
    cursor: pointer;
}.gsicon-arrow-open{
    position: absolute;
    right: 10px;
    top: 10px;
    height: 16px;
    width: 16px;
    cursor: pointer;
    transform: rotate(180deg);
}
.gsicon-arrow-txt{
    position: absolute;
    top: 11px;
    right: 10px;
    width: 16px;
    line-height: 16px;
    color: #fff;
    font-size: 14px;
    transform: rotate(90deg);
    
}
.gs-build-body{
    padding: 0 15px;
}
.gs-build-body-close{
    display: none;
}
.gs-build-devices{
    padding: 0 0 3px 0;
    border-bottom: 1px solid #b0b0b0;
}
.build_state_num{
    margin-left: 8px;
}
.total_stateNum{
    font-size: 16px;
    line-height: 24px;
}
.gs-build-alarm{
    padding: 3px 0 0 0;
}
.build_stateNum{
    display: flex;
    flex-flow: row nowrap;
    width: 100%;
    justify-content: space-between;
    line-height: 24px;
} */
</style>
