import config from '~/config'
const base_prefix = import.meta.env.VITE_BASE_PREFIX

const CONST_GSCache = {
  adminCodeDicCache: new DicCache(50),
  indoorAreaDicCache_Map: new DicCache(16),
  indoorAreaDicCache_Scene: new DicCache(16),
  indoorAreaExtentDicCache_Map: new DicCache(32),
  indoorAreaExtentDicCache_Scene: new DicCache(32),
  gridAreaDicCache_Map: new DicCache(16),
  gridAreaDicCache_Scene: new DicCache(16),
  ovUnitModelInfoDicCache_Scene: new DicCache(3),
}
export const CONST_GSUrlHeader = {
  // gsUrlHeader: config.base_host + config.serviceName,
  gsUrlHeader: base_prefix,
  gis_image_url: base_prefix + '/img1/floorImage/',
  gis_serve_url: base_prefix + '/api/v3/bw-svc-enterprise-gis-service',
  dbService: base_prefix, //'https://agjp.tanzervas.com/aqsc/v1',
  dbService_Record: base_prefix, // 'https://agjp.tanzervas.com/aqsc/v1',
}
const CONST_GSOptions = {
  tile: false, //底图设置
  dbService: newIndoorService(CONST_GSUrlHeader.dbService + '/api/v3/bw-svc-indoor-gis-service/indoorMap'),
  dbService_Record: newIndoorService(CONST_GSUrlHeader.dbService_Record + '/api/v3/bw-svc-indoor-gis-service/record'),
  unitUrlHeader: CONST_GSUrlHeader.gsUrlHeader + '/img1/floorImage',
  deviceIconUrlHeader: CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/_v3.0',
  sky: false,
  deviceIconAlarmGifUrl: CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/gif/alarm.gif',
  skyUrl: [
    CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/z/sky/box/5/Front.JPG',
    CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/z/sky/box/5/Back.JPG',
    CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/z/sky/box/5/Up.JPG',
    CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/z/sky/box/5/Down.JPG',
    CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/z/sky/box/5/Left.JPG',
    CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/z/sky/box/5/Right.JPG',
  ],
  ovUnitModelUrlHeader: CONST_GSUrlHeader.gsUrlHeader + '/img1/indoor',
  wmsURL: CONST_GSUrlHeader.gsUrlHeader + '/geoserver/GS/wms',
  videoBufferQueryVideoTypeCode: '25030000',
  deviceFieldNameState: 'priorityEventType', // eventType priorityEventType
  deviceFieldNameOnlineState: undefined,
  deviceStateValueConvertFun: CONST_Function_DeviceStateValueConvertFun_Default_3,
  gridLoad: false,
  center: [0, 0],
  deviceIconAlarmGifler: true,
  indoorAreaDicCache: CONST_GSCache.indoorAreaDicCache_Scene,
  indoorAreaExtentDicCache: CONST_GSCache.indoorAreaExtentDicCache_Scene,
  gridAreaDicCache: CONST_GSCache.gridAreaDicCache_Scene,
  ovUnitModelInfoDicCache: CONST_GSCache.ovUnitModelInfoDicCache_Scene,
  deviceIconUrlFilter: function (deviceIconUrl: string, data: any) {
    if (data.deviceTypeId === this.videoBufferQueryVideoTypeCode) {
      return {
        src: this.videoBufferQueryVideoImage,
        size: this.videoBufferQueryVideoImageSize,
        anchor: this.videoBufferQueryVideoImageAnchor,
      }
    }
  },
}

const dom = document.createElement('div')
// dom.className = 'h-full w-full';
dom.style.height = '100%'
dom.style.width = '100%'
dom.style.overflow = 'hidden'
dom.style.position = 'relative'
const currentSubCenterCode = ''
const option: any = IndoorMap.Merge([
  CONST_GSOptions,
  {
    target: dom, //绑定的标签位置
    sky: true,
    skyUrl: [
      config.gisSkyUrl + '/Front.JPG',
      config.gisSkyUrl + '/Back.JPG',
      config.gisSkyUrl + '/Up.JPG',
      config.gisSkyUrl + '/Down.JPG',
      config.gisSkyUrl + '/Left.JPG',
      config.gisSkyUrl + '/Right.JPG',
    ],
    tile: true, //底图设置
    onLoad: () => {}, //加载完成事件
    ovUnitModelUrlHeader: config.gisUnitModel,
    ovUnitModelActivatable: true, //模型可激活
    indoorAreaDicCache: CONST_GSCache.indoorAreaDicCache_Scene,
    indoorAreaExtentDicCache: CONST_GSCache.indoorAreaExtentDicCache_Scene,
    gridAreaDicCache: CONST_GSCache.gridAreaDicCache_Scene,
    ovUnitModelInfoDicCache: CONST_GSCache.ovUnitModelInfoDicCache_Scene,
  },
])
IndoorThree.init()

export const gisIndoorMap = new IndoorThree(option)
;(window as any).gisIndoorMap = gisIndoorMap
export const initDom = (traget: HTMLElement) => {
  traget.appendChild(dom)
}
// todo
