<template>
  <div class="floorTab">
    <div class="floorTab-bg"></div>

    <div class="floorTab-btn-box">
      <div class="btn up" @click="up"></div>
    </div>

    <div class="item-box" ref="itemBox">
      <div ref="innerRef" :style="innerStyle">
        <div ref="innerRef" class="floor-item-box" v-for="(item, index) in floorList" :key="index">
          <!-- <div class="item-tip-box">
            <template v-for="(tipItem) in tipList">
              <div v-if="item && item[tipItem.key] > 0" :class="tipItem.key" :key='tipItem.key'>
                {{ item[tipItem.key] }}
              </div>
            </template>
          </div> -->

          <div class="floor-item" :class="{ active: currentFloorid == item.floorId }" @click="floorChange(item)">
            <span>
              {{ item.floorName }}
            </span>
          </div>
        </div>
        <!-- <div  class="floor-item" v-for='(item,index) in 50' :class="{'active': currentFloorid == item}"
          @click="floorChange(item)">
          <span>
            {{item}}
          </span>
        </div> -->
      </div>
      <!-- <el-scrollbar  ref="scrollbarRef"  @scroll="scroll" >
       <div ref="innerRef" class="floor-item" v-for='(item,index) in floorList' :class="{'active': currentFloorid == item.floorId}"
          @click="floorChange(item)">
          <span>
            {{item.floorName}}
          </span>
        </div>
        
        
         
        </el-scrollbar> -->
    </div>
    <div class="floorTab-btn-box">
      <div class="btn down" @click="down"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, reactive, watch, nextTick } from 'vue'
import { ElScrollbar } from 'element-plus'

const max = ref(0)
const scrollValue = ref(0)
const innerRef = ref<HTMLDivElement>()
const itemBox = ref<HTMLDivElement>()
const scrollbarRef = ref<InstanceType<typeof ElScrollbar>>()

const props = withDefaults(defineProps<{ floorList: any[] }>(), {
  floorList: () => [],
})

const currentFloorid = ref('')
onMounted(() => {
  max.value = innerRef.value!.clientHeight - itemBox.value!.clientHeight

  if (props.floorList.length > 0) {
    currentFloorid.value = (props.floorList[0] as any)?.floorId
  }
})
let innerStyle = computed(() => {
  return `transform: translateY(-${scrollValue.value}px);`
})
//     transform: translateY();
//外部可访问的事件回调
const emit = defineEmits(['floorChange'])
const tipList = reactive([
  {
    lable: '火警',
    key: 'alarmNum',
    eventType: '1',
  },
  {
    lable: '预警',
    key: 'warningNum',
    eventType: '2',
  },
  {
    lable: '故障',
    key: 'faultNum',
    eventType: '3',
  },
  {
    lable: '隐患',
    key: 'troubleNum',
    eventType: '4',
  },
  {
    lable: '动作',
    key: 'movingNum',
    eventType: '5',
  },
  {
    lable: '离线',
    key: 'offlineNum',
    onlineState: '1',
  },
])
const up = () => {
  scrollValue.value = scrollValue.value - itemBox.value!.clientHeight
  if (scrollValue.value < 0) {
    scrollValue.value = 0
  }
  // scrollbarRef.value!.setScrollTop(scrollValue.value)
}
const down = () => {
  scrollValue.value = scrollValue.value + itemBox.value!.clientHeight
  if (scrollValue.value > max.value) {
    scrollValue.value = max.value
  }
  // scrollbarRef.value!.setScrollTop(scrollValue.value)
}
const floorChange = (floor) => {
  if (currentFloorid.value == floor.floorId) return
  currentFloorid.value = floor.floorId
  emit('floorChange', floor)
}
const setActive = (floor: any = {}) => {
  currentFloorid.value = floor?.floorId || ''
}

// const onMeasureLength = () => {
//     emit('onMeasureLength')
// }

// function setMapToolbarButtonClassName(bActived:boolean){
//     return bActived ? "mapToolbar-button-actived" : "";
// }
defineExpose({
  setActive,
})
</script>

<style lang="scss" scoped>
.floorTab {
  border-radius: 10px;
  padding: 15px 0px;
  font-size: 12px;
  // width: 55px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  padding: 0 10px;
  position: relative;
  padding-right: 0;

  .floorTab-bg {
    width: 55px;
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    background-color: #fff;
    border-radius: 8px;
  }

  .item-box {
    flex: 1;
    width: 100%;
    overflow-y: hidden;
    margin: 15px 0;
  }

  .floorTab-btn-box {
    width: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .btn {
    width: 0;
    height: 0;
    border: 12px solid;
    flex-shrink: 0;
  }

  .up {
    border-color: transparent transparent rgba(217, 217, 217, 1) transparent;
  }

  .down {
    border-color: rgba(217, 217, 217, 1) transparent transparent transparent;
  }

  .up:hover {
    border-color: transparent transparent rgba(128, 128, 128, 1) transparent;
  }

  .down:hover {
    border-color: rgba(128, 128, 128, 1) transparent transparent transparent;
  }

  .floor-item-box {
    width: 100%;
    position: relative;
    display: flex;
    justify-content: end;
  }

  .floor-item {
    padding: 10px 0;
    text-align: center;
    font-size: 12px;
    cursor: pointer;
    width: 55px;
  }

  .item-tip-box {
    // position: absolute;
    // left: 0;
    // top: 50%;
    display: flex;
    align-items: center;

    & > div {
      height: 10px;
      width: 10px;
      margin-right: 2px;
      border-radius: 100%;
    }

    .alarmNum {
      // 火警
      background: red;
    }

    .warningNum {
      // 预警
      background: #e320c5;
    }

    .faultNum {
      // 故障
      background: #ffb74d;
    }

    .troubleNum {
      // 隐患
      background: #ffed2f;
    }

    .movingNum {
      // 动作

      background: #079afd;
    }

    .offlineNum {
      // 离线
      background: #949494;
    }
  }

  .floor-item:hover {
    background-color: rgba(48, 147, 250, 0.1);
    border-radius: 8px;
  }

  .active {
    background-color: #3093fa !important;
    border-radius: 8px;
    width: 55px;
  }
}
</style>
