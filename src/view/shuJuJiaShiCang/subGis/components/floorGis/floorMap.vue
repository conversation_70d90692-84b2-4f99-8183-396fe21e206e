<template>
  <div class="indoorContent" style="height: 100%" v-loading="isFloorLoading">
    <div class="header-box flex">
      <div class="left flex-1">
        <div class="back" @click="goback" v-if="!showDevicceInfo">
          <img src="@/assets/image/unitMap/back.png" alt="" />
        </div>
        <div class="flex items-center min-w-220px">
          <span class="flex-shrink-0"> 楼栋: </span>
          <el-select v-model="selectBuildId" @change="buildChange" filterable placeholder="楼栋">
            <el-option
              v-for="item in buildList"
              :key="item.buildingId"
              :label="item.buildingName"
              :value="item.buildingId"
            />
          </el-select>
        </div>
        <div class="cascader-box">
          <span> 设备类型 </span>
          <el-cascader
            ref="DeviceTypeCasRef"
            filterable
            placement="bottom-start"
            :options="deviceTypeOptions"
            v-model="deviceType"
            @change="deviceTypeChange"
            :props="cascaderProps"
            clearable
          >
            <template #default="{ node }">
              <div class="opacity-0"></div>
              <div class="custom-node" track @click="elCascaderOnClick">
                {{ node.label }}
              </div>
            </template>
          </el-cascader>
        </div>
        <div class="flex items-center min-w-240px">
          <span class="flex-shrink-0"> 监测状态: </span>
          <el-select v-model="eventType" @change="setStatus" filterable clearable placeholder="监测状态">
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </div>
      <div class="right">
        <!-- <div>
          <el-button type="primary" @click='initMap'>搜索</el-button>
        </div> -->
        <div class="full" @click="full">
          <img v-if="!_isFull" src="@/assets/image/unitMap/full.png" />
          <img v-else src="@/assets/image/unitMap/exit-full.png" />
        </div>
        <div class="close" @click="close">
          <img src="@/assets/image/unitMap/close-black.png" />
        </div>
      </div>
    </div>
    <div ref="ElfloorMap" class="floorMap" v-show="!isShowNofloorImg"></div>
    <div class="nofloorImg" v-show="isShowNofloorImg">
      <div>
        <img src="@/assets/image/unitMap/no_area_img.png" alt="" />
        <div class="mt-10px text">暂无楼层数据</div>
      </div>
    </div>
    <div class="card" style="background-color: #fff">
      <template v-for="n in cardList" :key="n.key">
        <div track @click="setType(n)" :class="[{ cardActive: acrdActive == n.key }, n.class]">
          <div class="num">
            {{ n.key ? currentbuildt[n.key] || 0 : '' }}
          </div>
          <div>
            {{ n.lable }}
          </div>
        </div>
      </template>
    </div>
    <!-- 楼层切换 -->
    <div class="floorList-box" v-if="floorList.length > 0">
      <floorTab :floorList="floorList" @floorChange="floorChange" ref="elFloorTab"></floorTab>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { setPopup } from '@/components/gisPage/floorTip/index.js'

import $API from '~/common/api'
import floorTab from './floorTab.vue'
import config from '~/config'

import { useUserInfo } from '~/store'
import PubSub from 'pubsub-js'

import { gisIndoorMap, initDom } from './gisGloba'

const userInfo = useUserInfo()
const currentGisUnit = ref<any>({})
const ElfloorMap = ref()
const isFirst = ref(false)
const props = defineProps({
  //默认加载鸟瞰图
  currentBuildInfo: {
    type: Object,
    default: () => {},
  },
  currentUnit: {
    type: Object,
    default: () => {},
  },
  isFull: {
    type: Boolean,
    default: false,
  },
  deviceInfo: {
    type: Object,
    default: () => {},
  },
  showDevicceInfo: {
    type: Boolean,
    default: false,
  },
})
const buildList = ref<any[]>([])
const acrdActive = ref('')
const isFloorLoading: any = ref(false)
const elFloorTab: any = ref('')

const statusList = reactive([
  //   {
  //   value: '',
  //   label: '全部'
  // },
  {
    value: '0',
    label: '正常',
  },
  {
    value: '1',
    label: '火警',
  },
  {
    value: '2',
    label: '预警',
  },
  {
    value: '3',
    label: '故障',
  },
  {
    value: '4',
    label: '隐患',
  },
  {
    value: '5',
    label: '动作',
  },
  {
    value: '7',
    label: '离线',
  },
])

const cardList = reactive([
  {
    lable: '设备',
    key: 'deviceCount',
  },
  {
    lable: '巡检点位',
    key: 'bluetoothCount',
  },
  {
    lable: '',
    key: '',
  },
  {
    lable: '火警',
    key: 'alarmNum',
    eventType: '1',
    class: 'cursor-pointer',
  },
  {
    lable: '预警',
    key: 'warningNum',
    eventType: '2',
    class: 'cursor-pointer',
  },
  {
    lable: '故障',
    key: 'faultNum',
    eventType: '3',
    class: 'cursor-pointer',
  },
  {
    lable: '隐患',
    key: 'troubleNum',
    eventType: '4',
    class: 'cursor-pointer',
  },
  {
    lable: '动作',
    key: 'movingNum',
    eventType: '5',
    class: 'cursor-pointer',
  },
  {
    lable: '离线',
    key: 'offlineNum',
    onlineState: '1',
    class: 'cursor-pointer',
  },
])

const cascaderProps = {
  checkStrictly: true,
  expandTrigger: 'hover',
}

const DeviceTypeCasRef: any = ref(null)
const _isFull = ref(false)
const floorList = ref<any[]>([])
const buildInfo = ref<any>({})
const currentFloorInfo = ref({})
const selectBuildId = ref('')
const deviceTypeOptions = ref([])
const selectDeviceType = ref([])
const deviceType = ref([])

const eventType = ref('')
const onlineState = ref('')
const isShowNofloorImg = ref(false)

const isMeasure = ref(false)
const isVideoBufferQuery = ref(false)
let indoorFloorMap
const floorMapId = 'floorMap' + new Date().getTime()
const emits = defineEmits(['goback', 'close', 'full'])
const goback = () => {
  emits('goback')
}
const close = () => {
  emits('close')
}
const full = () => {
  _isFull.value = !_isFull.value
  emits('full')
}

// goback=

onMounted(() => {
  initFloorMap()
})

const currentbuildt = ref<any>({})
const createMap = () => {
  nextTick(() => {
    indoorFloorMap = gisIndoorMap
    initDom(ElfloorMap.value)
    mapEvent()
    getFloorListByBuildId()
  })
}

const getMonitorBuildInfo = async () => {
  const params = {
    fields: 'buildingName,buildingId',
    unitId: currentGisUnit.value.value,
    modelType: 'gis_serve_url',
  }
  let res = await $API.post({
    url: '/deviceEvent/getBuildingListByUnitId',
    params,
  })
  if (buildInfo.value.buildId) {
    res.data.forEach((i) => {
      if (i.buildingId == buildInfo.value.buildId) {
        selectBuildId.value = buildInfo.value.buildId
      }
    })
  }

  buildList.value = res.data
}

const buildChange = (val) => {
  // 楼栋筛选
  buildInfo.value.buildId = val
  selectDeviceType.value = []
  deviceType.value = []
  if (!indoorFloorMap) return
  if (props.deviceInfo?.floorId && isFirst.value) return
  getFloorListByBuildId()
}
const deviceTypeChange = (val) => {
  selectDeviceType.value = val
  if (!indoorFloorMap) return
  showfloorMap(currentFloorInfo.value)
  setTimeout(() => {
    DeviceTypeCasRef.value && DeviceTypeCasRef.value.togglePopperVisible(false)
  }, 160)
}
const setStatus = () => {
  acrdActive.value = ''
  if (!indoorFloorMap) return
  showfloorMap(currentFloorInfo.value)
}
const setType = (item) => {
  console.log(item)
  if (!indoorFloorMap) return
  if (!item.eventType && !item.onlineState) {
    return
  }
  acrdActive.value = item.key
  if (item.eventType) {
    eventType.value = item.eventType
    onlineState.value = ''
  }
  if (item.onlineState) {
    eventType.value = '7'
  }
  showfloorMap(currentFloorInfo.value)
}

const setVideoBufferQuery = () => {
  //设置安消联动
  // indoorFloorMap.setVideoBufferQuery(isVideoBufferQuery.value);
  // setTimeout(()=>{
  //   indoorFloorMap.videoBufferQuery(180);
  // },3000)
  isVideoBufferQuery.value = !isVideoBufferQuery.value
  // indoorFloorMap.videoBufferQueryAll(isVideoBufferQuery.value)
}
const setMeasure = () => {
  //设置测量模式 1 长度   2面积
  isMeasure.value = !isMeasure.value
  if (!isMeasure.value) {
    indoorFloorMap.clearMeasure()
    indoorFloorMap.setMeasure(null)
  } else {
    indoorFloorMap.setMeasure(1)
  }
}

const getFireDeviceSystemType = async () => {
  let params = {
    orgCode: userInfo.value.orgCode,
    unitId: currentGisUnit.value.value,
    buildingId: selectBuildId.value,
    floorId: currentbuildt.value.floorId,
    modelType: 'unit_base_url',
  }

  let res
  res = await $API.post({
    url: '/monitor/getFireDeviceSystemType',
    params,
  })
  console.log('deviceTypeOptions=========', res)
  const index = res.data.options.findIndex((i) => i.value == '-1')
  res.data.options.splice(index, 1)
  deviceTypeOptions.value = res.data.options
}

const getFloorListByBuildId = async () => {
  floorList.value = []
  const params = {
    ownerId: buildInfo.value.ownerId,
    buildId: buildInfo.value.buildId,
    ownerType: buildInfo.value.ownerType || 0,
    subCenterCode: buildInfo.value.subCenterCode,
    modelType: 'gis_serve_url',
  }
  let res
  res = await $API.post({
    url: '/bitmap/queryFloorInfoAndDeviceNums',
    params,
  })

  floorList.value = res.data

  if (props.showDevicceInfo && isFirst.value) {
    // 地图页面点击 传参设备信息  直接展示当前设备
    let flooObj = floorList.value.find((i) => i.floorId == props.deviceInfo.floorId)
    console.log('🚀 ~ getFloorListByBuildId ~ arr:', JSON.stringify(flooObj), flooObj)
    floorChange(flooObj, true)
    isFirst.value = false
    return
  }
  floorChange(floorList.value[0])
}

const floorChange = (floor, showInfo = false) => {
  selectDeviceType.value = []
  deviceType.value = []
  eventType.value = ''
  currentFloorInfo.value = floor
  showfloorMap(floor, showInfo)
  getFireDeviceSystemType()
  nextTick(() => {
    elFloorTab.value.setActive(floor)
  })
}

const showfloorMap = (floor = {} as any, showInfo?: boolean) => {
  currentbuildt.value = floor
  isShowNofloorImg.value = false

  if (!floor.floorAreaImg) {
  }

  if (!floor.floorId) {
    isShowNofloorImg.value = true
    isFloorLoading.value = false
    return
  }
  // if (currentFloorid.value == floorid) return
  //         // IndoorMap.ViewType.IndoorAreaVector, IndoorMap.ViewType.IndoorAreaImage//视图类型：室内图-室内GIS
  indoorFloorMap.closePopup()
  indoorFloorMap.setBGVisible(false)

  indoorFloorMap.showFloorData(
    IndoorMap.ViewType.IndoorAreaVector, //数据类型
    null, //单位ID（楼层平面图时，可缺省）
    null, //楼层ID（楼层平面图时，可缺省）
    floor.floorId, //楼栋ID
    config.gis_image_url + floor.floorAreaImg, //平面图地址（可缺省，建议填写）
    function (mapType, success) {
      // ControlSky()
      //加载完成事件
      //加载第三方数据
      // deviceTypePid: 02000000
      // deviceTypeIds: 02020000
      isFloorLoading.value = false
      let _paramStr = 'subCenterCode=' + buildInfo.value.subCenterCode
      if (selectDeviceType.value && selectDeviceType.value[0]) {
        _paramStr += '&deviceTypePid=' + selectDeviceType.value[0]
      }
      if (selectDeviceType.value && selectDeviceType.value[1]) {
        _paramStr += '&deviceTypeIds=' + selectDeviceType.value[1]
      }
      if (eventType.value) {
        if (eventType.value == '7') {
          _paramStr += '&onlineState=1'
        } else {
          _paramStr += '&eventType=' + eventType.value
        }
      }

      indoorFloorMap.showFloorDataDeviceOnLoadGeoData(
        mapType,
        success,
        config.gis_serve_url + '/bitmap/queryDeviceList',
        // 'http://www.gsafetycloud.com/api/v2/operation-management-service/plan/eventWL/getDeviceListByCondition',
        // https://www.tanzercloud.com/api/v3/bw-svc-enterprise-gis-service/bitmap/queryDeviceList
        _paramStr,
        'POST',
        undefined,
        function (data, markFieldNameX, markFieldNameY) {
          if (data[markFieldNameX] && data[markFieldNameY]) {
            // data['runState'] =  data['runState'] == "5" ? '-99' : data['runState'];//2.0
            // cb && cb(data, markFieldNameX, markFieldNameY, markFieldNameZ, indoor);
          } else {
            return true
          }
        }
      ).onLoad = function (res) {
        // $("#mask_loading").hide();
        if (res.code == 'success') {
          if (showInfo && props.deviceInfo && props.deviceInfo.deviceId) {
            let arr = res.data.filter((i) => i.deviceId == props.deviceInfo.deviceId)
            setTimeout(() => {
              if (arr[0]) {
                addDevicePopup(arr[0])
              }
            }, 700)
          }

          // 自动完成安销联动 --摄像头
          // indoorFloorMap.videoBufferQueryAll(true)
          // var _deviceData = res.data;
          // if(publicObj.isHeightAlarm) {
          //     indoorFloorMap.AppendAlarmArrayToDeviceLayer(publicObj.alarmEquipments, publicObj.currentFloorId);
          // }
          // onLoadCb && onLoadCb(_deviceData);
        } else {
          if (res.code === 'tokenError') {
            // toDo: token报错关闭系统
          }
        }
      }
    }
  )
}

//
//天空显示控制
//
const ControlSky = () => {
  var viewType = indoorFloorMap.getViewType()
  if (
    viewType == IndoorMap.ViewType.IndoorAreaVector25D ||
    viewType == IndoorMap.ViewType.IndoorAreaVector3DM ||
    viewType == IndoorMap.ViewType.OVBuildArea ||
    viewType == IndoorMap.ViewType.OVUnitModel
  ) {
    indoorFloorMap.setBGVisible(true) //室内GIS平面图2.5D和3DM以及鸟瞰图模型模式和白模模式打开天空
  } else {
    indoorFloorMap.setBGVisible(false)
  }
}

// 根据设备id，获取点位坐标信息
function getCoordinateByDeviceId(_deviceId) {
  // 设置设备点位坐标
  let coordinate: any = null
  // 根据设备id，获取设备geo对象
  let geoObj = indoorFloorMap.getGeoDeviceByDeviceId(_deviceId)
  if (geoObj) {
    coordinate = geoObj.getGeometry().getCoordinates()
    // 判断pPoint是否继承自GISShare.SMap.Geometry.IPoint  (pPoint是否是对象的形式)
    if (GISShare.EX.Interface.ImplementOf(coordinate, GISShare.SMap.Geometry.IPoint))
      coordinate = [coordinate.getX(), coordinate.getY()]
    // 判断pPoint设置Z值
    if (geoObj.options) coordinate[2] = geoObj.options.altitude + geoObj.options.height

    return coordinate
  } else {
    return null
  }
}

const mapEvent = () => {
  //设备点位图标 点击事件
  indoorFloorMap.onDeviceSelected = addDevicePopup

  indoorFloorMap.onBufferReslutSelected = function (data, e) {
    //视频点位点击事件
    addDevicePopup(data, e)
    return false
  }

  indoorFloorMap.onNullSelected = function () {
    var _type = indoorFloorMap.getIndoorDataState().mapType
    if (_type > -1) {
      // publicObj.currentDeviceId = "";
      PubSub.publish('CLEARVIDEO')
      indoorFloorMap?.clearPopup()
    }
  }
}
let _container = document.createElement('div')
_container.classList.add('floor-popup')

const addDevicePopup = (data, e?: any) => {
  if (!data && !e) return
  let coordinate = getCoordinateByDeviceId(data.deviceId)
  if (!coordinate) return
  let p = indoorFloorMap.addPopup({
    element: _container,
    single: true, // 控制popup只存在一个还是多个
    custom: true, // 自定义content开关
    autoPan: true,
    // 'dx' : e.objPoint['x'],
    dy: -10,
  })
  let _docu = p.getElement()
  setPopup({
    elment: _docu.firstChild,
    options: {
      pointData: data,
      isVideoBufferQuery: isVideoBufferQuery.value,
    },
  })
  p.show([data.mapX, data.mapY, indoorFloorMap.getIndoorDataState().floorHeight || 0])

  return false
}

onUnmounted(() => {})

//外部可访问的函数
function getIndoor() {
  // return STATIC_Indoor;
}
function showDeviceInfo() {
  // return STATIC_Indoor;
  // console.log('showDeviceInfo-----floorList.value-------------', floorList.value)
  // floorList.value.filter(data)
}
const initFloorMap = () => {
  _isFull.value = props.isFull || false
  buildInfo.value = props.currentBuildInfo || {}
  currentGisUnit.value = props.currentUnit || ''
  isFloorLoading.value = true
  isFirst.value = false
  selectDeviceType.value = []
  deviceType.value = []
  eventType.value = ''
  if (props.showDevicceInfo) {
    isFirst.value = true
  }
  createMap()
  getMonitorBuildInfo()
}

const elCascaderOnClick = (e) => {
  e.target.parentNode.previousElementSibling.click()
}

defineExpose({
  initFloorMap,
  getIndoor,
  showDeviceInfo,
})
</script>

<style lang="scss" scoped>
.indoorContent {
  position: relative;
  width: 100%;
  background-color: #eee;

  .floorMap {
    width: 100%;
    height: calc(100% - 85px);
  }

  .nofloorImg {
    width: 100%;
    height: calc(100% - 85px);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    text-align: center;

    .text {
      color: rgba(0, 0, 0, 0.8);
    }

    img {
      width: 350px;
    }
  }

  .header-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 86px;
    background-color: #fff;
    padding: 0 15px;

    .left {
      display: flex;
      align-items: center;

      .back {
        img {
          width: 19px;
        }
      }

      & > div {
        margin-right: 30px;
      }
    }

    .right {
      display: flex;
      align-items: center;

      img {
        width: 18px;
      }

      & > div {
        margin-left: 15px;
      }
    }
  }
}

.mapContainer {
  width: 100%;
  height: 100%;
}

.mapContainer-floor {
  width: 100%;
  height: 100%;
  padding-top: 39px;
}

.floorList-box {
  position: absolute;
  top: 50%;
  right: 30px;
  height: calc(70% - 100px);
  width: 150px;
  transform: translateY(-50%);
  border-radius: 10px;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  // background: rgba(0, 0, 0, .1);
}

.card {
  position: absolute;
  top: 110px;
  left: 30px;
  width: 180px;
  padding: 15px;
  display: grid;
  grid-template-columns: repeat(3, auto);
  grid-column-gap: 15px;
  grid-row-gap: 15px;
  // grid-auto-flow:column;
  border-radius: 10px;
  opacity: 0.92;
  background: #ffffff;
  box-shadow:
    4px 0px 12px 0px rgba(0, 0, 0, 0.04),
    -4px 0px 12px 0px rgba(0, 0, 0, 0.04);
  text-align: center;

  .cardActive {
    color: rgba(24, 144, 255, 1);
  }

  .num {
    margin-bottom: 5px;
  }
}

.btn-box {
  position: absolute;
  top: 110px;
  right: 160px;
  display: flex;
  justify-content: space-between;
  min-width: 80px;

  .item {
    width: 34px;
    height: 32px;
    background: #ffffff;
    border-radius: 4px;
    margin-right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 16px;
    }
  }

  // .item:nth-of-tyep(1){
  //   img{
  //     width: 16px;
  //   }
  // }
  // .item:nth-of-tyep(3){
  //   img{
  //     width: 18px;
  //   }
  // }

  .activeMeasure {
    background-color: red;
  }
}
</style>

<!--        IndoorScene.init();
        //
        // 构造室内GIS
        //
        var indoorFloorMap = new IndoorScene({
            target: 'gsMap',          //绑定的标签位置
            tile: false,              //底图设置
            // animation: false,        //关闭初始动画
            onLoad: onLoad,           //加载完成事件
            deviceIconUrlHeader: 'http://60.174.207.208:7051/img/deviceIcons',
        });
        function onLoad(sender) {
            //此处可确保顺序执行
        }
        //
        //事件绑定
        //
        //楼层图斑点击事件
        indoorFloorMap.onAreaSelected = function (data, e, obj) {
            alert('onAreaSelected');
            //
            console.log('onAreaSelected');
            console.log(data);
            return false;
        }
        //楼层网格点击事件
        indoorFloorMap.onGridSelected = function (data, e, obj) {
            alert('onGridSelected');
            //
            console.log('onGridSelected');
            console.log(data);
            return false;
        }
        //设备点位图标 点击事件
        indoorFloorMap.onDeviceSelected = function (data, e, obj) {
            alert('onDeviceSelected');
            //
            console.log('onDeviceSelected');
            console.log(data);
            return false;
        }
        //安消联动查询到的视频设备点位图标 点击事件
        indoorFloorMap.onBufferReslutSelected = function (data, e, obj) {
            alert('onBufferReslutSelected');
            //
            console.log('onBufferReslutSelected');
            console.log(data);
            return false;
        }
        //
        //
        //
        //楼层切换
        changeFloor = function (floorid) {
            indoorFloorMap.closePopup();
            indoorFloorMap.showFloorData(
                indoorFloorMap.ViewType.IndoorAreaVector,     //数据类型
                null,                                    //单位ID（楼层平面图时，可缺省）
                null,                                    //楼层ID（楼层平面图时，可缺省）
                floorid,                                 //楼栋ID 
                './421002YZDW201912120002_014_D001.jpg', //平面图地址（可缺省，建议填写）
                function (mapType, success) {            //加载完成事件
                    //加载第三方数据
                    indoorFloorMap.showFloorDataDeviceOnLoadGeoData(
                        mapType,
                        success,
                        'http://www.gsafetycloud.com/api/v2/operation-management-service/plan/eventWL/getDeviceListByCondition',
                        '',
                        "POST",
                        undefined,
                        undefined).onLoad = function (sender) {
                            // sender.data;
                            // var alarmArray = [];
                            // for (var i = 0; i < 5; i++) {
                            //     alarmArray.push(sender.data[i]);
                            //     if (i % 2 === 0) alarmArray.push(indoorFloorMap.CloneDeep(sender.data[i]));
                            // }
                            // indoorFloorMap.AppendAlarmArrayToDeviceLayer(alarmArray, floorid);
                        };
                });
        };
        //
        function chGridLayer_onchange(sender) {
            //设置网格数据
            indoorFloorMap.setGridVisible(sender.checked);
        }
        function chBufferQuery_onchange(sender) {
            //设置安消联动
            indoorFloorMap.setVideoBufferQuery(sender.checked);
        }
       
        function btnClearMeasure_onclick(sender) {
            //清除测量痕迹
            indoorFloorMap.clearMeasure();
        }
        function numBufferQueryRadius_onchange(e) {
            //设置安消联动半径
            indoorFloorMap.videoBufferQuery(Number(e.value));
        }
    -->
