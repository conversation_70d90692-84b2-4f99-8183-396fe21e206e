<template>
  <div class="item-card m-15px mt-0px cursor-pointer">
    <div class="item-card-text unitName-box">
      <div class="title" :class="{ long: statusActive == 2 }">
        <myTooltip :str="props.data.unitName || '--'" :size="16"></myTooltip>
      </div>
      <div class="title-tip" v-if="statusActive == 2" :class="{ num: statusActive == 2 }">
        {{ eventTime }} <span v-if="statusActive == 2 && eventTime">(起)</span>
      </div>
    </div>
    <div v-if="statusActive == 1" class="item-card-text flex pt-10px flex">
      <div>
        <myTooltip :str="fomartDeviceAddress(props.data)"></myTooltip>
      </div>
      <div class="title-tip">{{ eventTime }} <span v-if="statusActive == 2 && eventTime">(起)</span></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import $API from '~/common/api'
import { ref, computed, onMounted, reactive } from 'vue'
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
  statusActive: {
    type: Number,
    default: 1,
  },
})

onMounted(() => {
  // console.log('-----',props)
})

const className = computed(() => {
  if (props.data.eventType == 1 || props.data.eventType == 8) return 'fire'
  if (props.data.eventType == 2) return 'warning'
  if (props.data.eventType == 4) return 'hidden-danger'
  if (props.data.eventType == 3) return 'fault'
  return props.data.eventType
})
const tipStr = computed(() => {
  if (props.data.eventType == 8) return '疑似真警'
  if (props.data.eventType == 2) {
    if (props.data.warningRank) {
      if (props.data.warningRank == 1) {
        return '一级预警'
      } else if (props.data.warningRank == 2) {
        return '二级预警'
      } else if (props.data.warningRank == 3) {
        return '三级预警'
      } else if (props.data.warningRank == 4) {
        return '四级预警'
      }
      return ''
    }
  }
  if (props.data.eventType == 4 && props.data.isTimeout == 1) return '超期隐患'
  if (props.data.eventType == 3) {
    if (props.data.transferType === 0) return '自行处置'
    if (props.data.transferType === 1) return '转维保'
    if (props.data.transferType === 2) return '转设备运维'
  }
  return ''
})
const eventTime = computed(() => {
  // priorityEventType为7是离线
  if (props.statusActive == 1) {
    if (props.data.priorityEventType == '7') return props.data.offlineTime
    else return props.data.iotReceiveTime
  }
  return props.data.counts
  // if (
  //   props.data.eventType == 1 ||
  //   props.data.eventType == 2 ||
  //   props.data.eventType == 8
  // ) {
  //   return props.data.lastEventTime
  // } else {
  //   return props.data.eventTime
  // }
})
const fomartDeviceAddress = (item) => {
  if (!item.buildingName && !item.floorName && !item.deviceAddress) return '--'
  const { buildingName = '', floorName = '', deviceAddress = '' } = item

  return buildingName + floorName + deviceAddress
}
</script>

<style lang="scss" scoped>
.item-card {
  // height: 80px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 10px 15px;
  border: 1px solid #0057b0;
  font-size: 14px;
  border-radius: 4px;
  margin-bottom: 8px;
  position: relative;
  background-image: url(@/assets/image/unitMap/item-bg.png);
  background-size: 100% 100%;

  &:hover {
    border-radius: 4px;
    // background: #003471;
    background-image: url(@/assets/image/unitMap/item-bg-active.png);

    border: 1px solid #00baff;
    // box-shadow: inset 0px 0px 8px 0px #8df0fe;
    border-radius: 4px;
  }

  .item-card-text {
    width: 100%;
  }

  .item-card-text:nth-child(2) {
    // width: 278px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .unitName-box {
    display: flex;
    align-items: center;
    justify-content: space-between;

    // align-items: center;
    .title {
      // width: 201px;
      max-width: 170px;
      font-size: 16px;
      color: #fffffe;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      // margin-right: 10px;
    }

    .long {
      max-width: 230px;
    }

    .title-tip {
      // position: absolute;
      // right: 90px;
      // top: 2px;
      padding: 3px 8px;
      font-size: 12px;
      font-weight: 400;
      color: #ffffff;
      border-radius: 11px;
    }

    .num {
      font-size: 18px;

      span {
        font-size: 13px;
      }
    }

    .fire {
      border: 1px solid #f79a9a;
      background: linear-gradient(0deg, #b60000 0%, #e85252 100%);
    }

    .warning {
      border: 1px solid #ff8aed;
      background: linear-gradient(0deg, #670057 0%, #f239d6 100%);
    }

    .hidden-danger {
      border: 1px solid #f1e177;
      background: linear-gradient(0deg, #473e00 0%, #d2b700 100%);
    }

    .fault {
      border: 1px solid #ffd18d;
      background: linear-gradient(0deg, #7e4c01 0%, #f49c19 100%);
    }
  }
}

.font-14 {
  font-size: 14px;
  padding-left: 0;
  padding-right: 0;
  color: rgba(255, 255, 254, 1);
}

.p-12 {
  padding: 12px 0;
}

.status {
  color: #ff2626;
  position: absolute;
  right: 13px;
  top: 35px;
  // transform: translateY(-50%);
  background: rgba(255, 38, 38, 0.29);
  border: 1px solid #ff2626;
  border-radius: 4px;
  font-size: 12px;
  padding: 4px 8px;
}

.status2 {
  color: #11e6ff;
  position: absolute;
  right: 13px;
  top: 35px;
  // transform: translateY(-50%);
  font-size: 12px;
  padding: 4px 8px;
  background: rgba(17, 230, 255, 0.16);
  border: 1px solid #11e6ff;
  border-radius: 4px;
}

.status3 {
  color: rgba(255, 168, 0, 1);
  position: absolute;
  right: 13px;
  top: 35px;
  // transform: translateY(-50%);
  font-size: 12px;
  padding: 4px 8px;
  background: rgba(255, 168, 0, 0.16);
  border: 1px solid #ffb82e;
  border-radius: 4px;
}

.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.5s;
}

.fade-transform-enter {
  transform: scaleX(1);
}

.fade-transform-leave-to {
  transform: scaleX(-1);
}
</style>
