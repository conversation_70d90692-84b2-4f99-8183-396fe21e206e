<template>
  <div
    v-show="!showData"
    v-loading="showLoading"
    element-loading-text="加载中..."
    element-loading-background="transparent"
    class="pb-30px"
    :class="`${type === 'device' ? 'h-[calc(100%-75px)]' : ''}`"
  >
    <div ref="elLineCharts" style="width: 100%; height: 190px"></div>
  </div>
  <custom-empty v-show="showData" class="h-[calc(100%-55px)] w-full relative"></custom-empty>
</template>

<script lang="ts" setup>
import buttonGroup from '@/components/public/buttonGroup.vue'
import * as echarts from 'echarts'
import { onMounted, onUnmounted, ref, reactive } from 'vue'
const elLineCharts = ref('')
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  bgi_url: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'safe', // safe: 安全一张图 device: 设备一张图
  },
  wrap: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['activeChange'])

const activeTab = ref('1')
const showLoading = ref(true)
const showData: any = ref(false)
let myChart: any = null
let data_y = reactive([])
let data_w = reactive([])
let data_x = reactive([])
let legendData: any = [] // 图例数据

onMounted(() => {
  // echartInit()
})

function resizeHandler() {
  myChart.resize()
}

const setTab = (type) => {
  activeTab.value = type
  showLoading.value = true
  emits('activeChange', type)
}
async function echartInit() {
  console.log('  console.login()---', elLineCharts.value)
  if (!myChart) {
    // if (!document.getElementById('lineCharts')) return
    myChart = echarts.init(elLineCharts.value as any)
  }
  const option = await getOptions()
  myChart.setOption(option, true)
}

const getOptions = () => {
  const option = {
    backgroundColor: 'rgba(0,0,0,0)',
    color: legendData.map((legandItem) => {
      return legandItem.color
    }),
    title: {
      show: false,
      text: '',
      textStyle: {
        align: 'center',
        color: '#fff',
        fontSize: 20,
      },
      top: '3%',
      left: '15%',
    },
    grid: {
      containLabel: true,
      top: '30',
      bottom: '20', //也可设置left和right设置距离来控制图表的大小
      right: '10',
      left: '10',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        label: {
          show: true,
        },
      },
    },
    legend: {
      data: legendData.map((legandItem) => {
        return legandItem.name
      }),
      top: '0',
      right: '0',
      textStyle: {
        color: '#ffffff',
      },
      lineStyle: {
        width: 2, // 线的宽度
      },
      itemHeight: 0, //圆点大小
      itemWidth: 20, // 线的长度
    },
    xAxis: {
      data: data_x,
      axisLine: {
        show: true, //隐藏X轴轴线
      },
      axisTick: {
        show: true, //隐藏X轴刻度
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: 'rgba(233, 240, 255, 1)', //X轴文字颜色
        },
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '',
        nameTextStyle: {
          color: '#fff',
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
        },
        axisLabel: {
          show: true,
          formatter: '{value}', //右侧Y轴文字显示
          textStyle: {
            color: '#fff',
          },
        },
      },
      {
        type: 'value',
        gridIndex: 0,
        min: 50,
        max: 100,
        splitNumber: 8,
        splitLine: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: ['rgba(250,250,250,0.0)', 'rgba(250,250,250,0.05)'],
          },
        },
      },
    ],
    series: legendData.map((legandItem) => {
      return {
        name: legandItem.name,
        type: 'line',
        lineStyle: {},
        // smooth: true, //平滑曲线显示
        showAllSymbol: true, //显示所有图形。
        symbol: 'circle', //标记的图形为实心圆
        data: legandItem.data,
      }
    }),
  }
  return option
}

const initData = (data = [], _data_x = []) => {
  console.log('报警时段分布------', data)
  showLoading.value = false
  showData.value = false
  data_x = _data_x
  legendData = [...data]
  // if (props.type === 'device') {
  //   if (!data.resultList || data.resultList.length === 0) showData.value = true
  //   data_x = data.resultList.map((i) => i.date)
  //   data_w = data.resultList.map((i) => i.offlineNum) // 离线次数
  //   data_y = data.resultList.map((i) => i.offlineDeviceNum) // 离线设备数
  // } else {
  //   if(!data.data_x||data.data_x.length<=0) return showData.value = true

  //   data_x = data.data_x
  //   data_w = data.data_w
  //   data_y = data.data_y
  // }

  echartInit()
}

defineExpose({ initData })
</script>

<style scoped lang="scss"></style>
