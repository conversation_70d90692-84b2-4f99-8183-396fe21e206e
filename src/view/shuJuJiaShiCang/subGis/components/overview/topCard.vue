<template>
  <div
    class="dataScreening"
    v-loading="showLoading"
    element-loading-text="加载中..."
    element-loading-background="transparent"
  >
    <div class="card-box" :class="{ 'opacity-0': showLoading }">
      <div
        class="item-card"
        v-for="(item, index) in list"
        :key="index"
        :class="item.className"
        track
        @click="jump(item)"
      >
        <div class="num">
          {{ item.num }}<span class="compnay">{{ item.compnay }} </span>
        </div>
        <div class="flex">
          <div>
            {{ item.title }}
          </div>
          <div class="ml-5px">
            <el-tooltip
              class="box-item"
              :popper-class="['complex', 'tip']"
              effect="dark"
              raw-content
              :content="item.tip"
            >
              <el-icon size="14px">
                <InfoFilled class="!h-full !w-full" />
              </el-icon>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import $API from '~/common/api'
import { ref, onMounted, reactive, onUnmounted } from 'vue'
import { useUserInfo } from '~/store'
import { InfoFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import config from '~/config'

const router = useRouter()
const userInfo = useUserInfo()
const showLoading = ref(true)

let list: any = reactive([
  {
    title: '火警',
    num: 0,
    className: 'fire Jump',
    key: 'alarmNum',
    compnay: '起',
    tip: `火警 ：火灾自动报警系统、独立式探测设备及烟火视频分析设备上报的火警信号。`,
    // JumpUrl: '/fireRemoteManage/realTimeMonitor',
    status: '1',
  },
  {
    title: '预警',
    num: 0,
    className: 'warning Jump',
    key: 'warningNum',
    compnay: '起',
    tip: `预警：在火灾发生前，监测到的火灾前兆信号，如电气火灾监测装置上报的过温信号。`,
    // JumpUrl: '/fireRemoteManage/realTimeMonitor',
    status: '2',
  },
  {
    title: '故障',
    num: 0,
    className: 'fault Jump',
    key: 'faultNum',
    compnay: '起',
    tip: `故障：由消防设施、独立式消防设备上报到平台的"故障"信号，此时设备无法达到预期设定的工况。`,
    // JumpUrl: '/fireRemoteManage/realTimeMonitor',
    status: '3',
  },
  {
    title: '隐患',
    num: 0,
    className: 'hidden-danger Jump',
    key: 'troubleNum',
    compnay: '起',
    tip: `隐患：通过监测设备监测到的消防隐患，例如水压监测装置监测到的消防管网水压不足，水位监测装置监测到的消防水池水位不足，或由智能视频分析仪监测到的消防控制室人员离岗等。`,
    // JumpUrl: '/fireRemoteManage/realTimeMonitor',
    status: '4',
  },
  {
    title: '动作',
    num: 0,
    className: 'action Jump',
    key: 'movingNum',
    compnay: '起',
    tip: `动作：由火灾自动报警主机上报的启动、停止、反馈信号。`,
    // JumpUrl: '/fireRemoteManage/realTimeMonitor',
    status: '5',
  },
  {
    title: '离线',
    num: 0,
    className: 'off-line Jump',
    key: 'offlineNum',
    compnay: '起',
    tip: `离线：指通过平台规则判定的设备的联网状态为离线。`,
    // JumpUrl: '/fireRemoteManage/realTimeMonitor',
    status: '7',
  },
])
const timer: any = ref(null)

onMounted(() => {
  getStatisticDeviceEventNums()
  if (timer.value) {
    clearInterval(timer.value)
  }
  // timer.value = setInterval(() => {
  //   getStatisticDeviceEventNums()
  // }, 5000)
})
onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})
const jump = async (val) => {
  // 跳转设备列表  7 离线

  // sysCode=iot_monitoring
  const res = await $API.post({
    url: `ehs-clnt-platform-service/login/checkSysPower`,
    data: {
      sysCode: 'iot_monitoring',
      userId: userInfo.value.id,
    },
  })

  let url = `${config.base_prefix}/ehs-internet-monitor/#/index?token=${res.data.token}&sysCode=iot_monitoring&eventType=${val.status}&page=/monitor/realTimeMonitorPage`
  window.open(url, '_blank')
}
const getStatisticDeviceEventNums = async () => {
  // 查询 实时事件数量
  const params = {
    orgCode: userInfo.value.orgCode,
    modelType: 'unit_base_url',
  }
  let res: any = {}
  try {
    res = await $API.post({
      url: '/unit/distribution/statisticDeviceEventNums',
      params,
    })
    showLoading.value = false
    console.log('statisticDeviceEventNums---', res)
    if (res.code != 'success') return
    list.forEach((i) => {
      i.num = res.data[i.key]
    })
  } catch (e) {
    //TODO handle the exception
    console.log(e)
    showLoading.value = false
  }
}

function initData(data) {
  console.log('传参--', data)
  showLoading.value = false
  if (!data) return
  list.forEach((i) => {
    if (i.key != 'totalArea') {
      i.num = data[i.key] || 0
    } else {
      i.num = data[i.key] ? (data[i.key] / 10000).toFixed(1) : 0
    }
  })
}
defineExpose({ initData })
</script>

<style scoped lang="scss">
.dataScreening {
  .card-box {
    display: grid;
    grid-template-columns: repeat(3, 125px);
    justify-content: space-between;
  }

  .item-card {
    margin-bottom: 14px;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100px;
    width: 100%;

    .num {
      font-size: 18px;
      padding-top: 3px;
    }

    .compnay {
      display: inline-block;
      font-size: 12px;
    }
  }

  .Jump {
    cursor: pointer;
  }

  .proportion-box {
    display: flex;
    justify-content: space-around;

    .item-box {
      .bg {
        width: 98px;
        height: 98px;
        background-image: url(@/assets/image/unitMap/detail-zs.png);
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .onlineRate {
        background-image: url(@/assets/image/unitMap/onlineRate-bg.png);
      }

      .title {
        margin-top: 12px;
        font-size: 14px;
      }
    }
  }

  .blue {
    // background: #0b389b;
    // border: 1px solid #079afd;
    // box-shadow: inset 0px 0px 16px 0px rgba(7, 181, 253, 0.91);
    background-image: url(@/assets/image/unitMap/001.png);
  }

  .fire {
    // background: #650000;
    // border: 1px solid #ff4545;
    // box-shadow: inset 0px 0px 16px 0px #ff4343;
    background-image: url(@/assets/image/unitMap/001.png);
    background-size: 160px 136px;
    background-position: 60% 0%;
  }

  .warning {
    background-image: url(@/assets/image/unitMap/002.png);
    background-size: 160px 136px;
    background-position: 60% 0%;
  }

  .action {
    background-image: url(@/assets/image/unitMap/005.png);
    background-size: 160px 136px;
    background-position: 60% 0%;
  }

  .hidden-danger {
    background-image: url(@/assets/image/unitMap/004.png);
    background-size: 160px 136px;
    background-position: 60% 0%;
  }

  .fault {
    background-image: url(@/assets/image/unitMap/003.png);
    background-size: 160px 136px;
    background-position: 60% 0%;
  }

  .off-line {
    background-image: url(@/assets/image/unitMap/006.png);
    background-size: 160px 136px;
    background-position: 60% 0%;
  }
}
</style>
