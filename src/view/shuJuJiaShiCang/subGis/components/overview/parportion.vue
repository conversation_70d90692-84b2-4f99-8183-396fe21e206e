<template>
  <div class="dataScreening">
    <div class="proportion-box">
      <div class="item-box">
        <div class="bg">{{ intactnessRate.facilityIntactRate }}%</div>
        <div class="title">消防设施完好率</div>
      </div>
      <div class="item-box">
        <div class="bg onlineRate">{{ intactnessRate.onlineRate }}%</div>
        <div class="title">设备在线率</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import $API from '~/common/api'
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { useUserInfo } from '~/store'
const userInfo = useUserInfo()

const intactnessRate = ref({
  facilityIntactRate: 0,
  onlineRate: 0,
})

onMounted(() => {
  getFireFacilityIntactRate()
  getFacilityOnlineRate()
})
const getFireFacilityIntactRate = async () => {
  // 查询 消防设施完好率
  const params = {
    orgCode: userInfo.value.orgCode,
    modelType: 'unit_base_url',
  }
  let res: any = {}
  try {
    res = await $API.post({
      url: '/unit/distribution/getFireFacilityIntactRate',
      params,
    })
    console.log('getFireFacilityIntactRate---', res)
    if (res.code != 'success') return
    intactnessRate.value.facilityIntactRate = res.data.facilityIntactRate
  } catch (e) {
    //TODO handle the exception
    console.log(e)
  }
}

const getFacilityOnlineRate = async () => {
  // 查询 设备在线率
  const params = {
    orgCode: userInfo.value.orgCode,
    modelType: 'unit_base_url',
  }
  let res: any = {}
  try {
    res = await $API.post({
      url: '/unit/distribution/getFacilityOnlineRate',
      params,
    })
    console.log('getFacilityOnlineRate---', res)
    if (res.code != 'success') return
    intactnessRate.value.onlineRate = res.data.onlineRate
  } catch (e) {
    //TODO handle the exception
    console.log(e)
  }
}

function initData(data) {}
defineExpose({ initData })
</script>

<style scoped lang="scss">
.dataScreening {
  .card-box {
    display: grid;
    grid-template-columns: repeat(3, 108px);
    justify-content: space-between;
  }

  .item-card {
    height: 58px;
    margin-bottom: 14px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    & > div {
      // padding: 5px;
      // padding-top: 3px;
    }

    .num {
      font-size: 18px;
      padding-top: 3px;
    }

    .compnay {
      display: inline-block;
      font-size: 12px;
    }
  }

  .proportion-box {
    display: flex;
    justify-content: space-around;

    .item-box {
      .bg {
        width: 98px;
        height: 98px;
        background-image: url(@/assets/image/unitMap/detail-zs.png);
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
      }

      .onlineRate {
        background-image: url(@/assets/image/unitMap/onlineRate-bg.png);
      }

      .title {
        margin-top: 12px;
        font-size: 14px;
        text-align: center;
      }
    }
  }

  .blue {
    background: #0b389b;
    border: 1px solid #079afd;
    box-shadow: inset 0px 0px 16px 0px rgba(7, 181, 253, 0.91);
  }

  .fire {
    background: #650000;
    border: 1px solid #ff4545;
    box-shadow: inset 0px 0px 16px 0px #ff4343;
  }

  .warning {
    background: #700760;
    border: 1px solid #f13ad5;
    box-shadow: inset 0px 0px 16px 0px #e320c5;
  }

  .action {
    background: #0b389b;
    border: 1px solid #079afd;
    box-shadow: inset 0px 0px 16px 0px rgba(7, 181, 253, 0.91);
  }

  .hidden-danger {
    background: #7d7200;
    border: 1px solid #ffed2f;
    box-shadow: inset 0px 0px 16px 0px #ffee36;
  }

  .fault {
    background: #774700;
    border: 1px solid #ffb74d;
    box-shadow: inset 0px 0px 16px 0px #ffb649;
  }

  .off-line {
    background: #6f6f6f;
    border: 1px solid #949494;
    box-shadow: inset 0px 0px 16px 0px #343434;
  }
}
</style>
