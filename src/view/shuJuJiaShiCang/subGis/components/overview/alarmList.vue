<template>
  <div
    class="alarmInformation"
    v-loading="showLoading"
    element-loading-text="加载中..."
    element-loading-background="transparent"
  >
    <div class="tab-box m-15px mt-0px">
      <div
        class="title-item"
        v-for="(item, index) in titleList"
        :class="{ active: activeIndex == item.type }"
        @click="handleSetActive(item)"
        :key="index"
      >
        <span>
          {{ item.title }}
        </span>
      </div>
    </div>
    <div class="tab-box mb-15px flex_center">
      <div
        class="alarm-status mr-10px"
        :class="{ 'alarm-status-active': alarmStatusActive == 1 }"
        track
        @click="handleAlarmStatus(1)"
      >
        实时{{ statusTitle }}
      </div>
      <div
        class="alarm-status"
        :class="{ 'alarm-status-active': alarmStatusActive == 2 }"
        track
        @click="handleAlarmStatus(2)"
      >
        当前{{ statusTitle }}单位
      </div>
    </div>
    <el-scrollbar
      class="list-box"
      ref="scrollbarRef"
      @scroll="scroll"
      @mouseenter="onMouseenter"
      @mouseleave="onMouseleave"
    >
      <div ref="innerRef">
        <div
          v-for="item in dataList"
          :key="alarmStatusActive == 1 ? item.id : item.unitId"
          class="item"
          track
          @click="itemClick(item)"
        >
          <listItem
            :data="item"
            :key="alarmStatusActive == 1 ? item.id : item.unitId"
            :statusActive="alarmStatusActive"
          >
          </listItem>
        </div>
      </div>
      <custom-empty
        v-if="dataList && dataList.length <= 0 && !showLoading"
        class="h-full w-full relative nomore"
        labelTitle="暂未发现异常数据"
      >
      </custom-empty>
      <div class="more" v-else-if="dataList && dataList.length < total && !showLoading" track @click="getMoreDatas">
        加载更多
      </div>
      <div class="more" v-else style="opacity: 0">占位</div>
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import listItem from './listItemCard.vue'
import { ref, onMounted, onUnmounted, reactive, nextTick } from 'vue'
import $API from '~/common/api'
// import * as types from '~/common/types'
import PubSub from 'pubsub-js'
import { useUserInfo } from '~/store'

const userInfo = useUserInfo()
defineProps({
  type: {
    type: String,
    default: '', // safe: 安全一张图 device: 设备一张图
  },
})
const showLoading = ref(true)
// const emits = defineEmits(['activeChange', 'getMoerData'])
const activeIndex = ref(1)
const alarmStatusActive = ref(1)
const dataList: any = ref([])
const total = ref('')
const statusTitle = ref('火警')
// 1：火警事件 2：预警事件 3：故障事件 4：隐患事件 5：动作事件 7：离线事件
const titleList: any = reactive([
  {
    title: '火警',
    type: 1,
  },
  {
    title: '预警',
    type: 2,
  },
  {
    title: '故障',
    type: 3,
  },
  {
    title: '隐患',
    type: 4,
  },
  {
    title: '动作',
    type: 5,
  },
  {
    title: '离线',
    type: 7,
  },
])
const max = ref(0)
const scrollbarRef = ref()
const innerRef = ref<HTMLDivElement>()
const scrllValue = ref(0)
const timer = ref()
const pageMode: any = reactive({
  pageNo: 1,
  pageSize: 30,
  eventType: 1,
})
const isroll = ref(true)

onMounted(() => {
  getEventListByType()
})

onUnmounted(() => {
  timerClear()
})

const onMouseenter = () => {
  timerClear()
}

const onMouseleave = () => {
  timerOpen()
  // PubSub.unsubscribe(ALARM_STATUS)
}
const scroll = ({ scrollTop }) => {
  scrllValue.value = scrollTop
}

// let ALARM_STATUS = PubSub.subscribe(types.ALARM_STATUS, async () => {
//   getEventListByType()
// })

const handleSetActive = (item) => {
  if (activeIndex.value == item.type) return
  showLoading.value = true
  activeIndex.value = item.type
  dataList.value = []
  pageMode.pageNo = 1
  pageMode.eventType = item.type
  console.log(item)
  statusTitle.value = item.title
  getEventListByType()
}
const itemClick = (item) => {
  console.log('当前激活---', alarmStatusActive.value)
  console.log('当前激活---', item)
  // return
  if (alarmStatusActive.value == 2) {
    PubSub.publish('UNITCHANGEMAPZOOMTO', item)
  } else {
    // let obj = {
    //     "deviceTypeId": "02030000",
    //     "eventTypeNames": "",
    //     "latitude": 1233372.14,
    //     "eventType": "",
    //     "manufacturerCode": "tanZer",
    //     "deviceId": "30549",
    //     "mapZ": 0,
    //     "mapY": 3710255.6371704293,
    //     "mapX": 13055348.47914497,
    //     "deviceNum": "113",
    //     "buildingName": "指挥演示大厅",
    //     "monitorReceiveTime": "2022-07-06 15:14:01",
    //     "iotReceiveTime": "2022-07-06 15:13:48",
    //     "deviceAddress": "A-101室",
    //     "signalCode": "",
    //     "deviceOnlyId": "1841913361a409e149671d6f47c714a0",
    //     "unitId": "AHHF_QHHFY_20180408",
    //     "deviceState": 0,
    //     "floorName": "1层",
    //     "priorityEventType": "",
    //     "useInfo": "",
    //     "onlineState": 0,
    //     "longitude": 872628.32,
    //     'subCenterCode': '340100YYZX201805230001',
    //     'buildId': 'AHHF_QHHFY_20180408_002',
    //     'floorId': 'AHHF_QHHFY_20180408_002_U004'
    // }
    PubSub.publish('TOFLOORINFO', item)
  }
  console.log('当前点击---', item)
}
const handleAlarmStatus = (type) => {
  alarmStatusActive.value = type
  getEventListByType()
}
const getMoreDatas = () => {
  // emits('getMoerData')
  isroll.value = false
  pageMode.pageNo++
  isroll.value = false
  getEventListByType(false)
}

const getEventListByType = async (clear = true) => {
  // 查询 设备在线率
  let params = {
    orgCode: userInfo.value.orgCode,
    modelType: 'unit_base_url',

    ...pageMode,
  }
  showLoading.value = true
  if (clear) {
    dataList.value = []
  }
  let url: any = ''
  if (alarmStatusActive.value == 1) {
    url = '/unit/distribution/getEventListByType'
  } else if (alarmStatusActive.value == 2) {
    url = '/unit/distribution/statisticUnitEventList'
    if (params.eventType == 7) {
      params.deviceClassification = '3,4'
    }
  }
  let res: any = {}
  try {
    res = await $API.post({
      url: url,
      params,
    })
    showLoading.value = false
    if (res.code != 'success') return

    total.value = res.data.total
    dataList.value.push(...res.data.rows)
    nextTick(() => {
      isroll.value = true
      if (pageMode.pageNo > 1) return
      timerClear()
      timerOpen()
    })
  } catch (e) {
    //TODO handle the exception
    console.log(e)
    showLoading.value = false
  }
}

const timerOpen = () => {
  // return
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
  // console.log('scrollbarRef.value---',scrollbarRef.value.wrap$)
  // wrapRef
  if (innerRef.value && scrollbarRef.value) {
    max.value = innerRef.value!.clientHeight - (scrollbarRef.value?.wrapRef.clientHeight || 0) + 40
  }
  if (max.value <= 0) return
  timer.value = setInterval(() => {
    if (!isroll.value) return
    scrllValue.value++
    if (scrllValue.value >= max.value) {
      scrllValue.value = 0
    }
    if (scrollbarRef.value) {
      scrollbarRef.value!.setScrollTop(scrllValue.value)
    }
  }, 80)
}
const timerClear = () => {
  clearInterval(timer.value)
}

const initData = (data, _total, clear = false, type) => {
  console.log(dataList, type)
  console.log('type----------', activeIndex.value, type)
  showLoading.value = false
  total.value = _total
  activeIndex.value = type
  if (clear) {
    dataList.value = []
  }
  dataList.value.push(...data)

  if (!clear) {
    return
  }
  nextTick(() => {
    // 开启定时器 滚动开启
    timerOpen()
  })
}
defineExpose({ initData })
</script>

<style scoped lang="scss">
.alarmInformation {
  display: flex;
  height: 100%;
  flex-direction: column;

  .tab-box {
    display: flex;

    .alarm-status {
      cursor: pointer;
      padding: 3px 15px;
      background-size: 100% 100%;
      background-image: url(@/assets/image/unitMap/tabs-active.png);
    }

    .alarm-status-active {
      background-image: url(@/assets/image/unitMap/tabs.png);

      background-size: 100% 100%;
    }
  }

  .flex_center {
    justify-content: center;
  }

  .title-item {
    height: 32px;
    width: 80px;
    line-height: 32px;
    text-align: center;
    // background-color: #3498db; /* 平行四边形的颜色 */
    background: linear-gradient(180deg, rgba(0, 113, 255, 0) 0%, rgba(0, 133, 255, 0.26) 100%);
    transform: skewX(-20deg); /* 倾斜平行四边形 */
    margin: 2px; /* 四边形之间的间隔 */
    color: #50a1ff;
    border: 1px solid;
    border-image: linear-gradient(180deg, rgba(13, 42, 112, 1), rgba(0, 78, 188, 1)) 1 1;
    span {
      display: inline-block;
      transform: skewX(20deg); /* 倾斜平行四边形 */
    }
    // background-image: url(@/assets/image/unitMap/alarmTabs-bg.png);
    // background-size: cover;
    // margin-right: 10px;
    // cursor: pointer;
    // color: #11e6ff;
  }

  .active {
    // background-image: url(@/assets/image/unitMap/alarmTabs-active.png);
    background: linear-gradient(180deg, rgba(0, 120, 253, 0) 0%, #60a1ed 100%);
    background-size: 100% 100%;
    color: #fff;
  }

  .list-box {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow-y: auto;
    padding-right: 4px;
    padding-bottom: 10px;

    :deep(.el-scrollbar__view) {
      height: 90%;
    }

    .more {
      text-align: center;
      padding: 8px 0;
      color: rgba(179, 193, 201, 1);
      cursor: pointer;
    }

    .nomore {
      // margin-top: 30%;
    }

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar:horizontal {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background: #36425e;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(56, 107, 187, 1);
      border-radius: 10px;
      transition: all 0.2s ease-in-out;
    }
  }
}
</style>
