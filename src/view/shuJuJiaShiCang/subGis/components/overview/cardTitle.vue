<template>
  <div class="title-box">
    <div class="title-main">
      <div class="icon" v-if="showIcon">
        <img src="@/assets/image/unitMap/title-icon.png" alt="" />
      </div>
      <div class="title text-[18px]">
        {{ title }}
      </div>
      <div class="end-box" v-if="showIcon">
        <img src="@/assets/image/unitMap/title-end.png" alt="" />
      </div>
    </div>

    <div class="title-right">
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
const props = defineProps({
  title: {
    type: String,
    default: '综合安全指数',
  },
  showIcon: {
    type: Boolean,
    default: true,
  },
  size: {
    type: Number,
    default: 18,
  },
  style: {
    type: Object,
    default: () => {},
  },
})
const titleSize = computed(() => {
  return props.size + 'px'
})
onMounted(() => {
  // console.log(props.wrap)
  // console.log(props.title)
})
</script>

<style lang="scss" scoped>
.title-box {
  position: relative;
  height: 40px;
  line-height: 40px;
  display: flex;
  .title-main {
    display: flex;
    align-items: center;
    width: 100%;
    img {
      height: 12px;
    }
  }

  .title {
    // background-image: url(@/assets/image/yzt/card-title.png);
    font-size: 18px;
    font-family: Microsoft YaHei;
    font-weight: 600;
    color: #ffffff;
    background: linear-gradient(0deg, #3aa9ff 0%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    padding: 0 5px;
  }
  .title-right {
    margin-left: 10px;
  }
}
</style>
