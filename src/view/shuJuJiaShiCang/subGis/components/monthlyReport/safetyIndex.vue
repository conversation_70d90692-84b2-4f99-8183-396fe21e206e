<template>
  <div class="safetyIndex">
    <div class="item mb-10px" v-for="item in list" :style="`background-color:${item.bgColor}`" :key="item.key">
      <div class="flex justify-between p-10px items-center" :class="item.className">
        <div class="flex justify-center items-center">
          <div>
            <span class="icon-bg"></span>
          </div>
          <div>
            {{ item.name }}
          </div>
        </div>
        <div>
          <span class="num">
            {{ item.num }}
          </span>
          天
        </div>
      </div>
      <div class="flex justify-center p-10px items-center" v-if="item.scoreName">
        <span class="spot"></span> {{ item.scoreName }} <span class="num"> {{ item.score }} </span> 分
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, reactive, watch } from 'vue'
// ACount: 0
//    BCount: 0
//    CCount: 0
//    DCount: 0
//    hignScore: 0
//    lowScore: 0
//    monthAverage: 0
// const props = defineProps({
//  listValue: {
//    type: Object,
//    default: {}
//  }
// })

const list: any = reactive([
  {
    name: '优秀',
    num: 0,
    key: 'ACount',
    scoreKey: 'hignScore',
    score: 0,
    scoreName: '最高',
    className: 'safetyIndex-excellent',
    bgColor: 'rgba(25, 159, 103, .36)',
  },
  {
    name: '良好',
    num: 0,
    score: 0,
    key: 'BCount',
    scoreKey: 'monthAverage',
    scoreName: '平均',
    className: 'safetyIndex-good',
    bgColor: 'rgba(45, 200, 255, .36)',
  },
  {
    name: '及格',
    num: 0,
    score: 0,
    key: 'CCount',
    scoreKey: 'lowScore',
    scoreName: '最低',
    className: 'safetyIndex-pass',
    bgColor: 'rgba(255, 127, 33, .36)',
  },
  {
    name: '危险',
    num: 0,
    key: 'DCount',
    // score:88,
    // scoreName:'最高',
    className: 'safetyIndex-danger',
    bgColor: 'rgba(247, 53, 56, .36)',
  },
])
const initData = (val) => {
  console.log('initData----------', val)
  list.forEach((i) => {
    i.num = val[i.key]
    if (i.scoreKey) i.score = val[i.scoreKey]
  })
}
defineExpose({ initData })
</script>

<style lang="scss" scoped>
.safetyIndex {
  .item {
    display: grid;
    grid-template-columns: repeat(2, 50%);
    img {
      width: 16px;
    }
    .spot {
      width: 6px;
      height: 6px;
      background: #53d0ff;
      border-radius: 50%;
      margin-right: 8px;
    }
    .num {
      display: inline-block;
      font-size: 18px;
      margin: 0 5px;
    }
    .safetyIndex-excellent {
      background-image: url(@/assets/image/unitMap/safetyIndex-excellent.png);
      background-size: 100% 100%;
      .icon-bg {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-image: url(@/assets/image/unitMap/icon-excellent.png);
        background-size: 100% 100%;
      }
    }
    .safetyIndex-good {
      background-image: url(@/assets/image/unitMap/safetyIndex-good.png);
      background-size: 100% 100%;
      .icon-bg {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-image: url(@/assets/image/unitMap/icon-good.png);
        background-size: 100% 100%;
      }
    }
    .safetyIndex-pass {
      background-image: url(@/assets/image/unitMap/safetyIndex-pass.png);
      background-size: 100% 100%;
      .icon-bg {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-image: url(@/assets/image/unitMap/icon-pass.png);
        background-size: 100% 100%;
      }
    }
    .safetyIndex-danger {
      background-image: url(@/assets/image/unitMap/safetyIndex-danger.png);
      background-size: 100% 100%;
      .icon-bg {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-image: url(@/assets/image/unitMap/icon-danger.png);
        background-size: 100% 100%;
      }
    }
  }
}
</style>
