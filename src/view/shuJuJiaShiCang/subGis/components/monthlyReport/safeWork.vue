<template>
  <div class="safeWork">
    <cardTitle :title="title" :showIcon="false" :size="16"></cardTitle>
    <div class="list">
      <div class="item mb-15px" v-for="(item, index) in data as any" :key="index">
        <div class="name">{{ item.name }}</div>
        <div class="speed-box">
          <div class="speed" :class="{ 'speed-standard': item.isStandard }" :style="{ width: item.num }"></div>
        </div>
        <div class="rate" :class="{ 'rate-standard': item.isStandard }">{{ item.num }}</div>
      </div>

      <!-- <div class="item mb-15px">
        <div class="name"> 防火巡查 </div>
        <div class="speed-box">
          <div class="speed speed-standard" :style="{width:'40%'}"></div>
        </div>
        <div class="rate rate-standard">80%</div>
      </div> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import $API from '~/common/api'
import cardTitle from '../overview/cardTitle.vue'
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { useUserInfo } from '~/store'

const userInfo = useUserInfo()
// const showLoading = ref(true)

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  title: {
    type: String,
    default: '',
  },
})
onMounted(() => {})

// [
//     {
//       title: '火警',
//       num: 0,
//       className: 'fire',
//       key: 'alarmNum',
//       compnay: '起'
//     },
//     {
//       title: '预警',
//       num: 0,
//       className: 'warning',
//       key: 'warningNum',
//       compnay: '栋'
//     },
//     {
//       title: '动作',
//       num: 0,
//       className: 'action',
//       key: 'movingNum',
//       compnay: '起'
//     },
//     {
//       title: '故障',
//       num: 0,
//       className: 'fault',
//       key: 'faultNum',
//       compnay: '起'
//     },
//     {
//       title: '隐患',
//       num: 0,
//       className: 'hidden-danger',
//       key: 'troubleNum',
//       compnay: '起'
//     }
//   ]

let list: any = ref([])

const intactnessRate = ref({
  facilityIntactRate: 0,
  onlineRate: 0,
})

onMounted(() => {
  console.log('props.data-----------', props.data)
  list.value = props.data
})

const getFacilityOnlineRate = async () => {
  // 查询 设备在线率
  const params = {
    orgCode: userInfo.value.orgCode,
    modelType: 'unit_base_url',
  }
  let res: any = {}
  try {
    res = await $API.post({
      url: '/unit/distribution/getFacilityOnlineRate',
      params,
    })
    console.log('getFacilityOnlineRate---', res)
    if (res.code != 'success') return
  } catch (e) {
    //TODO handle the exception
    console.log(e)
  }
}

function initData(data) {
  console.log('传参--', data)
  // showLoading.value = false
  if (!data) return
  list.value.forEach((i) => {
    if (i.key != 'totalArea') {
      i.num = data[i.key] || 0
    } else {
      i.num = data[i.key] ? (data[i.key] / 10000).toFixed(1) : 0
    }
  })
}
defineExpose({ initData })
</script>

<style scoped lang="scss">
.safeWork {
  border: 1px solid rgba(26, 110, 192, 1);
  border-radius: 12px;
  padding: 0 15px;
  margin-bottom: 15px;

  .list {
    .item {
      display: flex;
      align-items: center;

      .name {
        width: 70px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex-shrink: 0;
        font-size: 14px;
        // text-align: right;
      }

      .speed-box {
        flex: 1;
        background-color: rgba(24, 52, 80, 1);
        height: 10px;
        margin: 0 10px;

        .speed {
          width: 0;
          height: 10px;
          background: linear-gradient(90deg, #00e3eb 40%, #3fffd4 100%);
        }

        .speed-standard {
          background: linear-gradient(90deg, #17aaff 21%, #13f7ff 100%);
        }
      }

      .rate {
        color: rgba(77, 255, 216, 1);
        font-size: 14px;
        width: 50px;
      }

      .rate-standard {
        color: #13f4ff;
      }
    }
  }

  .blue {
    background: #0b389b;
    border: 1px solid #079afd;
    box-shadow: inset 0px 0px 16px 0px rgba(7, 181, 253, 0.91);
  }

  .fire {
    background: #650000;
    border: 1px solid #ff4545;
    box-shadow: inset 0px 0px 16px 0px #ff4343;
  }

  .warning {
    background: #700760;
    border: 1px solid #f13ad5;
    box-shadow: inset 0px 0px 16px 0px #e320c5;
  }

  .action {
    background: #0b389b;
    border: 1px solid #079afd;
    box-shadow: inset 0px 0px 16px 0px rgba(7, 181, 253, 0.91);
  }

  .hidden-danger {
    background: #7d7200;
    border: 1px solid #ffed2f;
    box-shadow: inset 0px 0px 16px 0px #ffee36;
  }

  .fault {
    background: #774700;
    border: 1px solid #ffb74d;
    box-shadow: inset 0px 0px 16px 0px #ffb649;
  }

  .off-line {
    background: #6f6f6f;
    border: 1px solid #949494;
    box-shadow: inset 0px 0px 16px 0px #343434;
  }

  .complete {
    background: #00785d;
    border: 1px solid #57ffd5;
    box-shadow: inset 0px 0px 16px 0px #63ffd8;
  }
}
</style>
