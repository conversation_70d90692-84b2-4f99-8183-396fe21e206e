<template>
  <div class="dataScreening">
    <div class="card-box">
      <div class="item-card" v-for="item in list" :key="item.key" :class="item.className" track @click="jump(item)">
        <div class="flex">
          <div>
            {{ item.title }}
          </div>
          <div class="ml-5px" v-if="item.tip">
            <el-tooltip
              class="box-item"
              :popper-class="['complex', 'tip']"
              effect="dark"
              raw-content
              :content="item.tip"
            >
              <el-icon size="14px">
                <InfoFilled class="!h-full !w-full" />
              </el-icon>
            </el-tooltip>
          </div>
        </div>
        <div class="num">
          {{ item.num }}<span class="compnay">{{ item.compnay }} </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { InfoFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const showLoading = ref(true)
const emits = defineEmits(['itemClick'])
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  statusActive: {
    type: Number,
    default: 1,
  },
  unitId: {
    type: String,
    default: '',
  },
  unitName: {
    type: String,
    default: '',
  },
})
let list: any = ref([])

onMounted(() => {
  console.log('props.data-----------', props.data)
  list.value = props.data
})

const jump = (val) => {
  // 跳转设备列表  7 离线
  if (!val.JumpUrl) return
  // router.push({
  //   path: val.JumpUrl,
  //   query: {
  //     eventType: val.monitorState,
  //     isShowBack: 'true',
  //     unitId: props.unitId,
  //   },
  // })
  // emits('itemClick')

  // router.push({
  //   path: '/fireRemoteManage/monitoring-management/realTime-monitoring',
  //   query: {
  //     ...setPageJsonQueryParamsAdapter({
  //       unitId: props.unitId,
  //       unitName: props.unitName,
  //       monitorState: val.monitorState,
  //       onlineState: val.onlineState,
  //     }),
  //     isShowBack: 1,
  //   },
  // })
}

function initData(data) {
  console.log('传参--', data)
  showLoading.value = false
  if (!data) return
  list.value.forEach((i) => {
    if (i.key != 'totalArea') {
      i.num = data[i.key] || 0
    } else {
      i.num = data[i.key] ? (data[i.key] / 10000).toFixed(1) : 0
    }
  })
}
defineExpose({ initData })
</script>

<style scoped lang="scss">
.dataScreening {
  .card-box {
    display: grid;
    grid-template-columns: repeat(3, 108px);
    justify-content: space-between;
  }

  .item-card {
    margin-bottom: 14px;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100px;
    width: 100%;

    .num {
      font-size: 18px;
      padding-top: 3px;
    }

    .compnay {
      display: inline-block;
      font-size: 12px;
    }
  }

  .Jump {
    cursor: pointer;
  }

  .proportion-box {
    display: flex;
    justify-content: space-around;

    .item-box {
      .bg {
        width: 98px;
        height: 98px;
        background-image: url(@/assets/image/unitMap/detail-zs.png);
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .onlineRate {
        background-image: url(@/assets/image/unitMap/onlineRate-bg.png);
      }

      .title {
        margin-top: 12px;
        font-size: 14px;
      }
    }
  }

  .blue {
    // background: #0b389b;
    // border: 1px solid #079afd;
    // box-shadow: inset 0px 0px 16px 0px rgba(7, 181, 253, 0.91);
    background-image: url(@/assets/image/unitMap/001.png);
  }

  .fire {
    // background: #650000;
    // border: 1px solid #ff4545;
    // box-shadow: inset 0px 0px 16px 0px #ff4343;
    background-image: url(@/assets/image/unitMap/001.png);
    background-size: 160px 136px;
    background-position: 60% 0%;
  }

  .warning {
    background-image: url(@/assets/image/unitMap/002.png);
    background-size: 160px 136px;
    background-position: 60% 0%;
  }

  .action {
    background-image: url(@/assets/image/unitMap/005.png);
    background-size: 160px 136px;
    background-position: 60% 0%;
  }

  .hidden-danger {
    background-image: url(@/assets/image/unitMap/004.png);
    background-size: 160px 136px;
    background-position: 60% 0%;
  }

  .fault {
    background-image: url(@/assets/image/unitMap/003.png);
    background-size: 160px 136px;
    background-position: 60% 0%;
  }

  .off-line {
    background-image: url(@/assets/image/unitMap/006.png);
    background-size: 160px 136px;
    background-position: 60% 0%;
  }
}
</style>
