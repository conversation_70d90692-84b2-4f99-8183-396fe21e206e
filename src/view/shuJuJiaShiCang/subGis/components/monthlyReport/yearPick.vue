<template>
  <div class="yearPick">
    <div class="select-year">
      <div class="mounth">
        <span> {{ activeMounth }}月 </span>
      </div>
      <div class="my-picker">
        <el-date-picker
          popper-class="complex"
          v-model="activeYear"
          :clearable="false"
          type="year"
          :editable="false"
          format="YYYY年"
          value-format="YYYY"
          @change="changeYear"
          prefix-icon=""
        >
          <!--  :suffix-icon="Calendar"-->
        </el-date-picker>
      </div>
    </div>
    <div class="select-mounth-box">
      <div
        class="item"
        v-for="n in 12"
        :key="n"
        :class="{
          'mounth-active': n.toString() == activeMounth,
          disbable: (n.toString() > currentMounth && activeYear >= currentYear) || activeYear > currentYear,
        }"
        @click="checkMounth(n)"
      >
        {{ n }}月
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, reactive, watch } from 'vue'
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
})
const emits = defineEmits(['update:modelValue', 'change'])

const activeYear = ref<string | number>('')
const activeMounth = ref<string | number>('')
const currentMounth = ref<string | number>('')
const currentYear = ref('')
activeMounth.value = new Date().getMonth()
currentMounth.value = new Date().getMonth()

activeYear.value = new Date().getFullYear().toString()
currentYear.value = new Date().getFullYear().toString()

if (new Date().getMonth() === 0) {
  activeMounth.value = 12
  currentMounth.value = 12
  activeYear.value = (new Date().getFullYear() - 1).toString()
  currentYear.value = (new Date().getFullYear() - 1).toString()
}
// .toString().padStart(2,0)
let star = activeYear.value + '-' + activeMounth.value.toString().padStart(2, '0') + '-' + '01'
let end =
  activeYear.value +
  '-' +
  activeMounth.value.toString().padStart(2, '0') +
  '-' +
  new Date(Number(activeYear.value), activeMounth.value, 0).getDate()
emits('update:modelValue', star + ',' + end)
// getMonth() + 1
const changeYear = (val) => {
  if (activeYear.value > currentYear.value) return
  let star = activeYear.value + '-' + activeMounth.value.toString().padStart(2, '0') + '-' + '01'
  let end =
    activeYear.value +
    '-' +
    activeMounth.value.toString().padStart(2, '0') +
    '-' +
    new Date(Number(activeYear.value), Number(activeMounth.value), 0).getDate()
  emits('update:modelValue', star + ',' + end)
  emits('change', star + ',' + end)
}
const checkMounth = (val) => {
  if (activeYear.value > currentYear.value) return
  if (activeYear.value >= currentYear.value && val > currentMounth.value) return
  activeMounth.value = val
  let star = activeYear.value + '-' + activeMounth.value.toString().padStart(2, '0') + '-' + '01'
  let end =
    activeYear.value +
    '-' +
    activeMounth.value.toString().padStart(2, '0') +
    '-' +
    new Date(Number(activeYear.value), Number(activeMounth.value), 0).getDate()
  emits('update:modelValue', star + ',' + end)
  emits('change', star + ',' + end)
}
</script>

<style lang="scss" scoped>
.select-year {
  display: flex;
  padding-bottom: 15px;
  .mounth {
    color: rgba(255, 189, 61, 1);
    font-size: 22px;
    display: flex;
    align-items: center;
    span {
      display: inline-block;
      color: rgba(255, 189, 61, 1);
      font-size: 22px;
      min-width: 50px;
      text-align: center;
    }
    &::after {
      content: '';
      display: block;
      width: 1px;
      height: 16px;
      background: linear-gradient(
        0deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.99) 51%,
        rgba(255, 255, 255, 0) 100%
      );
      opacity: 0.76;
      margin: 0 10px;
    }
  }
  :deep(.my-picker) {
    width: 100px;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2;
    &::after {
      content: '';
      display: block;
      position: absolute;
      right: 0;
      top: 50%;
      width: 0px;
      height: 0px;
      border: 8px solid #fffdef;
      border-color: #fff transparent transparent transparent;
      transform: translateY(-4px);
      z-index: 1;
    }
    .el-date-editor,
    .el-input {
      background-color: transparent;
      position: relative;
      z-index: 2;
    }
    .el-input__wrapper {
      width: 100px;
      padding-left: 0 !important;
      background-color: transparent;
      border: none !important;
      border-color: red !important;
      border-radius: 0;
      --el-input-border: 0px;
      color: #fff;
      font-size: 22px;
      cursor: pointer;
      box-shadow: 0 0 0 0px !important;
    }
    .el-input__inner {
      color: #fff;
    }
    .el-input__prefix {
      display: none;
    }
  }
}
.select-mounth-box {
  color: #fff;
  display: grid;
  grid-template-columns: repeat(6, 46px);
  justify-content: space-between;
  .item {
    width: 46px;
    height: 46px;
    text-align: center;
    line-height: 46px;
    font-size: 20px;
    cursor: pointer;
    margin-bottom: 10px;
  }
  .item:hover {
    background-image: url(@/assets/image/unitMap/mounth-hover.png);
    background-size: 100% 100%;
  }
  .mounth-active {
    background-image: url(@/assets/image/unitMap/mounth-active.png) !important;
    background-size: 100% 100%;
  }
  .disbable {
    color: rgba(94, 116, 139, 1) !important;
    cursor: not-allowed;
    &:hover {
      background-image: none !important;
    }
  }
}
</style>
