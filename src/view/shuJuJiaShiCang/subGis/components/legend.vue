<template>
  <div class="legend grid-cols-2">
    <div
      class="item-box"
      :class="{ active: activeArr.includes(n.type) }"
      v-for="n in list"
      :key="n.icon"
      track
      @click="setActive(n.type)"
    >
      <img :src="n.icon" />
      <div>
        {{ n.title }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import $API from '~/common/api'
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { useUserInfo } from '~/store'
//     // 事件（0：正常 1：火警 2：预警 3：故障 4：隐患 5：动作 7：离线） ','隔开
const activeArr = ref<number[]>([])
const list = reactive([
  {
    title: '正常',
    icon: new URL(`@/assets/image/unitMap/normal.png`, import.meta.url).href,
    type: 0,
  },
  {
    title: '火警',
    icon: new URL(`@/assets/image/unitMap/1.png`, import.meta.url).href,
    type: 1,
  },
  {
    title: '预警',
    icon: new URL(`@/assets/image/unitMap/2.png`, import.meta.url).href,
    type: 2,
  },
  {
    title: '故障',
    icon: new URL(`@/assets/image/unitMap/4.png`, import.meta.url).href,
    type: 3,
  },
  {
    title: '隐患',
    icon: new URL(`@/assets/image/unitMap/3.png`, import.meta.url).href,
    type: 4,
  },
  {
    title: '动作',
    icon: new URL(`@/assets/image/unitMap/5.png`, import.meta.url).href,
    type: 5,
  },
  {
    title: '离线',
    icon: new URL(`@/assets/image/unitMap/7.png`, import.meta.url).href,
    type: 7,
  },
])
const emits = defineEmits(['change'])
onMounted(() => {})

const setActive = (type) => {
  if (activeArr.value.includes(type)) {
    activeArr.value.splice(activeArr.value.indexOf(type), 1)
  } else {
    activeArr.value.push(type)
  }
  emits('change', activeArr.value.join(','))
  // '55'
}
</script>

<style scoped lang="scss">
.legend {
  width: 180px;
  height: 168px;
  background: linear-gradient(0deg, #050e23 0%, #0f234f 98%);
  border: 1px solid rgba(116, 191, 243, 0.5);
  border-radius: 8px;
  display: grid;
  grid-gap: 5px;

  .item-box {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;

    img {
      width: 21px;
      margin-right: 8px;
    }
  }

  .item-box:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .active {
    background: rgba(49, 152, 255, 0.3);
  }
}
</style>
