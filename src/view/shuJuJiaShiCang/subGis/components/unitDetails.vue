<template>
  <div class="tip-box">
    <div class="close-box">
      <div class="title">
        <myTooltip :str="currentGisUnit.name"></myTooltip>
      </div>
      <div class="close flex-shrink-0 pr-20px" @click="closeUnitInfo">
        <el-icon :size="20">
          <Close></Close>
        </el-icon>
      </div>
    </div>
    <!-- <div class="">
      <div class="tab-box mb-15px flex_center">
        <div class="alarm-status" :class="{ 'alarm-status-active': active == 1 }" track @click="handleActive(1)">
          概览
        </div>
        <div class="alarm-status" :class="{ 'alarm-status-active': active == 2 }" track @click="handleActive(2)">
          消防安全月报
        </div>
      </div>
    </div> -->
    <div class="m-15px">
      <div class="mb-20px">
        <card
          :data="unitEventCount"
          :unitId="currentGisUnit.value"
          @itemClick="closeUnitInfo"
          :unitName="currentGisUnit.name"
        ></card>
      </div>
      <div>
        <cardTitle title="隐患趋势"></cardTitle>
        <div>
          <lineChart ref="elHiddenDangerTrend"></lineChart>
        </div>
      </div>
    </div>

    <!-- <div class="monthlyReport" v-if="active == 2">
      <div style="color: #000000">
        <yearPick v-model="activeDate" @change="yearPickChange"></yearPick>
      </div>
      <div>
        <cardTitle title="安全指数"> </cardTitle>
        <safetyIndex ref="elSafetyIndex"></safetyIndex>
      </div>

      <div>
        <cardTitle title="报警趋势"></cardTitle>
        <div>
          <lineChart ref="elHandle"></lineChart>
        </div>
      </div>

      <div>
        <cardTitle title="火灾报警"></cardTitle>
        <card :data="fireList"></card>
      </div>

      <div>
        <cardTitle title="预警" :showIcon="false" :size="16"></cardTitle>
        <card :data="warningList"></card>
      </div>

      <div>
        <cardTitle title="隐患" :showIcon="false" :size="16"></cardTitle>
        <card :data="dangerList"></card>
      </div>

      <div>
        <cardTitle title="安全工作"></cardTitle>
        <safeWork :data="complete" title="完成率"></safeWork>
        <safeWork title="规范率" :data="standard"></safeWork>
      </div>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
import cardTitle from './overview/cardTitle.vue'
import lineChart from './overview/lineChart.vue'

import yearPick from './monthlyReport/yearPick.vue'
import safetyIndex from './monthlyReport/safetyIndex.vue'
import card from './monthlyReport/card.vue'
// import card from './overview/topCard.vue'

import safeWork from './monthlyReport/safeWork.vue'

import $API from '~/common/api'
import { ref, computed, onMounted, reactive, watch, nextTick, onUnmounted } from 'vue'

import { Close } from '@element-plus/icons-vue'
import { unitMapStore } from '~/store'
import PubSub from 'pubsub-js'

let unitMapRefresh = PubSub.subscribe('unitMapRefresh', async () => {
  overviewInit()
})

const unitMapData = unitMapStore()
const active = ref(1)
const elHiddenDangerTrend = ref()
const elHandle = ref()
const elSafetyIndex = ref()
const activeDate = ref('')
const showOverview = ref(false)
const scoreData = ref<any>({})

const currentGisUnit = computed<any>(() => unitMapData.currentGisUnit)
const isShowDetails = computed(() => unitMapData.isShowMapDetail)
watch(isShowDetails, (v) => {
  if (v) showOverview.value = true
})
const fireList = reactive([
  {
    title: '报警数',
    num: 0,
    className: 'fire',
    key: 'total',
    compnay: '起',
  },
  {
    title: '设备误报',
    num: 0,
    className: 'fire',
    key: 'resFalseNum',
    compnay: '起',
  },
  {
    title: '真警',
    num: 0,
    className: 'fire',
    key: 'resTureNum',
    compnay: '起',
  },
  {
    title: '复核完成率',
    num: 0,
    className: 'complete',
    key: 'complateRate',
    compnay: '',
  },
  {
    title: '规范率',
    num: 0,
    className: 'complete',
    key: 'standardRate',
    compnay: '',
  },
])
const warningList = reactive([
  {
    title: '预警数',
    num: 0,
    className: 'warning',
    key: 'total',
    compnay: '起',
  },
  {
    title: '未处置预警数',
    num: 0,
    className: 'warning',
    key: 'disResNoDealNum',
    compnay: '起',
  },
])
const dangerList = reactive([
  {
    title: '消防隐患数',
    num: 0,
    className: 'hidden-danger',
    key: 'total',
    compnay: '起',
  },
  {
    title: '未完成整改',
    num: 0,
    className: 'hidden-danger',
    key: 'undoneNum',
    compnay: '起',
  },
])

const unitEventCount = ref([
  {
    title: '火警',
    num: 0,
    className: 'fire cursor-pointer',
    key: 'alarmNum',
    compnay: '起',
    tip: `火警 ：火灾自动报警系统、独立式探测设备及烟火视频分析设备上报的火警信号。`,
    // JumpUrl: '/fireRemoteManage/realTimeMonitor',
    monitorState: '1',
  },
  {
    title: '预警',
    num: 0,
    className: 'warning cursor-pointer',
    key: 'warningNum',
    compnay: '起',
    tip: `预警：在火灾发生前，监测到的火灾前兆信号，如电气火灾监测装置上报的过温信号。`,
    // JumpUrl: '/fireRemoteManage/realTimeMonitor',
    monitorState: '2',
  },
  {
    title: '故障',
    num: 0,
    className: 'fault cursor-pointer',
    key: 'faultNum',
    compnay: '起',
    tip: `故障：由消防设施、独立式消防设备上报到平台的"故障"信号，此时设备无法达到预期设定的工况。`,
    // JumpUrl: '/fireRemoteManage/realTimeMonitor',
    monitorState: '3',
  },
  {
    title: '隐患',
    num: 0,
    className: 'hidden-danger cursor-pointer',
    key: 'troubleNum',
    compnay: '起',
    tip: `隐患：通过监测设备监测到的消防隐患，例如水压监测装置监测到的消防管网水压不足，水位监测装置监测到的消防水池水位不足，或由智能视频分析仪监测到的消防控制室人员离岗等。`,
    // JumpUrl: '/fireRemoteManage/realTimeMonitor',
    monitorState: '4',
  },
  {
    title: '动作',
    num: 0,
    className: 'action cursor-pointer',
    key: 'movingNum',
    compnay: '起',
    tip: `动作：由火灾自动报警主机上报的启动、停止、反馈信号。`,
    // JumpUrl: '/fireRemoteManage/realTimeMonitor',
    monitorState: '5',
  },

  {
    title: '离线',
    num: 0,
    className: 'off-line cursor-pointer',
    key: 'offlineNum',
    compnay: '起',
    tip: `离线：指通过平台规则判定的设备的联网状态为离线。`,
    // JumpUrl: '/fireRemoteManage/realTimeMonitor',
    onlineState: '1',
    monitorState: '7',
  },
])
const complete = reactive([
  {
    name: '消控室值班',
    isStandard: false,
    num: '0',
    key: 'controlRoomCompleteRate',
  },
  {
    name: '防火巡查',
    isStandard: false,
    num: '0',
    key: 'patrolCompleteRate',
  },
  {
    name: '防火检查',
    isStandard: false,
    num: '0',
    key: 'inspectCompleteRate',
  },
])
const standard = reactive([
  {
    name: '消控室值班',
    isStandard: true,
    num: '0',
    key: 'controlRoomStandardRate',
  },
  {
    name: '防火巡查',
    isStandard: true,
    num: '0',
    key: 'patrolStandardRate',
  },
])

onMounted(() => {
  // overviewInit()
  nextTick(() => {
    overviewInit()
    // if (timer.value) clearInterval(timer.value)
    // timer.value = setInterval(() => {
    //   getUnitEventCount()
    // }, 8000)
  })
})
onUnmounted(() => {
  // if (timer.value) {
  //   clearInterval(timer.value)
  // }
  PubSub.unsubscribe(unitMapRefresh)
})
watch(activeDate, (n) => {
  console.log(n)
  // nextTick(()=>{
  //   monthlyReportInit()
  // })
})
const yearPickChange = () => {
  nextTick(() => {
    monthlyReportInit()
  })
}

const closeUnitInfo = () => {
  // console.log('close----------')
  unitMapData.isShowMapDetail = false
  // currentGisUnit.value = {}
}

const handleActive = (type) => {
  console.log('activeDate-----', activeDate.value)
  active.value = type
  if (type == 1) {
    nextTick(() => {
      overviewInit()
    })
  } else {
    nextTick(() => {
      monthlyReportInit()
    })
  }
}
const overviewInit = () => {
  getUnitEventCount()
  getEventTrendByMonth([4])

  // getUnitSafetyTrend()
  // getUnitScoreData()
}

const monthlyReportInit = () => {
  getUnitSafetyIndex()
  getEventTrendByMonth([1, 2])
  getStatistiscEventData(1)
  getStatistiscEventData(2)
  getStatistiscEventData(4)
  getInspectionCompleteCondition()
}

// 单位概览 --star

const getUnitEventCount = async () => {
  // 查询 概览-顶部card
  const params = {
    // orgCodeuserInfo.value.orgCode,
    unitId: currentGisUnit.value.value,
    unitType: '0',
    modelType: 'unit_base_url',
  }
  let res: any = {}
  try {
    res = await $API.post({
      url: '/unit/distribution/getUnitEventCount',
      params,
    })
    console.log('概览-顶部card----------', res)
    if (res.code != 'success') return
    unitEventCount.value.forEach((i) => {
      i.num = res.data[i.key]
    })
  } catch (e) {
    //TODO handle the exception
    console.log(e)
    // showLoading.value = false
  }
}
// /unit/distribution/getUnitScoreData
const getUnitScoreData = async () => {
  // 综合安全评分-获取该业主单位当月安全趋势
  const params = {
    // orgCodeuserInfo.value.orgCode,
    unitId: currentGisUnit.value.value,
  }
  let res: any = {}
  try {
    res = await $API.post({
      url: '/unit/distribution/getUnitScoreData',
      params,
    })
    console.log('综合安全评分-安全指数---', res.data)
    if (res.code != 'success') return
    scoreData.value = res.data
  } catch (e) {
    console.log(e)
  }
}

const getUnitSafetyTrend = async () => {
  // 综合安全评分-获取该业主单位当月安全趋势
  const params = {
    // orgCodeuserInfo.value.orgCode,
    unitId: currentGisUnit.value.value,
  }
  let res: any = {}
  try {
    res = await $API.post({
      url: '/unit/distribution/getUnitSafetyTrend',
      params,
    })
    console.log('综合安全评分-获取该业主单位当月安全趋势---', res.data)
    if (res.code != 'success') return
    elSafetyIndex.value.initData(
      [
        {
          name: '安全指数',
          color: 'rgba(73, 184, 255, 1)',
          data: [...res.data.ja_y],
        },
      ],
      [...res.data.ja_x]
    )
  } catch (e) {
    console.log(e)
  }
}

const getEventTrendByMonth = async (type: any[] = []) => {
  //  月报 概览 通用传值为数组  企业单位：获取当月火警、预警、隐患趋势图
  try {
    let postArr: any[] = []
    type.forEach((i) => {
      let params = {
        unitId: currentGisUnit.value.value,
        type: i,
        startDate: '',
        endDate: '',
        modelType: 'unit_base_url',
      }
      if (type.length >= 2) {
        params.startDate = activeDate.value.split(',')[0]
        params.endDate = activeDate.value.split(',')[1]
      }
      postArr.push(
        $API.post({
          url: '/unit/distribution/getEventTrendByMonth',
          params,
        })
      )
    })

    Promise.all([...postArr])
      .then((result) => {
        console.log('Promise--------', result) //['成功了', 'success']
        if (result.length == 1) {
          let res: any = result[0]
          if (res.code != 'success') return
          elHiddenDangerTrend.value.initData(
            [
              {
                name: '隐患趋势',
                color: 'rgba(255, 75, 75, 1)',
                data: [...res.data.data_y],
              },
            ],
            [...res.data.data_x]
          )
        } else {
          let xdata: any = [
            {
              name: '火警次数',
              color: 'rgba(244, 54, 54, 1)',
              data: [],
            },
            {
              name: '预警次数',
              color: 'rgba(159, 29, 139, 1)',
              data: [],
            },
          ]
          let yData = []
          xdata.forEach((item, index) => {
            let res = result[index]
            if (res.code != 'success') return
            item.data = res.data.data_y
            yData = res.data.data_x
          })
          elHandle.value.initData(xdata, [...yData])
        }
      })
      .catch((error) => {
        console.log(error)
        if (type.length == 1) {
          elHiddenDangerTrend.value &&
            elHiddenDangerTrend.value.initData(
              [
                {
                  name: '隐患趋势',
                  color: 'rgba(255, 75, 75, 1)',
                  data: [],
                },
              ],
              []
            )
        } else {
          elHandle.value.initData(
            [
              {
                name: '火警次数',
                color: 'rgba(244, 54, 54, 1)',
                data: [],
              },
              {
                name: '预警次数',
                color: 'rgba(159, 29, 139, 1)',
                data: [],
              },
            ],
            []
          )
        }
      })
  } catch (e) {
    console.log(e)
  }
}
// 单位概览 --end

// 安全月报 --star
const getUnitSafetyIndex = async () => {
  //  综合安全评分-统计企业单位当月安全指数数据
  const params = {
    // orgCodeuserInfo.value.orgCode,
    unitId: currentGisUnit.value.value,
    startTime: activeDate.value.split(',')[0] + ' 00:00:00',
    endTime: activeDate.value.split(',')[1] + ' 23:59:59',
  }
  let res: any = {}
  try {
    res = await $API.post({
      url: '/unit/distribution/getUnitSafetyIndex',
      params,
    })
    if (res.code != 'success') return
    elSafetyIndex.value.initData(res.data)
  } catch (e) {
    console.log(e)
  }
}
const getStatistiscEventData = async (type) => {
  //  企业单位-安全月报：获取火警、预警、隐患相关数据

  let res: any = {}
  const params = {
    unitId: currentGisUnit.value.value,
    startTime: activeDate.value.split(',')[0],
    endTime: activeDate.value.split(',')[1],
    eventType: type,
    clientType: type == 1 ? 4 : 1,
  }
  try {
    res = await $API.post({
      url: '/unit/distribution/statistiscEventData',
      params,
    })
    if (res.code != 'success') return
    if (type == 1) {
      fireList.forEach((i) => {
        i.num = res.data[i.key] || 0
      })
    }
    if (type == 2) {
      warningList.forEach((i) => {
        i.num = res.data[i.key] || 0
      })
    }
    if (type == 4) {
      dangerList.forEach((i) => {
        i.num = res.data[i.key] || 0
      })
    }
  } catch (e) {
    console.log(e)
  }
}
const getInspectionCompleteCondition = async () => {
  //  企业单位：统计企业单位消控室值班、巡检巡查完成情况

  let res: any = {}
  const params = {
    unitId: currentGisUnit.value.value,
    startTime: activeDate.value.split(',')[0],
    endTime: activeDate.value.split(',')[1],
  }
  try {
    res = await $API.post({
      url: '/unit/distribution/getInspectionCompleteCondition',
      params,
    })
    console.log('getInspectionCompleteCondition-----', res)
    if (res.code != 'success') return
    // complete
    // standard
    standard.forEach((i) => {
      i.num = res.data[i.key]
    })
    complete.forEach((i) => {
      i.num = res.data[i.key]
    })
  } catch (e) {
    console.log(e)
  }
}

// 安全月报 --end
</script>

<style lang="scss" scoped>
.tip-box {
  width: 446px;
  overflow: hidden;
  height: 100%;
  color: #fff;
  padding-bottom: 20px;
  background-color: rgba(0, 16, 80, 0.6);
  border: 1px solid rgba(0, 78, 188, 1);
  padding-top: 0;
}

.ttile-tip-box {
  display: flex;
  align-items: center;
  height: 100%;
}

.ttile-tip {
  min-width: 60px;
  height: 24px;
  line-height: 24px;
  border: 1px solid #63dde0;
  background: linear-gradient(0deg, #004188 0%, #00d6d8 98%);
  border-radius: 12px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // text-align: center;
}

.slide-fade-enter-active {
  transition: all 0.6s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s ease-out;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(100%);
}

.isShowBtn-show {
  position: absolute;
  left: 0px;
  top: 40%;
  width: 24px;
  transform: translateX(-100%);
}

.isShowBtn-hidden {
  position: absolute;
  right: 0px;
  top: 40%;
  width: 24px;
  transform: rotateY(180deg) translateX(-15px);
}

.close-box {
  display: flex;
  justify-content: space-between;
  background-image: url(@/assets/image/unitMap/title-bg.png);
  background-size: 100% 100%;
  height: 45px;
  align-items: center;
  margin-bottom: 18px;

  .title {
    font-size: 18px;
    padding-left: 40px;
  }
  .close-icon {
    padding-right: 20px;
  }
}
.tab-box {
  display: flex;

  .alarm-status {
    width: 110px;
    cursor: pointer;
    text-align: center;
    padding: 5px 0px;
    background: #042550;
    border: 1px solid #3f9bcf;
    box-shadow: inset 0px 0px 6px 0px #65b6ff;
  }

  .alarm-status-active {
    background-image: url(@/assets/image/unitMap/tabs-active.png);
    background-size: 100% 100%;
  }
}

.flex_center {
  justify-content: center;
}

.monthlyReport {
  height: calc(100% - 60px - 48px);
  overflow: auto;
  padding: 15px;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar:horizontal {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    border-radius: 10px;
    background: #36425e;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(56, 107, 187, 1);
    border-radius: 10px;
    transition: all 0.2s ease-in-out;
  }
}
</style>
