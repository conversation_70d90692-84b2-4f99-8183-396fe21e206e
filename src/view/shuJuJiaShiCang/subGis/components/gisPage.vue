<template>
  <div class="gisMap" id="gisMap"></div>
  <div style="height: 100%; width: 100%" v-show="dialogVisible">
    <floorGis @close="dialogVisible = false" ref="elfloorGis" v-if="dialogVisible" :buildId="buildId"></floorGis>
  </div>
  <div class="floorMap-box" v-if="showFloorMap">
    <div class="floor-main" :class="{ full: isFull }">
      <floorMap
        v-if="showFloorMap"
        :showDevicceInfo="true"
        :currentBuildInfo="currentBuildInfo"
        :currentUnit="currentGisUnit"
        :deviceInfo="deviceInfo"
        @full="setisfull"
        @close="showFloorMap = false"
        ref="ElfloorMap"
      ></floorMap>
    </div>
  </div>
  <div class="search-box">
    <gisSearch :list="gisSearchList" @change="searchChange"></gisSearch>
  </div>
  <div class="legend-box">
    <gislegend @change="legendChange"></gislegend>
  </div>
</template>

<script lang="ts" setup>
import $API from '~/common/api'
import gisConfig from '@/config/gisConfig.js'
import floorGis from './floorGis/index.vue'
import floorMap from './floorGis/floorMap.vue'
// import gisSearch from './components/overview/gisSearch.vue'
// import gisSearch from './search.vue'

import { setPopup } from '@/components/gisPage/gisTip/index.js'
import PubSub from 'pubsub-js'
import { ref, computed, onMounted, reactive, watch, onUnmounted, nextTick } from 'vue'
import gislegend from './legend.vue'
import { unitMapStore, useUserInfo, useCounterStore } from '~/store'

const userInfo = useUserInfo()

// userInfo.value.orgCode = 'bbeda7195cea46e8a5a63d5ee1b178db'
const unitMapData = unitMapStore()
const dialogVisible = ref(false)
const buildId = ref('')
const showFloorMap = ref(false)
const elfloorGis = ref()
const counterStore: any = useCounterStore()
const isCollapse = computed(() => counterStore.isCollapse)
let gisMap: any = null
const ElfloorMap: any = ref('')

watch(
  isCollapse,
  () => {
    setTimeout(() => {
      gisMap && gisMap.updateSize && gisMap.updateSize()
    }, 250)
  },
  { immediate: false }
)

// 地图参数
interface unitListItem {
  unitEnglishName: string
  fireControlRoomTel: string
  city: string
  subCenterCode: string
  aerialMapType: number
  county: string
  aerialviewImg: string
  eventType: string
  contactsName: string
  floorMapType: number
  isAerialMap: number
  province: string
  cityName: string
  contactsTel: string
  name: string
  unitShortName: string
  unitPointY: string
  provinceName: string
  unitPointX: string
  serviceModelCode: number
  value: string
  countyName: string
}
const isFull = ref(false)
const currentUnitList = ref<unitListItem[]>([])
const currentGisUnit = computed(() => unitMapData.currentGisUnit)
const currentBuildInfo = ref({
  ownerId: '',
  subCenterCode: '',
  buildId: '',
  ownerType: 0,
})
const deviceInfo = ref({})

const layerObj: {
  unitLayer: any
  unitVectorLayer: any
} = {
  unitLayer: null,
  unitVectorLayer: null,
}
interface listItem {
  label: string
  value: string
}
const gisSearchList = ref<listItem[]>([])
const clusterStyleCache = reactive({})
const pointStyleCache = reactive({
  unitLayer: {},
  familyLayer: {},
  serviceLayer: {},
  personLayer: {},
})
const pointPopup = ref()
const pointTip = ref()
const pointListParams = reactive({
  orgCode: userInfo.value.orgCode,
  // orgCode: 'bbeda7195cea46e8a5a63d5ee1b178db',

  eventType: '',
})
const _textFill = new ol.style.Fill({
  color: '#fff',
})
const _textStroke = new ol.style.Stroke({
  color: 'rgba(0, 0, 0, 0.6)',
  width: 2,
})
const isShowDetails = computed(() => unitMapData.isShowMapDetail)

watch(isShowDetails, (n) => {
  if (!n && pointPopup.value) {
    pointPopup.value.close()
  }
})
const showPopup = (data, isShowPopur = false) => {
  if (currentUnitList.value.length > 0 && data && data.unitId) {
    let arr = currentUnitList.value.filter((i) => i.value == data.unitId)
    if (arr.length > 0) {
      let obj = arr[0]
      if (isShowPopur) {
        if (pointPopup.value) {
          pointPopup.value.close()
        }
        unitMapData.isShowMapDetail = true
        unitMapData.currentGisUnit = obj
        addPopup({ data: obj, layerName: 'unitVectorLayer' })
      }

      gisMap.zoomTo({
        center: [obj.unitPointX, obj.unitPointY],
        zoom: 16,
      })
    }
  }
}

let UNITCHANGEMAPZOOMTO = PubSub.subscribe('UNITCHANGEMAPZOOMTO', async (msg: string, data: any) => {
  showPopup(data)
})

let unitMapRefresh = PubSub.subscribe('unitMapRefresh', async () => {
  setPointsData()
})
// PubSub.publish('unitMapRefresh')

let TOFLOORINFO = PubSub.subscribe('TOFLOORINFO', async (msg: string, data: any) => {
  // return;
  if (currentUnitList.value.length > 0 && data.unitId) {
    let arr = currentUnitList.value.filter((i) => i.value == data.unitId)
    if (arr && arr.length > 0) {
      let obj = arr[0]
      // data.buildId||data.buildingId
      unitMapData.currentGisUnit = obj
      // currentBuildInfo.value = data
      currentBuildInfo.value.buildId = data.buildingId || data.buildId
      currentBuildInfo.value.ownerId = data.unitId
      currentBuildInfo.value.subCenterCode = data.subCenterCode

      deviceInfo.value = data
      // buildId.value = obj.buildingId||obj.buildId
      showFloorMap.value = true
      nextTick(() => {
        // ElfloorMap.value.initFloorMap()
      })
      // sendDeviceInfo({...obj,...data})
    }
  }
})
onMounted(() => {
  initMap()
  init()
})

onUnmounted(() => {
  try {
    PubSub.unsubscribe(UNITCHANGEMAPZOOMTO)
    PubSub.unsubscribe(TOFLOORINFO)
    PubSub.unsubscribe(unitMapRefresh)
  } catch (e) {
    //TODO handle the exception
  }
  if (gisMap) {
    gisMap.release(false)
    gisMap = undefined
    layerObj.unitLayer = null
    layerObj.unitVectorLayer = null
    pointStyleCache.unitLayer = {}
    pointStyleCache.familyLayer = {}
    pointStyleCache.serviceLayer = {}
    pointStyleCache.personLayer = {}
  }
})

const searchChange = (val) => {
  if (pointPopup.value) {
    pointPopup.value.close()
  }
  unitMapData.isShowMapDetail = true
  let obj: any = currentUnitList.value.find((i) => i.value === val.value)

  addPopup({ data: obj, layerName: 'unitVectorLayer' })
  unitMapData.currentGisUnit = obj

  setTimeout(() => {
    gisMap.zoomTo({
      center: [obj.unitPointX, obj.unitPointY],
      zoom: 16,
    })
  }, 500)
  // unitPointX: 13055357.53
  // unitPointY: 3710320.81
}
const legendChange = (type) => {
  // clearCustomizeClusterLayer
  gisMap.clearCustomizeClusterLayer(layerObj.unitLayer)
  gisMap.clearCustomizeVectorLayer(layerObj.unitVectorLayer)
  if (pointPopup.value) {
    pointPopup.value.close()
  }
  pointListParams.eventType = type
  setPointsData()
}
const setisfull = () => {
  isFull.value = !isFull.value
}
const init = () => {
  if (gisMap) {
    let el = gisMap.getElement()
    el.style.opacity = 0.1
    gisMap.clearCustomizeClusterLayer(layerObj.unitLayer)
    gisMap.clearCustomizeVectorLayer(layerObj.unitVectorLayer)
    if (pointPopup.value) {
      pointPopup.value.close()
    }
    setPointsData()
  }
}

const getPointList = () => {
  // 事件（0：正常 1：火警 2：预警 3：故障 4：隐患 5：动作 7：离线） ','隔开
  const params = {
    ...pointListParams,
    modelType: 'unit_base_url',
  }
  return $API.post({
    url: '/unit/distribution/mapLocation',
    params,
  })
}

// 地图

const initMap = () => {
  const { tileURL, center } = gisConfig
  gisMap = new window.IndoorMap({
    target: 'gisMap',
    tile: false,
    tileURL: tileURL,
    maxZoom: 22,
    minZoom: 4,
    zoom: 15,
    center: center,
    isVector: true,
    deviceIconAlarmGifUrl: '',
    multiWorld: false,
    onLoad: mapOnLoad,
  })
  let el = gisMap.getElement()
  el.style.opacity = 0.1
  setTimeout(() => {
    el.style.opacity = 1
  }, 800)
  const pMap = GSMap.Plugin.new_PMap_ex({
    forceRenderType: 'WebGL',
    mapMain: gisMap,
    styleJson: GSMap.Plugin.BaiDu.PMapStyleArray[4], // 百度地图风格 4 蓝色  ，东岳客户要求白色背景
  })
  // pMap.setMapStyle(GSMap.Plugin.BaiDu.PMapStyleArray[4]);
  ;(window as any).unitGisMap = gisMap
  gisMap.BindSynchronousDo(pMap)
}
const mapOnLoad = (map) => {
  // 初始化图层
  initLayer(map)
  // // 绑定事件
  mapEvent(map)
  // // 设置撒点数据
  // setPointsData()
  // // 设置监听
  // this.subscribe();
}
const initLayer = (map) => {
  // // 企业单位，聚合图层 & 企业单位矢量图层 （ 做视图切换，防止坐标重复或相近的点只展示聚合 ）
  layerObj.unitLayer = createClusterLayer(map)
  layerObj.unitLayer.setMinResolution(4)
  let _source = layerObj.unitLayer.getSource().getSource()
  layerObj.unitVectorLayer = map.createCustomizeVectorLayer({
    name: 'unitVectorLayer',
    source: _source,
    extent: (window as any).CONST_Extent_WebMC || [],
  })
  layerObj.unitVectorLayer.setMaxResolution(4)
}

const createClusterLayer = (map) => {
  let cl = map.createCustomizeClusterLayer({
    name: 'unitLayer',
    distance: 45,
    minDistance: 45,
    source: null,
    style: styleFunction,
    extent: (window as any).CONST_Extent_WebMC || [],
  })
  ;(window as any).cl = cl
  return cl
}
const styleFunction = (feature, resolution) => {
  const size = feature.get('features').length //每个点当前的聚合点数
  // // 单个点，直接返回点样式
  if (size === 1) return feature.get('features')[0].getStyle()
  // //计算每个聚合点的半径大小
  calculateClusterInfo(feature, resolution)

  let cacheKey = `${size}-${feature.get('hasAlarm')}`
  let style = clusterStyleCache[cacheKey]

  if (!style) {
    style = new ol.style.Style({
      image: new ol.style.Circle({
        radius: feature.get('radius'), //获取聚合圆圈的半径大小，聚合的点数越多，圆圈的半径越大
        fill: new ol.style.Fill({
          color: feature.get('fillColor'),
        }),
      }),
      text: new ol.style.Text({
        font: 'bold 18px serif',
        text: size.toString(),
        fill: _textFill,
        stroke: _textStroke,
      }),
    })
    clusterStyleCache[size] = style
  }
  return style
}
const calculateClusterInfo = (feature, resolution) => {
  // 计算点位大小 样式
  const { createEmpty, extend, getWidth, getHeight } = ol.extent

  let innerFeatures = feature.get('features')
  let hasAlarm = '0'

  let extent = createEmpty()
  let j, jj
  for (j = 0, jj = innerFeatures.length; j < jj; ++j) {
    extend(extent, innerFeatures[j].getGeometry().getExtent())
  }
  const _v = innerFeatures.find((data) => {
    return data.gsData.eventType === '1'
  }) // 如果eventType=1 说明火警 显示红色
  if (_v) hasAlarm = '1'

  let radius = (0.25 * (getWidth(extent) + getHeight(extent))) / resolution

  let fillColor = hasAlarm === '1' ? `rgba(255, 0, 0, 0.6)` : `rgba(0, 0, 255, 0.6)`

  feature.set('radius', Math.max(20, radius))
  feature.set('fillColor', fillColor)
  feature.set('hasAlarm', hasAlarm)
}
const setPointsData = async () => {
  let res,
    _data: any = []
  try {
    res = await getPointList()
    if (gisMap) {
      let el = gisMap.getElement()
      el.style.opacity = 1
    }
    if (res.code == 'success') {
      _data = res.data
      currentUnitList.value = _data.unitList
      gisSearchList.value = _data.unitList.map((i) => {
        return {
          value: i.value,
          label: i.name,
        }
      })
    }
  } catch (e) {
    //TODO handle the exception
    // console.log(e)
  }

  if (!_data) return
  const { unitList } = _data

  addPointsToLayer(unitList, 'unitLayer')
}

const addPointsToLayer = (points, layerName) => {
  if (!points || !points.length) return
  const layer = layerObj[layerName]
  let dwArray: any[] = [] //聚合图层
  let qtArray: any[] = [] //普通图层
  // console.time();
  points.forEach((data) => {
    const { unitPointX, unitPointY } = data

    if (unitPointX && unitPointY && !isNaN(unitPointX) && !isNaN(unitPointY)) {
      data.unitPointX = Number(unitPointX)
      data.unitPointY = Number(unitPointY)
      if (!GISShare.SMap.Fitting.CHINA.OutOfChinaExtent_WebMercator_BD09MC(data.unitPointX, data.unitPointY)) {
        if (layerName === 'unitLayer') {
          dwArray.push(data)
          // that.map.addPointToCustomizeClusterLayer(layer, data, "unitPointX", "unitPointY", undefined, _style);
        } else {
          qtArray.push(data)
          // that.map.addPointToCustomizeVectorLayer(layer, data, "unitPointX", "unitPointY", undefined, _style);
        }
      } else {
        console.error(data)
      }
    }
  })
  console.log('点位-----', dwArray, qtArray)
  // 有聚合则添加聚合点
  if (dwArray.length > 0) {
    // 先清除聚合点，否则会重复添加
    gisMap && gisMap.clearCustomizeClusterLayer(layer)
    gisMap &&
      gisMap.addPointsToCustomizeClusterLayer(layer, dwArray, 'unitPointX', 'unitPointY', undefined, function (data) {
        return setPointStyle(layerName === 'personLayer' ? data.onlineStatus : data.eventType, layerName)
      })
  }
  // 添加普通点样式
  if (qtArray.length > 0)
    gisMap.addPointsToCustomizeVectorLayer(layer, qtArray, 'unitPointX', 'unitPointY', undefined, function (data) {
      return setPointStyle(layerName === 'personLayer' ? data.onlineStatus : data.eventType, layerName)
    })
  // console.timeEnd();
  // 图层存在后设置地图中心点
  // pExtent.Expand(2.0, 2.0)
  // console.log('layer.getSource().getExtent()------------', layer.getSource().getExtent())
  // debugger
  let extent
  if (dwArray.length > 0) {
    extent = layer && layer.getSource().getSource().getExtent()
  } else {
    extent = layer && layer.getSource().getExtent()
  }
  const pExtent = extent && new GISShare.SMap.Geometry.BoundingBox(extent[0], extent[3], extent[2], extent[1])
  pExtent && pExtent.Expand(2.0, 2.0)
  gisMap &&
    gisMap.zoomToExtent(
      undefined,
      undefined,
      [pExtent.getLeft(), pExtent.getBottom(), pExtent.getRight(), pExtent.getTop()],
      {
        nearest: true,
      }
    )
  ;(window as any).winGisMap = gisMap
}
const setPointStyle = (eventType, layerName) => {
  // console.log('setPointStyle-----', eventType, layerName)
  const { symbols } = gisConfig
  let currentLayer = pointStyleCache[layerName]
  let pointStyle = currentLayer[eventType]
  if (!pointStyle) {
    const _img = layerName === 'serviceLayer' ? symbols[layerName]['icon'] : symbols[layerName][eventType]['icon']
    currentLayer[eventType] = pointStyle = new ol.style.Style({
      image: new ol.style.Icon({
        src: _img,
        anchor: [0.5, 1],
      }),
    })
  }
  return pointStyle
}
const mapEvent = (map) => {
  // map click
  map.onMouseClick = function (e) {
    // TODO
    // console.log(layerObj.unitLayer);
    let geoObject = map.getGeoObjectByClientXY(layerObj.unitLayer, e.pixel[0], e.pixel[1])
    let geoObject2 = map.getGeoObjectByClientXY(layerObj.unitVectorLayer, e.pixel[0], e.pixel[1])
    let features = []
    if (geoObject) {
      // console.log('gsData-----',geoObject.get('features')[0].gsData);
      features = geoObject.get('features')
    } else if (geoObject2) {
      // console.log('gsData-2----',geoObject2.gsData);
      let elementInfo = geoObject2.gsData
      unitMapData.isShowMapDetail = true
      unitMapData.currentGisUnit = elementInfo
      addPopup({ data: elementInfo, layerName: 'unitVectorLayer' })
      return
    }

    if (features && Array.isArray(features) && features.length == 1) {
      // 聚合图层只有一个点位
      // console.log('gsData---聚合图层只有一个点位--',geoObject.get('features')[0].gsData);
      let elementInfo = geoObject.get('features')[0].gsData
      unitMapData.isShowMapDetail = true
      unitMapData.currentGisUnit = elementInfo
      addPopup({ data: elementInfo, layerName: 'unitLayer' })
      return
    }

    if (features && Array.isArray(features) && features.length > 1) {
      // console.log('聚合图层不止一个点位');
      const pGeometryCollection = new GISShare.SMap.Geometry.MultiPoint()
      features.forEach((ele: any) => {
        let xyArray = ele.getGeometry().getCoordinates()
        pGeometryCollection.Add(new GISShare.SMap.Geometry.Point(xyArray[0], xyArray[1]))
      })
      const pExtent = pGeometryCollection.getEnvelope()
      if (pExtent.getWidth() > 0 || pExtent.getHeight() > 0) {
        pExtent.Expand(2.0, 2.0)
        gisMap.zoomToExtent(undefined, undefined, [
          pExtent.getLeft(),
          pExtent.getBottom(),
          pExtent.getRight(),
          pExtent.getTop(),
        ])
      } else {
        gisMap.setZoom(16)
        gisMap.setCenter([pExtent.getLeft(), pExtent.getBottom()])
      }
    }
  }
}

const addPopup = (pointInfo) => {
  const { unitPointX, unitPointY } = pointInfo.data
  const coordinate = [unitPointX, unitPointY]
  if (!pointPopup.value) {
    let _container = document.createElement('div')
    _container.classList.add('gis-popup')
    _container.innerHTML = `<div id="gisPopup"></div>`
    // debugger
    pointPopup.value = gisMap.addPopup({
      element: _container,
      autoPan: false,
      offset: [0, -65],
      positioning: 'bottom-center',
    })
  }
  pointPopup.value['target'] = pointInfo
  pointPopup.value.show(coordinate)

  let hasBtn = true
  if (pointInfo.layerName === 'unitLayer' || pointInfo.layerName === 'unitVectorLayer') {
    hasBtn = true
  } else {
    hasBtn = false
  }
  // 挂载popup组件
  // if(!pointTip.value){
  let _docu = pointPopup.value.getElement()
  pointTip.value = setPopup({
    elment: _docu.firstChild,
    options: {
      onClick: popupClick,
      name: pointInfo.data.name,
      hasBtn,
    },
  })
  gisMap.updateSize()
  // }
  // pointTip.value.name = pointInfo.data.name;
}
const popupClick = () => {
  // console.log('popupClick----------')
  dialogVisible.value = true
  // elfloorGis.value.initdata()
  // const _pointTag = pointPopup["target"].data;
  // localStorage.setItem("mapClickCurrentUnit", _pointTag.value);
  // localStorage.setItem("mapClickCurrentCode", this.adminInfo.orgCode);
  // // 存储服务模式
  // localStorage.setItem("currentUnitServiceModel", _pointTag.serviceModelCode);
  // let _unitType = this.pointPopup["target"].layerName == "unitLayer" ? 0 : _pointTag.ownerType;
  // // 存储单位类型
  // localStorage.setItem("currentUnitType",_unitType);
  // this.gisFloorVisiable = true;
}

const showUnitPopup = (data) => {
  // console.log('showUnitPopup--------', data)
  nextTick(() => {
    setTimeout(() => {
      showPopup(data, true)
    }, 1500)
  })
}

defineExpose({ showUnitPopup })
</script>

<style lang="scss" scoped>
.gisMap {
  width: 100%;
  height: 100%;
  // background-color: red;
}

.floorMap-box {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 11;
  display: flex;
  align-items: center;
  justify-content: center;

  .floor-main {
    width: 80%;
    height: 85%;
    background-color: #fff;
  }

  .full {
    width: 100%;
    height: 100%;
  }
}

.search-box {
  position: absolute;
  left: 15px;
  top: 115px;
}

.legend-box {
  position: absolute;
  left: 15px;
  bottom: 33px;
}
</style>
