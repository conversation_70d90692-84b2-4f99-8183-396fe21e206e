<template>
  <div class="tip-box">
    <div class="close-box">
      <div class="title">实时监测</div>
      <div class="close-icon" @click="close">
        <el-icon :size="20" @click="close">
          <Close />
        </el-icon>
      </div>
    </div>
    <div class="m-15px mt-0px mb-25px">
      <topCard type="safe" subTitle="数据总览" ref="EltopCard"></topCard>
      <parportion></parportion>
    </div>
    <div class="footer-box">
      <alarmList ref="ElalarmList"></alarmList>
    </div>
  </div>
</template>

<script lang="ts" setup>
import topCard from './overview/topCard.vue'
import alarmList from './overview/alarmList.vue'
import parportion from './overview/parportion.vue'
import { Close } from '@element-plus/icons-vue'
const emits = defineEmits(['closePopor'])
const close = () => {
  emits('closePopor', false)
}
</script>

<style lang="scss" scoped>
.tip-box {
  width: 446px;
  overflow: hidden;
  height: 100%;
  color: #fff;
  // background-color: rgba(0, 16, 50, 1);
  background-color: rgba(0, 16, 80, 0.6);
  // padding: 20px 17px;
  border: 1px solid rgba(0, 78, 188, 1);
  padding-top: 0;
  display: flex;
  flex-direction: column;
}

.close-box {
  display: flex;
  justify-content: space-between;
  background-image: url(@/assets/image/unitMap/title-bg.png);
  background-size: 100% 100%;
  height: 45px;
  align-items: center;
  margin-bottom: 18px;

  .title {
    font-size: 18px;
    padding-left: 40px;
  }
  .close-icon {
    padding-right: 20px;
  }
}
.footer-box {
  flex: 1;
  overflow: hidden;
}
</style>
