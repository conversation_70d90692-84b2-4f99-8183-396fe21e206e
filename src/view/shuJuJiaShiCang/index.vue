<template>
  <div class="shuJuJiaShiCang">
    <Gis />
    <Header :isShowTab="true" class="relative z-10" />
    <!-- 左侧 -->
    <div class="body-left-box z-10">
      <div class="left-line"></div>
      <div class="scroll-wrap pr-10px h-full overflow-y-auto overflow-x-hidden">
        <PopupModules.LeftPopup />
      </div>
    </div>
    <!-- 中间 -->
    <PopupModules.MiddlePopup class="middle-box z-5" />
    <!-- 右侧 -->
    <div class="body-right-box z-10">
      <div
        class="scroll-wrap pr-10px h-full overflow-y-auto overflow-x-hidden"
        style="direction: rtl"
        v-if="showRightScroll"
      >
        <div class="h-full" style="direction: ltr">
          <PopupModules.RightPopup />
        </div>
      </div>
      <div v-else>
        <PopupModules.RightPopup />
      </div>
      <div class="right-line"></div>
    </div>
    <footer />
  </div>
</template>
<script setup lang="ts">
import { computed, Component, onMounted, provide } from 'vue'
import Header from './components/header.vue'
import Gis from './components/cockpitGis/index.vue'
import * as safetyManageModules from './components/safetyManage'
import * as riskControlModules from './components/riskControl'
import * as safetyPostureModules from './components/safetyPosture'
import * as deviceRunModules from './components/deviceRun'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const showRightScroll = computed(() => {
  const list = ['1', '2', '3']
  return list.includes(route.query.tab as string)
})

const PopupModules = computed<{
  LeftPopup: Component
  RightPopup: Component
  MiddlePopup: Component
}>(() => {
  console.log('route.query.tab -----> 🚀', route.query.tab)
  switch (route.query.tab) {
    case '1':
      return safetyManageModules
    case '2':
      return riskControlModules
    case '3':
      return deviceRunModules
    case '4':
      return safetyPostureModules
    default:
      return safetyManageModules
  }
})

let host = window.location.host
let baseU = ''

// 打开一个新的窗口
function openNewWindow() {
  const baseU = location.origin + location.pathname + router.resolve('/subgis').href
  const target = '_blank' // 在新窗口中打开
  window.open(baseU, target)
}

onMounted(() => {
  // openNewWindow()
})
// 把这个方法传给子组件
provide('openNewWindow', openNewWindow)
</script>
<style scoped lang="scss">
.shuJuJiaShiCang {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #182338;
  overflow: hidden;

  .body-left-box {
    position: absolute;
    left: 0;
    top: 100.032px;
    width: 504px;
    height: calc(100% - 114.048px);
    display: flex;
    overflow-y: auto;
    &::-webkit-scrollbar {
      display: none;
    }
  }
  .middle-box {
    position: absolute;
    left: 50%;
    top: 100.032px;
    transform: translateX(-50%);
    width: calc(100% - 996.48px);
    // height: 100%;
  }
  .body-right-box {
    position: absolute;
    right: 0;
    top: 100.032px;
    width: 504px;
    height: calc(100% - 114.048px);
    display: flex;
    overflow-y: auto;
    // 隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }
  }
  .left-line,
  .right-line {
    width: 29.952px;
    background: url('../../assets/image/shujujiashicang/left-line.png') no-repeat;
    background-size: 100% 100%;
  }
  .right-line {
    background: url('../../assets/image/shujujiashicang/right-line.png') no-repeat;
    background-size: 100% 100%;
  }

  footer {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 14.016px;
    background: url('../../assets/image/shujujiashicang/bottom.png') no-repeat;
    background-size: 100% 100%;
  }
}
</style>
<style lang="scss">
.leftPopup > div:not(:first-child) {
  margin-top: 10px;
}
.rightPopup > div:not(:first-child) {
  margin-top: 10px;
}
.scroll-wrap::-webkit-scrollbar-thumb {
  background-color: transparent;
}
.scroll-wrap:hover::-webkit-scrollbar-thumb {
  background-color: rgba(19, 39, 81, 0.5);
}
</style>
