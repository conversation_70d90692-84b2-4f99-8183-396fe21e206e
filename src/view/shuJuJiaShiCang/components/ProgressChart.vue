<template>
  <div class="w-full h-full" :class="{ progress_red: bgimg, pgs: pgsStyle }" ref="progressChart"></div>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts'
import 'echarts-liquidfill' // 引入水球图的组件
import { defineComponent, markRaw, onMounted, onUnmounted, ref, watch } from 'vue'
import { isFontSizeRem } from '@/common/utils'
defineComponent({ name: 'ProgressChart' })

const props = defineProps({
  titleValueLeft: {
    type: String,
    default: '30%',
  },
  titleValueTop: {
    type: String,
    default: '35%',
  },
  titleTextLeft: {
    type: String,
    default: '55%',
  },
  titleTextTop: {
    type: String,
    default: '41%',
  },
  showText: {
    type: Boolean,
    default: true,
  },
  pgsStyle: {
    type: Boolean,
    default: false,
  },
  bgimg: {
    type: Boolean,
    default: true,
  },
  echartsData: {
    type: Number || String,
    default: '',
  },
  type: {
    type: String,
    default: 'red',
  },
  borderColor: {
    type: Array,
    default: () => ['rgba(47, 191, 97, 0.5)', 'rgba(47, 191, 97, 0.2)'],
  },
  color: {
    type: Array,
    default: () => {
      return ['rgba(255, 91, 92, 0.5)', 'rgba(255, 91, 92, 0.8)']
    },
  },
  titleText: {
    type: String,
    default: '%',
  },
})

const progressChart = ref()
let myChart = ref<any>(null)

function initEcharts(val: number) {
  myChart.value = markRaw(echarts.init(progressChart.value))
  const borderColor: any[] = props.borderColor || []
  const colors = props.color
  const titleText = props.titleText
  const data = [val / 100, val / 100]
  const option = {
    title: [
      {
        show: props.showText,
        text: val,
        textStyle: {
          fontSize: isFontSizeRem(16),
          fontFamily: 'D-DIN-PRO',
          color: '#ffffff',
        },
        left: props.titleValueLeft,
        top: props.titleValueTop,
      },
      {
        show: props.showText,
        // text: titleText,
        textStyle: {
          fontSize: isFontSizeRem(13),
          fontFamily: 'D-DIN-PRO',
          color: '#fff',
        },
        left: props.titleTextLeft,
        top: props.titleTextTop,
      },
    ],
    series: [
      {
        type: 'liquidFill',
        radius: '80%',
        center: ['50%', '50%'],
        data: data,
        color: borderColor,
        backgroundStyle: {
          color: 'black',
        },
        outline: {
          show: true,
          borderDistance: 0,
          itemStyle: {
            borderWidth: 1,
            borderColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0.05,
                color: borderColor[0],
              },
              {
                offset: 1,
                color: borderColor[1],
              },
            ]),
          },
        },
        label: {
          show: false,
          position: 'top',
          color: '#fff',
          baseline: 'bottom',
        },
      },
      {
        name: '外边框',
        type: 'pie',
        z: 5,
        radius: ['87%', '92%'],
        center: ['50%', '50%'],
        label: {
          show: false,
        },
        data: [
          {
            value: 70,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0.05,
                  color: borderColor[0],
                },
                {
                  offset: 1,
                  color: borderColor[1],
                },
              ]),
            },
          },
        ],
      },
    ],
  }
  myChart.value.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart.value) myChart.value.resize()
  })
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

onMounted(() => {
  initEcharts(props.echartsData)
})

onUnmounted(() => {
  destroyEcharts()
})

watch(
  () => props.echartsData,
  (val: any) => {
    initEcharts(props.echartsData)
  }
)
</script>

<style scoped>
.pgs {
  width: 100%;
  height: 100%;
}
.progress_red {
  background: url('@/assets/image/bigScreen/progress-red-bg.png') no-repeat center;
  background-size: contain;
}
</style>
