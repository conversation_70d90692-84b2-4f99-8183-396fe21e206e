<template>
  <div class="w-full h-full" ref="pieChart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import * as echarts from 'echarts'

defineComponent({ name: 'pieChart' })
const props = defineProps({
  legend: {
    type: Object,
    default: () => {
      return {
        left: 50,
        top: 0,
        width: 100,
        icon: 'square',
        itemGap: 15,
        itemWidth: 25,
        itemHeight: 14,
        // backgroundColor: 'rgba(24, 62, 105, 1)',
      }
    },
  },
  dataInfo: {
    type: Object,
    default: () => ({}),
  },
})
const pieChart = ref()
const myChart = ref<any>(null)

async function initEcharts() {
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(pieChart.value))
  const series: any = []
  const centerY = 50 // 原点y轴的单位距离
  const centerYOffset = 5 // 原点偏移
  const centerX = 100 / 3 // 原点x轴的单位距离
  const chartList = [{ value: props.dataInfo.complateRate }]
  chartList.forEach((item, index) => {
    const radius = 80
    const borderWidth = 12
    const titleSize = 10
    const valueSize = 20
    const ratio = (item.value / 100) * 360
    const center = ['50%', '50%']
    series.push(
      {
        // 内圆背景
        type: 'pie',
        radius: radius + '%',
        center,
        z: 1,
        itemStyle: {
          color: new echarts.graphic.RadialGradient(
            0,
            0,
            0,
            [
              {
                offset: 0,
                color: '#00273a',
              },
              {
                offset: 0.5,
                color: '#00273a',
              },
              {
                offset: 1,
                color: '#00273a',
              },
            ],
            false
          ),
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
        },
        label: {
          show: false,
        },
        tooltip: {
          show: false,
        },
        data: [100],
      },
      {
        //  内圆边框
        type: 'pie',
        radius: [radius + '%', radius - 1 + '%'],
        center,
        // clockWise: false,
        z: 2,
        itemStyle: {
          shadowBlur: 20,
          shadowColor: props.dataInfo.color || '#3e97e8',
          color: props.dataInfo.color || '#3e97e8',
        },
        label: {
          show: false,
        },
        data: [100],
      },
      {
        // 进度
        type: 'gauge',
        radius: radius + '%',
        startAngle: 90,
        endAngle: ~ratio + 91,
        center,
        z: 3,
        axisLine: {
          lineStyle: {
            width: borderWidth,
            color: [
              [
                1,
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#fff' },
                  { offset: 1, color: props.dataInfo.color || '#3e97e8' },
                ]),
              ],
            ],
          },
        },
        pointer: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        detail: {
          formatter: () => `${item.value}%\n本年培训计划\n完成率`,
          offsetCenter: [0, 0],
          fontSize: 12,
          fontWeight: 'bolder',
          color: '#fff',
          lineHeight: 15,
        },
        title: {
          offsetCenter: [0, '165%'],
          fontSize: titleSize,
          color: '#fff',
        },
        data: [item],
      },
      {
        // 内圆刻度
        type: 'gauge',
        startAngle: 90,
        endAngle: -270,
        radius: radius + '%',
        z: 4,
        center,
        axisLine: {
          show: false,
        },
        pointer: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          distance: -10,
          length: borderWidth,
          lineStyle: {
            color: 'rgba(0,35,52,1)',
            width: 2,
          },
        },
        axisLabel: {
          show: false,
        },
      },
      {
        // 外圆
        type: 'pie',
        z: 5,
        radius: [radius + 5 + '%', radius + 3 + '%'],
        center,
        // clockWise: false,
        itemStyle: {
          shadowBlur: 0,
          shadowColor: props.dataInfo.color || '#3a94e7',
          color: props.dataInfo.color || '#3a94e7',
        },
        label: {
          show: false,
        },
        data: [100],
      },
      {
        // 为了添加点击事件添加遮罩
        type: 'pie',
        z: 6,
        radius: [radius + 5 + '%', 0],
        center,
        // clockWise: false,
        itemStyle: {
          shadowBlur: 20,
          shadowColor: 'transparent',
          color: 'transparent',
        },
        label: {
          show: false,
        },
        data: [item],
      }
    )
  })

  const option = {
    animation: true,
    series: series,
  }
  myChart.value.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart.value) myChart.value.resize()
  })
}

onMounted(() => {
  // initEcharts()
})

watch(
  () => props.dataInfo.complateRate,
  () => {
    initEcharts()
  },
  {
    immediate: true,
    deep: true,
  }
)
</script>

<style scoped></style>
