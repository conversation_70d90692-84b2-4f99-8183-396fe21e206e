<template>
  <div class="leftPopup">
    <div class="part-one">
      <ChartTitle title="消防设施及监测" />
      <riskClass :dataList="dataList" />
    </div>
    <div class="part-two">
      <ChartTitle title="消防设备及维护" />
      <hiddenDanger :fireList="fireList" :hydrantList="hydrantList" :rates="rates" />
    </div>
    <div class="part-three">
      <ChartTitle title="特种设备及维护" />
      <siteManagement :deviceList="deviceList" :deviceRateList="deviceRateList" />
    </div>
  </div>
</template>
<script setup lang="ts">
import riskClass from './components/riskClass.vue'
import hiddenDanger from './components/hiddenDanger.vue'
import siteManagement from './components/siteManagement.vue'
import { onMounted, ref } from 'vue'

import $API from '~/common/api'
import { useUserInfo } from '~/store'
const userInfo = useUserInfo()

const rates = ref<{
  checkTimelinessRate: string
  timelyDetectionRate: string
}>({
  checkTimelinessRate: '0',
  timelyDetectionRate: '0',
})
const fireList = ref<{ name: string; value: number }[]>([
  { name: '干粉灭火器', value: 0 },
  { name: '推车式灭火器', value: 0 },
  { name: 'C02灭火器', value: 0 },
  { name: '水基灭火器', value: 0 },
])
const hydrantList = ref<{ name: string; value: number }[]>([
  { name: '消防栓', value: 0 },
  { name: '组合式消防箱', value: 0 },
])
const dataList = ref<{ name: string; value: number }[]>([
  { name: '电气火灾系统', value: 0 },
  { name: '自动喷水灭火系统', value: 0 },
  { name: '火灾自动报警系统', value: 0 },
  { name: '消防栓系统', value: 0 },
])
const deviceList = ref<{ name: string; value: number }[]>([
  // { name: '叉车', value: 34 },
])
const deviceRateList = ref<{ name: string; value: number; key: string }[]>([
  { name: '检查及时率', value: 0, key: 'checkTimelinessRate' },
  { name: '检测及时率', value: 0, key: 'timelyDetectionRate' },
  { name: '保养及时率', value: 0, key: 'timelyMaintenanceRate' },
])

// 消防设施及监测
const getFireFacilitiesMonitoring = async () => {
  const res: any = await $API.post({
    url: '/device/management/getFireFacilitiesMonitoring',
    params: {
      orgCode: userInfo.value.unitId,
      // isQueryVideo: '0',
      // isTolerant: '0',
      modelType: 'unit_base_url',
    },
  })
  if (res.code !== 'success') return
  let _data = res.data
  dataList.value = _data.map((i) => {
    return { name: i.label, value: i.deviceCount, ratio: i.deviceRatio + '%' }
  })
}

// 消防设备及维护
const getFireDeviceMaintain = async () => {
  //  type 1.灭火器 2.消防栓 3.特种设备
  const res: any = await $API.post({
    url: '/device/management/getFireDeviceMaintain',
    params: {
      orgCode: userInfo.value.unitId,
      modelType: 'unit_base_url',
    },
  })
  if (res.code !== 'success') return
  let _data = res.data
  // { name: '干粉灭火器', value: 34 },

  fireList.value = _data?.extinguisherDevices?.map((i) => {
    return { name: i.label, value: i.deviceCount, ratio: i.deviceRatio + '%' }
  })
  hydrantList.value = _data?.hydrantDevices?.map((i) => {
    return { name: i.label, value: i.deviceCount, ratio: i.deviceRatio + '%' }
  })
  rates.value.checkTimelinessRate = _data.checkTimelinessRate
  rates.value.timelyDetectionRate = _data.timelyDetectionRate

  // dataList.value = _data.map((i) => {
  //   return { name: i.label, value: i.deviceCount, ratio: i.deviceRatio + '%' }
  // })
}

// 特种设备及维护
const getSpecialDeviceMaintain = async () => {
  //  type 1.灭火器 2.消防栓 3.特种设备
  const res: any = await $API.post({
    url: '/device/management/getSpecialDeviceMaintain',
    params: {
      orgCode: userInfo.value.unitId,
      modelType: 'unit_base_url',
    },
  })
  if (res.code !== 'success') return
  let _data = res.data
  // { name: '干粉灭火器', value: 34 },

  deviceList.value =
    _data?.specialDevices?.map((i) => {
      return { name: i.label, value: i.deviceCount, ratio: i.deviceRatio + '%' }
    }) || []
  // { name: '检查及时率', value: 99,key:'checkTimelinessRate' },
  deviceRateList.value.forEach((i) => {
    i.value = _data[i.key] || 0
  })
}

const init = () => {
  getFireFacilitiesMonitoring()
  getFireDeviceMaintain()
  getSpecialDeviceMaintain()
}
//

onMounted(() => {
  init()
})
</script>
