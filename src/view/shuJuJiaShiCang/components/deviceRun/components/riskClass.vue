<template>
  <div class="riskClass">
    <Pie :optionData="dataList" />
  </div>
</template>

<script setup lang="ts">
import Pie from './Pie-check.vue'
const props = defineProps<{
  dataList: { name: string; value: number }[]
}>()
</script>

<style lang="scss" scoped>
.riskClass {
  height: 200px;
  padding: 10px;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
}
</style>
