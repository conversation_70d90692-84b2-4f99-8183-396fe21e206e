<template>
  <div class="rules-unite">
    <videoPie :optionData="dataList" />
    <div class="deivce-img">
      <div class="cricel"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import videoPie from './videoPie.vue'
import { ref } from 'vue'
const props = defineProps<{
  dataList: { name: string; value: number }[]
}>()
defineOptions({ name: 'regulationsRules' })
</script>

<style lang="scss" scoped>
.rules-unite {
  position: relative;
  width: 100%;
  height: 200px;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  .deivce-img {
    position: absolute;
    width: 86.976px;
    height: 86.976px;
    top: 50%;
    left: 74px;
    transform: translateY(-50%);
    background-color: #0d1b3d;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    border: #a1bae3 1.92px solid;
    box-shadow: inset 0 0 13px 4px #a1bae3;
    .cricel {
      width: 65.472px;
      height: 65.472px;
      background-color: #fff;
      background: url('@/assets/image/shujujiashicang/deviceRun/d-file.png') no-repeat center/cover;
      background-size: 100% 100%;
    }
  }
}
</style>
