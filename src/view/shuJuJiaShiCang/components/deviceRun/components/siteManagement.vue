<template>
  <div class="special-equipment">
    <div class="special-top h-1/2">
      <Pie :optionData="deviceList" v-if="deviceList.length > 0" />
      <div v-else class="w-full h-full flex justify-center items-center">
        <img class="w-170px h-140px" src="@/assets/image/no-data.png" alt="" />
      </div>
    </div>
    <div class="special-bottom h-1/2">
      <div v-for="(item, index) in deviceRateList" :key="item.name" :class="'bg-' + index" class="special-bottom-item">
        <div class="rate">{{ item.value }}%</div>
        <div class="title">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Pie from './Pie-special.vue'
const props = defineProps<{
  deviceList: { name: string; value: number }[]
  deviceRateList: { name: string; value: number }[]
}>()
</script>

<style lang="scss" scoped>
.special-equipment {
  height: 380px;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  .special-bottom {
    display: flex;
    justify-content: space-around;
    .special-bottom-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 129.984px;
      height: 180.096px;
      background: url(@/assets/image/shujujiashicang/deviceRun/d-r-01.png) no-repeat center/cover;
      .title {
        font-weight: 500;
        font-size: 14.016px;
        color: #ffffff;
      }
      .rate {
        font-weight: 500;
        font-size: 29.952px;
        color: #ffffff;
      }
    }
    .bg-1 {
      background: url(@/assets/image/shujujiashicang/deviceRun/d-r-02.png) no-repeat center/cover;
    }
    .bg-2 {
      background: url(@/assets/image/shujujiashicang/deviceRun/d-r-03.png) no-repeat center/cover;
    }
  }
}
</style>
