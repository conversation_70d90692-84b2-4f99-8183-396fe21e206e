<template>
  <div class="line-part w-full h-full" ref="pieChart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
import { isFontSizeRem } from '~/common/utils'
const props = defineProps<{
  optionData: { name: string; value: number }[]
}>()
let total = ref(2360)
watch(
  () => props.optionData,
  () => {
    total.value = 0
    props.optionData.forEach((item: any) => {
      total.value += item.value
    })
    initEcharts()
  }
)
defineComponent({ name: 'pieChart' })

const pieChart = ref()
let myChart: any

const handelData = () => {
  const colorArray = [
    ['#23CAFF', '#061E38'],
    ['#438CFF', '#08234C'],
    ['#FFB800', '#2A2F3C'],
    ['#00ECB3', '#152A34'],
    ['#B2C3C5', '#1B2E36'],
  ]

  props.optionData.forEach((item: any, index: number) => {
    item.itemStyle = {
      // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //   { offset: 0, color: colorArray[index][0] },
      //   { offset: 1, color: colorArray[index][1] },
      // ]),
      color: colorArray[index % 5][0],
    }
  })

  return props.optionData
}

function initEcharts() {
  if (myChart) myChart.dispose()

  // 标记一个对象，使其永远不会再成为响应式对象
  myChart = echarts.init(pieChart.value)

  // 传入数据生成 option
  const option = {
    title: {
      text: `{a|${total.value}}\n{b|设备总数}`,
      // subtext: '设备总数',
      left: '12%',
      top: 'center',
      textStyle: {
        color: '#fff',
        fontSize: isFontSizeRem(15),
        rich: {
          a: {
            fontSize: isFontSizeRem(24),
            padding: [3, 0],
            color: '#D3E5FF',
            align: 'center',
            fontWeight: 'bold',
          },
          b: {
            fontSize: isFontSizeRem(14),
            color: '#D3E5FF',
            align: 'center',
          },
        },
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: `{b} {c} {d}%`,
      confine: true,
    },
    grid: {
      containLabel: true,
    },
    legend: {
      type: 'scroll',
      // icon: 'rect',
      // icon: 'path://M0,0 L100,0 L70,80 L-30,80 Z', // 自定义形状
      // icon: 'path://M0,50 L50,0 L100,50 L50,100 Z', // 菱形
      // icon: 'path://M50,15 L61,35 L85,35 L66,50 L72,75 L50,60 L28,75 L34,50 L15,35 L39,35 Z', // 五角星
      // icon: 'path://M50,10 A40,20 0 1,1 50,90 A40,20 0 1,1 50,10', // 椭圆
      icon: 'path://M50,10 L90,90 L10,90 Z', // 三角形
      top: 'center',
      left: '45%',
      // 竖向排列
      orient: 'vertical',
      // borderColor: '#fff',
      // borderWidth: 2,
      textStyle: {
        color: '#fff',
        fontSize: isFontSizeRem(12),
      },
      // itemGap: 5,
      // itemHeight: 10,
      itemWidth: 14, // 设置图标高度为20px
      pageIconColor: '#ff781f', // 设置翻页箭头颜色
      pageTextStyle: {
        color: '#999', // 设置翻页数字颜色
        fontSize: isFontSizeRem(12),
      },
      pageIconSize: isFontSizeRem(12), // 设置翻页箭头大小
      formatter: (name: string) => {
        const currentNum = props.optionData.find((item: any) => item.name === name)?.value
        if (!currentNum) return name
        return `${name.length > 10 ? name.slice(0, 9) + '...' : name} ${'  ' + currentNum + '  '} ${((+(currentNum / total.value) * 10000) / 100).toFixed(2)}%`
      },
      tooltip: {
        show: true,
      },
    },
    series: [
      {
        type: 'pie',
        // padAngle: 3,
        radius: ['50%', '70%'],
        center: ['20%', '50%'],
        avoidLabelOverlap: false,
        minShowLabelPercent: 0.1,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: false,
            fontSize: isFontSizeRem(11),
            color: '#fff',
            formatter: `{c}\n\n{b}`,
          },
        },
        labelLine: {
          show: false,
        },
        data: handelData(),
      },
    ],
  }

  myChart.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart) myChart.resize()
  })
}

onMounted(() => {
  initEcharts()
})

// 把initEcharts暴露出去
defineExpose({
  initEcharts,
})
</script>

<style scoped>
.title-num {
  position: absolute;
  bottom: 10%;
  left: 18%;
  z-index: 500;
  background: linear-gradient(to right, #061d31, #227eb6 60%, #227eb6 60%, #051525);

  .a {
    color: #fff;
    font-size: 14px;
    font-weight: 700;
    margin-right: 10px;
  }
  .b {
    color: #fff;
    font-size: 18px;
    font-weight: 400;
  }
}
</style>
