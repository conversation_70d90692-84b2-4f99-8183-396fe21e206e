<template>
  <div v-if="props.optionData.length > 0" class="line-part w-full h-full" ref="pieChart"></div>
  <div v-else class="w-full h-full p-80px -pl-20px flex justify-center items-center">
    <img src="@/assets/image/no-data.png" alt="" />
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
import { isFontSizeRem } from '@/common/utils'
const props = defineProps<{
  optionData: { name: string; value: number }[]
}>()
let total = ref(300)
watch(
  () => props.optionData,
  () => {
    total.value = 0
    props.optionData.forEach((item: any) => {
      total.value += item.value
    })
    initEcharts()
  }
)
defineComponent({ name: 'pieChart' })

const pieChart = ref()
let myChart: any

const handelData = () => {
  const colorArray = [
    ['#34DAB5', '#061E38'],
    ['#438CFF', '#08234C'],
    ['#FE5A4C', '#2A2F3C'],
    ['#F0B38B', '#152A34'],
    ['#B2C3C5', '#1B2E36'],
  ]

  props.optionData.forEach((item: any, index: number) => {
    item.itemStyle = {
      color: colorArray[index][0],
    }
  })

  return props.optionData
}

function initEcharts() {
  if (myChart) myChart.dispose()

  // 标记一个对象，使其永远不会再成为响应式对象
  myChart = echarts.init(pieChart.value)

  // 传入数据生成 option
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: `{b} {c} {d}%`,
      appendTo: document.querySelector('body'),
    },
    legend: {
      type: 'scroll',
      // icon: 'path://M0,0 L100,0 L70,80 L-30,80 Z',
      icon: 'path://M50,15 L61,35 L85,35 L66,50 L72,75 L50,60 L28,75 L34,50 L15,35 L39,35 Z', // 五角星
      top: 'center',
      right: '0',
      // 竖向排列
      orient: 'vertical',
      textStyle: {
        color: '#fff',
        fontSize: isFontSizeRem(9),
      },
      itemGap: isFontSizeRem(5),
      itemHeight: isFontSizeRem(10),
      itemWidth: isFontSizeRem(10), // 设置图标高度为20px
      pageIconColor: '#ff781f', // 设置翻页箭头颜色
      pageTextStyle: {
        color: '#999', // 设置翻页数字颜色
      },
      pageIconSize: isFontSizeRem(12), // 设置翻页箭头大小
      formatter: (name: string) => {
        const currentNum = props.optionData.find((item: any) => item.name === name)?.value
        console.log('🚀 ~ initEcharts ~ props.optionData:', props.optionData)
        if (!currentNum) return name
        return `${name.length > 6 ? name.slice(0, 6) + '...' : name} ${((+(currentNum / total.value) * 10000) / 100).toFixed(2)}%`
      },
      tooltip: {
        show: true,
      },
    },
    series: [
      {
        type: 'pie',
        // padAngle: 3,
        radius: ['40%', '60%'],
        center: ['25%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: false,
            fontSize: isFontSizeRem(11),
            color: '#fff',
            formatter: `{c}\n\n{b}`,
          },
        },
        labelLine: {
          show: false,
        },
        data: handelData(),
      },
    ],
  }

  myChart.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart) myChart.resize()
  })
}

onMounted(() => {
  if (props.optionData.length > 0) {
    initEcharts()
  }
})

// 把initEcharts暴露出去
defineExpose({
  initEcharts,
})
</script>

<style scoped>
.title-num {
  position: absolute;
  bottom: 10%;
  left: 18%;
  z-index: 500;
  background: linear-gradient(to right, #061d31, #227eb6 60%, #227eb6 60%, #051525);

  .a {
    color: #fff;
    font-size: 14px;
    font-weight: 700;
    margin-right: 10px;
  }
  .b {
    color: #fff;
    font-size: 18px;
    font-weight: 400;
  }
}
</style>
