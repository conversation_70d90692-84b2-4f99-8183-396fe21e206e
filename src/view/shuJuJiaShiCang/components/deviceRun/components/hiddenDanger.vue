<template>
  <div class="riskClass">
    <div class="flex h-150px">
      <Pie :optionData="fireList" />
      <Pie :optionData="hydrantList" />
    </div>
    <div class="flex justify-around h-[calc(100%-150px)]">
      <div class="inspection">
        <div class="inspection-item">
          <div class="rate">{{ rates.checkTimelinessRate || 0 }}%</div>
          <div class="title">检查及时率</div>
        </div>
      </div>
      <div class="detection">
        <div class="detection-item">
          <div class="rate">{{ rates.timelyDetectionRate || 0 }}%</div>
          <div class="title">检测及时率</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Pie from './Pie-fire.vue'
const props = defineProps<{
  fireList: { name: string; value: number }[]
  hydrantList: { name: string; value: number }[]
  rates: { checkTimelinessRate: string; timelyDetectionRate: string }
}>()
//
</script>

<style lang="scss" scoped>
.riskClass {
  height: 260px;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  .inspection {
    display: flex;
    align-items: center;
    justify-content: end;
    .inspection-item {
      width: 210.048px;
      height: 87.936px;
      background: url(@/assets/image/shujujiashicang/deviceRun/d-big-check.png) no-repeat center/cover;
      // background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .title {
        font-weight: 500;
        font-size: 14.016px;
        color: #ffffff;
      }
      .rate {
        font-weight: 500;
        font-size: 29.952px;
        color: #ffffff;
      }
    }
  }
  .detection {
    display: flex;
    align-items: center;
    justify-content: start;
    .detection-item {
      width: 210.048px;
      height: 87.936px;
      background: url(@/assets/image/shujujiashicang/deviceRun/d-big-check1.png) no-repeat center/cover;
      // background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .title {
        font-weight: 500;
        font-size: 14.016px;
        color: #ffffff;
      }
      .rate {
        font-weight: 500;
        font-size: 29.952px;
        color: #ffffff;
      }
    }
  }
}
</style>
