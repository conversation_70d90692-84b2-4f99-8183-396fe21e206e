<template>
  <div class="riskClass">
    <div class="device-total h-200px p-10px">
      <Pie :optionData="optionData" />
    </div>
    <div class="online-total">
      <div class="online">
        <div class="online-top">
          <div class="icon"></div>
          <div class="text">在线数/总数</div>
        </div>
        <div class="online-bottom">
          <div class="num">{{ info.onlineTotal }}/</div>
          <span class="num-end">{{ info.deviceTotal }}</span>
        </div>
      </div>
      <div class="online-rate">
        <div class="rate-left">
          <div class="online-top">
            <div class="icon"></div>
            <div class="text">在线率</div>
          </div>
          <div class="online-bottom">{{ info.onlinePercentage }}%</div>
        </div>
        <div class="rate-right">
          <PieMini :optionData="optionData2" />
        </div>
      </div>
    </div>
    <div class="device-total h-200px px-10px overflow-auto">
      <div v-for="(item, index) in dataList" :key="item.typeName" class="progress mb-5px pl-5px">
        <div class="progress-name">
          <div class="name">{{ item.typeName }}</div>
          <div class="num" :style="{ color: item.color }">{{ item.num }}/{{ item.total }}</div>
        </div>
        <div class="progress-centent">
          <div class="progress-bar">
            <div class="progress-main">
              <div
                class="progress-main-bar"
                :class="'color' + index"
                :style="{ width: getRate(item.num, item.total) + '%' }"
              ></div>
            </div>
          </div>
          <div class="progress-rate">
            <span>{{ getRate(item.num, item.total) }}</span>
            <span class="symbol">%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Pie from './Pie-total.vue'
import PieMini from './Pie-mini.vue'
import { ref, onMounted, computed } from 'vue'

type DeviceInfo = {
  typeName: string
  num: number
  total: number
  color: string
}

const props = defineProps<{
  dataList: DeviceInfo[]
}>()

const info = computed(() => {
  let _info = {
    onlineTotal: 0,
    deviceTotal: 0,
    onlinePercentage: '',
  }
  props.dataList.map((i) => {
    // { typeName: i.deviceTypeName, num: i.offlineNum, total: i.typeTotal, ratio: i.typePercentage, ...i }
    _info.onlineTotal += i.num
    _info.deviceTotal += i.total
  })
  _info.onlinePercentage = _info.deviceTotal ? ((_info.onlineTotal / _info.deviceTotal) * 100).toFixed(2) : '0.00'
  return _info
})

//   ref<{
//   onlineTotal: ''
//   deviceTotal: ''
//   onlinePercentage: ''
// }>({})

const optionData2 = computed(() => {
  return [{ name: '水位传感器', value: info.value.onlinePercentage }]
})

// const optionData = ref<{ name: string; value: number }[]>([
//   { name: '水位传感器', value: 18 },
//   { name: '独立式感烟探测器', value: 2175 },
//   { name: '天泽防灾智盒', value: 204 },
//   { name: '物联网用户信息传输', value: 29 },
// ])
const optionData = computed(() => {
  return props.dataList.map((i) => {
    // { typeName: i.deviceTypeName, num: i.offlineNum, total: i.typeTotal, ratio: i.typePercentage, ...i }
    return { name: i.typeName, value: i.total || 0 }
  })
})

const getRate = (num: number, total: number) => {
  return ((+(num / total) * 10000) / 100).toFixed(2)
}
onMounted(() => {})
</script>

<style lang="scss" scoped>
.riskClass {
  padding: 0 12.096px 12.096px;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  .device-total {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;
    padding-right: 6px;
    &:hover {
      overflow: auto;
      padding-right: 0px;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar:horizontal {
      height: 6px;
    }
    &::-webkit-scrollbar-track {
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--el-border-color);
      border-radius: 10px;
      transition: all 0.2s ease-in-out;

      &:hover {
        cursor: pointer;
        background-color: var(--el-border-color);
      }
    }

    .progress {
      height: 37.056px;
      .progress-name {
        display: flex;
        font-size: 12.096px;
        color: #cfe0e6;
        .num {
          margin-left: 9.984px;
          color: #46f9ff;
        }
      }
      .progress-centent {
        display: flex;
        align-items: center;
        .progress-rate {
          width: 80px;
          font-size: 14px;
          color: #ffffff;
          text-align: right;
          .symbol {
            margin-left: 2px;
            font-size: 14px;
            // color: #8a9399;
            color: #ebeef5;
          }
        }
        .progress-bar {
          flex: 1;
          .progress-main {
            width: 100%;
            height: 5.76px;
            background: #141e3c;

            .progress-main-bar {
              position: relative;
              height: 100%;
              background-image: linear-gradient(135deg, rgba(255, 255, 255, 0), rgba(0, 230.00000149011612, 255, 1));
              transition: width 1s;
              &::after {
                content: '';
                width: 21.12px;
                height: 21.12px;
                border-radius: 50%;
                border: #46f9ff solid 1px;
                position: absolute;
                right: 0;
                top: 50%;
                transform: translate(50%, -50%);
                box-shadow: inset 0px 0px 3px 1px #72e2e6;
                // 反复的动画
                animation: change 0.7s infinite ease-out;
              }
              &::before {
                content: '';
                width: 9.6px;
                height: 9.6px;
                border-radius: 50%;
                background: #fff;
                position: absolute;
                right: 0;
                top: 50%;
                transform: translate(50%, -50%);
                box-shadow: 0px 0px 3px 1px #72e2e6;
              }
            }
            .color1 {
              background-image: linear-gradient(135deg, rgba(255, 255, 255, 0), #6cd400);
              &::after {
                content: '';
                border: #6cd400 solid 1px;
                box-shadow: inset 0px 0px 3px 1px #6cd400;
              }
              &::before {
                content: '';
                box-shadow: 0px 0px 3px 1px #6cd400;
              }
            }
            .color2 {
              background-image: linear-gradient(135deg, rgba(255, 255, 255, 0), #f7b500);
              &::after {
                content: '';
                border: #f7b500 solid 1px;
                box-shadow: inset 0px 0px 3px 1px #f7b500;
              }
              &::before {
                content: '';
                box-shadow: 0px 0px 3px 1px #f7b500;
              }
            }
          }
        }
      }
    }
  }
  .online-total {
    height: 100%;
    display: flex;
    justify-content: space-around;
    .online {
      color: #b4c0cc;
      font-size: 15.936px;
      .online-top {
        display: flex;
        .icon {
          width: 24.96px;
          height: 24.96px;
          background: url('../../../../../assets/image/shujujiashicang/deviceRun/d-jing.png') no-repeat center/cover;
        }
      }
      .online-bottom {
        font-size: 38.016px;
        color: #fff;
        display: flex;
        align-items: center;

        font-family: 'DS-Digital-jia';
        .num-end {
          height: 26.88px;
          font-size: 24px;
          justify-self: end;
        }
      }
    }
    .online-rate {
      display: flex;
      .rate-left {
        .online-top {
          display: flex;
          align-items: center;
          font-size: 15.936px;
          color: #b4c0cc;
          .icon {
            margin-right: 5.76px;
            width: 15.36px;
            height: 15.36px;
            background: url('../../../../../assets/image/shujujiashicang/deviceRun/d-rate.png') no-repeat center/cover;
          }
        }
        .online-bottom {
          font-size: 38.016px;
          color: #fff;
          display: flex;
          align-items: center;

          font-family: 'DS-Digital-jia';
        }
      }
      .rate-right {
        width: 70.08px;
        height: 70.08px;
        // background-color: #123a61;
        border-radius: 50%;
      }
    }
  }
}
:deep(.el-progress-bar__outer) {
  background-color: #263b63;
}
@keyframes change {
  0% {
    width: 21.12px;
    height: 21.12px;
  }
  50% {
    width: 24.96px;
    height: 24.96px;
  }
  100% {
    width: 21.12px;
    height: 21.12px;
  }
}
</style>
