<template>
  <div class="line-part w-full h-full" ref="pieChart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
import { isFontSizeRem } from '~/common/utils'
const props = defineProps<{
  optionData: { name: string; value: number }[]
}>()
let total = ref(300)
watch(
  () => props.optionData,
  () => {
    console.log(props.optionData, 2122)
    total.value = 0
    props.optionData.forEach((item: any) => {
      total.value += item.value
    })
    initEcharts()
  }
)
defineComponent({ name: 'pieChart' })

const pieChart = ref()
let myChart: any

const handelData = () => {
  // const colorArray = [
  //   ['#04C1E9', '#061E38'],
  //   ['#1778E8', '#08234C'],
  //   ['#D8AB61', '#2A2F3C'],
  //   ['#66C667', '#152A34'],
  //   ['#B2C3C5', '#1B2E36'],
  // ]

  // props.optionData.forEach((item: any, index: number) => {
  //   item.itemStyle = {
  //     color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
  //       { offset: 0, color: colorArray[index][0] },
  //       { offset: 1, color: colorArray[index][1] },
  //     ]),
  //   }
  // })

  return props.optionData
}

function initEcharts() {
  if (myChart) myChart.dispose()

  // 标记一个对象，使其永远不会再成为响应式对象
  myChart = echarts.init(pieChart.value)

  // 传入数据生成 option
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: `{b} {d}%`,
    },
    legend: {
      type: 'scroll',
      icon: 'path://M0,0 L60,0 L40,80 L-20,80 Z',
      top: 'center',
      right: '10%',
      // 竖向排列
      orient: 'vertical',
      textStyle: {
        color: '#fff',
        fontSize: isFontSizeRem(12),
      },
      // itemGap: 5,
      // itemHeight: 11,
      pageIconColor: '#ff781f', // 设置翻页箭头颜色
      pageTextStyle: {
        color: '#999', // 设置翻页数字颜色
        fontSize: isFontSizeRem(12),
      },
      pageIconSize: isFontSizeRem(12), // 设置翻页箭头大小
      formatter: (name: string) => {
        const currentNum = props.optionData.find((item: any) => item.name === name)?.value
        if (!currentNum) return `${name} 0`
        return `${name} ${currentNum}`
      },
      tooltip: {
        show: true,
      },
    },
    series: [
      {
        type: 'pie',
        // padAngle: 1,
        radius: ['50%', '70%'],
        center: ['25%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 11,
            color: '#fff',
            formatter: `{c}\n\n{b}`,
          },
        },
        labelLine: {
          show: false,
        },
        data: handelData(),
      },
    ],
  }

  myChart.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart) myChart.resize()
  })
}

onMounted(() => {
  initEcharts()
})

// 把initEcharts暴露出去
defineExpose({
  initEcharts,
})
</script>

<style scoped>
.title-num {
  position: absolute;
  bottom: 10%;
  left: 18%;
  z-index: 500;
  background: linear-gradient(to right, #061d31, #227eb6 60%, #227eb6 60%, #051525);

  .a {
    color: #fff;
    font-size: 14px;
    font-weight: 700;
    margin-right: 10px;
  }
  .b {
    color: #fff;
    font-size: 18px;
    font-weight: 400;
  }
}
</style>
