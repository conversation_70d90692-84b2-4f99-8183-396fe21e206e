<template>
  <div class="line-part w-full h-full" ref="pieChart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
import { isFontSizeRem } from '@/common/utils'
const props = defineProps<{
  optionData: { name: string; value: number; ratio?: string }[]
}>()
let total = ref(0)
watch(
  () => props.optionData,
  () => {
    total.value = 0
    props.optionData.forEach((item: any) => {
      total.value += item.value
    })
    initEcharts()
  }
)
defineComponent({ name: 'pieChart' })

const pieChart = ref()
let myChart: any

const handelData = () => {
  const colorArray = [
    ['#19B994', '#061E38'],
    ['#CEBC63', '#061E38'],
    ['#4F7CC6', '#061E38'],
    ['#994F5E', '#061E38'],
    ['#04C1E9', '#061E38'],
    ['#1778E8', '#08234C'],
    ['#D8AB61', '#2A2F3C'],
    ['#66C667', '#152A34'],
    ['#B2C3C5', '#1B2E36'],
  ]

  props.optionData.forEach((item: any, index: number) => {
    item.itemStyle = {
      // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //   { offset: 0, color: colorArray[index][0] },
      //   { offset: 1, color: colorArray[index][1] },
      // ]),
      color: colorArray[index % 9][0],
    }
  })

  return props.optionData
}

function initEcharts() {
  if (myChart) myChart.dispose()

  // 标记一个对象，使其永远不会再成为响应式对象
  myChart = echarts.init(pieChart.value)

  // 传入数据生成 option
  const option = {
    title: {
      text: `{a|${total.value}}\n{b|系统总数（套）}`,
      // subtext: '设备总数',
      left: '15%',
      top: 'center',
      textStyle: {
        color: '#fff',
        fontSize: isFontSizeRem(15),
        rich: {
          a: {
            fontSize: isFontSizeRem(24),
            padding: [3, 0],
            color: '#D3E5FF',
            align: 'center',
            fontWeight: 'bold',
          },
          b: {
            fontSize: isFontSizeRem(12),
            color: '#D3E5FF',
            align: 'center',
          },
        },
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: `{b} {d}%`,
    },
    legend: {
      type: 'scroll',
      icon: 'path://M0,0 L60,0 L40,80 L-20,80 Z',
      top: 'center',
      right: '10',
      // 竖向排列
      orient: 'vertical',
      textStyle: {
        color: '#fff',
        fontSize: isFontSizeRem(11),
      },
      // itemGap: 5,
      // itemHeight: 11,
      pageIconColor: '#ff781f', // 设置翻页箭头颜色
      pageTextStyle: {
        color: '#999', // 设置翻页数字颜色
      },
      pageIconSize: isFontSizeRem(12), // 设置翻页箭头大小
      formatter: (name: string) => {
        const currentItem = props.optionData.find((item: any) => item.name === name)
        return `${name.length > 7 ? name.slice(0, 7) + '...' : name}  ${currentItem?.value || 0}  ${currentItem?.ratio || ''}`
      },
      tooltip: {
        show: true,
      },
    },
    series: [
      {
        type: 'pie',
        // padAngle: 3,
        radius: ['50%', '75%'],
        center: ['25%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: false,
            fontSize: isFontSizeRem(11),
            color: '#fff',
            formatter: `{c}\n\n{b}`,
          },
        },
        labelLine: {
          show: false,
        },
        data: handelData(),
      },
    ],
  }

  myChart.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart) myChart.resize()
  })
}

onMounted(() => {
  initEcharts()
})

// 把initEcharts暴露出去
defineExpose({
  initEcharts,
})
</script>

<style scoped>
.title-num {
  position: absolute;
  bottom: 10%;
  left: 18%;
  z-index: 500;
  background: linear-gradient(to right, #061d31, #227eb6 60%, #227eb6 60%, #051525);

  .a {
    color: #fff;
    font-size: 14px;
    font-weight: 700;
    margin-right: 10px;
  }
  .b {
    color: #fff;
    font-size: 18px;
    font-weight: 400;
  }
}
</style>
