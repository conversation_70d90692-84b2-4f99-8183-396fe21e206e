<template>
  <div class="line-part w-full h-full" ref="pieChart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
const props = defineProps<{
  optionData: { name: string; value: number }[]
}>()
let total = ref(0)
watch(
  () => props.optionData,
  () => {
    total.value = 0
    props.optionData.forEach((item: any) => {
      total.value += item.value
    })
    initEcharts()
  }
)
defineComponent({ name: 'pieChart' })

const pieChart = ref()
let myChart: any

const handelData = () => {
  const colorArray = [
    ['#23CAFF', '#061E38'],
    ['transparent', '#08234C'],
    ['#FFB800', '#2A2F3C'],
    ['#00ECB3', '#152A34'],
    ['#B2C3C5', '#1B2E36'],
  ]

  props.optionData.forEach((item: any, index: number) => {
    item.itemStyle = {
      // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //   { offset: 0, color: colorArray[index][0] },
      //   { offset: 1, color: colorArray[index][1] },
      // ]),
      color: colorArray[index][0],
    }
  })

  return props.optionData
}

function initEcharts() {
  if (myChart) myChart.dispose()

  // 标记一个对象，使其永远不会再成为响应式对象
  myChart = echarts.init(pieChart.value)

  // 传入数据生成 option
  const option = {
    backgroundColor: 'transparent',
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['60%', '80%'],
        itemStyle: {
          color: '#17305F',
        },
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 40,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [{ value: 1, name: '' }],
      },
      {
        type: 'pie',
        // padAngle: 3,
        radius: ['0%', '80%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 11,
            color: '#fff',
            formatter: `{c}\n\n{b}`,
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: props.optionData[0].value, name: 'Search Engine', itemStyle: { color: '#23CAFF' } },
          { value: 100 - props.optionData[0].value, name: 'Direct', itemStyle: { color: 'transparent' } },
        ],
      },
    ],
  }

  myChart.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart) myChart.resize()
  })
}

onMounted(() => {
  initEcharts()
})

// 把initEcharts暴露出去
defineExpose({
  initEcharts,
})
</script>

<style scoped>
.title-num {
  position: absolute;
  bottom: 10%;
  left: 18%;
  z-index: 500;
  background: linear-gradient(to right, #061d31, #227eb6 60%, #227eb6 60%, #051525);

  .a {
    color: #fff;
    font-size: 14px;
    font-weight: 700;
    margin-right: 10px;
  }
  .b {
    color: #fff;
    font-size: 18px;
    font-weight: 400;
  }
}
</style>
