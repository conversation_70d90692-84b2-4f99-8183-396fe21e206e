<template>
  <div class="access-unite">
    <div
      v-for="(item, index) in dataList as any"
      :key="item.typeName"
      class="access-item icon"
      :class="'icon-bg' + index"
    >
      <div class="item-title">{{ item.typeName }}</div>
      <div class="item-num">{{ item.num }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const props = defineProps({
  dataList: {
    type: Array,
    default: () => [],
  },
})
</script>

<style lang="scss" scoped>
.access-unite {
  padding: 14.208px;
  height: calc(100% - 46.08px);
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 11.136px;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  .access-item {
    padding: 7.68px 12.288px;
    width: 139.968px;
    height: 66.048px;
    .item-title {
      font-size: 14.016px;
    }
    .item-num {
      font-weight: bold;
      font-size: 24px;
    }
  }
  .icon {
    background: url(@/assets/image/shujujiashicang/deviceRun/d-tools.png) no-repeat center/cover;
  }
  .icon-bg1 {
    background: url(@/assets/image/shujujiashicang/deviceRun/d-fire.png) no-repeat center/cover;
  }
  .icon-bg2 {
    background: url(@/assets/image/shujujiashicang/deviceRun/d-book.png) no-repeat center/cover;
  }
  .icon-bg3 {
    background: url(@/assets/image/shujujiashicang/deviceRun/d-dan.png) no-repeat center/cover;
  }
  .icon-bg4 {
    background: url(@/assets/image/shujujiashicang/deviceRun/d-video.png) no-repeat center/cover;
  }
  .icon-bg5 {
    background: url(@/assets/image/shujujiashicang/deviceRun/d-room.png) no-repeat center/cover;
  }
}
</style>
