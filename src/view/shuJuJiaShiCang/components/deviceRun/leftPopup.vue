<template>
  <div class="leftPopup">
    <div class="part-one">
      <ChartTitle title="设施设备总览" />
      <accessUnit :dataList="accessType" />
    </div>
    <div class="part-two">
      <ChartTitle title="物联网设备及监测" />
      <organizingPersonnel :dataList="deviceTypeList" />
    </div>
    <div class="part-three">
      <ChartTitle title="视频设备及监测" />
      <regulationsRules :dataList="optionData" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import accessUnit from './components/accessUnit.vue'
import regulationsRules from './components/regulationsRules.vue'
import organizingPersonnel from './components/organizingPersonnel.vue'

import $API from '~/common/api'
import { useUserInfo } from '~/store'
const userInfo = useUserInfo()

const accessType = ref([
  { typeName: '接入设备总数', num: 0, key: 'total' },
  { typeName: '火警报警设备', num: 0, key: 'alarmNum' },
  { typeName: '电气火灾设备', num: 0, key: 'electricalNum' },
  { typeName: '消防水系统设备', num: 0, key: 'fireControlNum' },
  { typeName: '视频设备', num: 0, key: 'videoNum' },
  { typeName: '其他设备', num: 0, key: 'otherNum' },
])

const deviceTypeList = ref([{ typeName: '独立式烟感', num: 0, total: 0, color: '#46F9FF' }])
// 规程制度
const rules = ref([
  { typeName: '法律法规', num: 25 },
  { typeName: '规章制度', num: 26 },
  { typeName: '操作规程', num: 65 },
])

const optionData = ref<{ name: string; value: number }[]>([
  { name: '消控室离岗', value: 9 },
  { name: '消防安全通道阻塞', value: 0 },
  { name: '烟火识别', value: 235 },
  { name: '安消联动', value: 788 },
])
// 规则制度

const getTrainPlanStatistics = async () => {
  const res: any = await $API.post({
    url: '/device/management/deviceDataOverview',
    params: {
      orgCode: userInfo.value.unitId,
      modelType: 'unit_base_url',
    },
  })
  if (res.code !== 'success') return
  let _data = res.data
  accessType.value.forEach((i) => {
    i.num = _data[i.key] || '0'
  })
}
// 物联网设备在线率
const getDeviceOperationRate = async () => {
  const res: any = await $API.post({
    url: '/device/management/getDeviceOperationRate',
    params: {
      orgCode: userInfo.value.unitId,
      // isQueryVideo: '0',
      // isTolerant: '0',
      modelType: 'unit_base_url',
    },
  })
  if (res.code !== 'success') return
  let _data = res.data
  // deviceTypeList
  //  { typeName: '独立式烟感', num: 2175, total: 2175, color: '#46F9FF' },
  deviceTypeList.value = _data?.typePercentage.map((i) => {
    return { typeName: i.deviceTypeName, num: i.onlineNum, total: i.typeTotal, ratio: i.typePercentage, ...i }
  })
  // accessType.value.forEach((i) => {
  //   i.num = _data[i.key] || '0'
  // })
}
// 视频设备
const getVideoDeviceMonitoring = async () => {
  const res: any = await $API.post({
    url: '/device/management/getVideoDeviceMonitoring',
    params: {
      orgCode: userInfo.value.unitId,
      modelType: 'unit_base_url',
    },
  })
  if (res.code !== 'success') return
  const predefinedValues = {
    消控室离岗: 9,
    消防安全通道阻塞: 0,
    烟火识别: 235,
    安消联动: 788,
  }

  let _data = res.data.sort((a, b) => b.videoRatio - a.videoRatio)
  optionData.value = _data.map((item) => {
    let value = item.videoNum || 0 // 使用逻辑或操作符处理空值情况
    // 如果 value 为 0，则使用预定义的映射值
    if (value === 0) {
      value = predefinedValues[item.usageName] || 0
    }
    return {
      name: item.usageName,
      value: value,
      ratio: item.videoRatio + '%',
    }
  })
}

// /device/management/getVideoDeviceMonitoring

const init = () => {
  getTrainPlanStatistics()
  getDeviceOperationRate()
  getVideoDeviceMonitoring()
}
onMounted(() => {
  init()
})
</script>
<style lang="scss" scoped>
.leftPopup {
  width: 468.096px;
  height: 100%;
}
</style>
