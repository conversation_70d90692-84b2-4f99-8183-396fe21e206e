<template>
  <div class="header-box">
    <div class="header-left">
      <div class="date-box">
        <div class="date-text-time">{{ currentTime }}</div>
        <div class="date-text-date">{{ weekZh }}</div>
      </div>
      <div v-if="isShowTab" class="tab-item-bg" :class="curentTab == 1 ? 'active' : ''" @click="handleClick(1)">
        安全管理
      </div>
      <div v-if="isShowTab" class="tab-item-bg" :class="curentTab == 2 ? 'active' : ''" @click="handleClick(2)">
        风险防控
      </div>
    </div>
    <div class="header-title">
      <div class="title">中国外运长航资产安全管理监管驾驶舱</div>
    </div>
    <div class="header-right">
      <div v-if="isShowTab" class="tab-item-bg" :class="curentTab == 3 ? 'active' : ''" @click="handleClick(3)">
        设备运行
      </div>
      <div v-if="isShowTab" class="tab-item-bg" :class="curentTab == 4 ? 'active' : ''" @click="handleClick(4)">
        安全态势
      </div>
      <div v-if="!isShowTab"></div>
      <div v-if="!isShowTab"></div>
      <div class="other-box">
        <div class="other-box-pc"></div>
        <div v-if="isShowTab" class="other-box-local" @click="openSubGis"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDateFormat, useNow } from '@vueuse/core'
import { inject, onMounted, ref } from 'vue'
import router from '~/router'
import { useRoute } from 'vue-router'

const props = defineProps<{
  isShowTab: boolean
}>()
const route = useRoute()

const openNewWindow = inject('openNewWindow', () => {})
// 当前时间
const currentTime = useDateFormat(useNow(), 'HH:mm:ss')
// 星期几
const weekZh = useDateFormat(useNow(), 'YYYY-MM-DD dddd', { locales: 'zh-CN' })

const curentTab = ref(1)

const handleClick = (index: number) => {
  curentTab.value = index
  router.push({ query: { tab: index } })
}

const openSubGis = () => {
  openNewWindow()
}
onMounted(() => {
  const tab = route.query.tab
  if (tab) {
    curentTab.value = Number(tab)
  }
})
</script>

<style lang="scss" scoped>
.header-box {
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
  height: 100.032px;
  background: url('../../../assets/image/shujujiashicang/header-bg.png') no-repeat center/contain;
  background-size: 100% 100%;
  .header-left {
    position: absolute;
    left: 0;
    top: 0;
    width: 25%;
    height: 76.032px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    align-items: center;
    .date-box {
      padding-left: 16.512px;
      width: 149.952px;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      color: #fff;
      .date-text-time {
        font-size: 19.2px;
      }
      .date-text-date {
        font-size: 10.56px;
      }
    }

    .tab-item-bg {
      width: 172.8px;
      height: 48.576px;
      line-height: 48.576px;
      text-align: center;
      color: #fff;
      font-size: 19.2px;
      font-family: 'shujujiashicang', 'Microsoft YaHei';
      background: url('../../../assets/image/shujujiashicang/tab-bg.png') no-repeat center;
      background-size: 100% 100%;
      cursor: pointer;
      &:nth-child(2) {
        margin-left: -14.976px;
      }
    }
    .active {
      background: url('../../../assets/image/shujujiashicang/tab-bg-active.png') no-repeat center;
      background-size: 100% 100%;
    }
  }
  .header-title {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .title {
      font-family: DOUYU;
      font-weight: normal;
      font-size: 32px;
      color: #ffffff;
      background: linear-gradient(0deg, #31beff 0%, #9ebeff 0%, #effcfe 75.3173828125%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .header-right {
    position: absolute;
    right: 0;
    top: 0;
    width: 25%;
    height: 76.032px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    align-items: center;
    .other-box {
      padding-right: 16.512px;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      .other-box-pc,
      .other-box-local {
        width: 42.048px;
        height: 42.048px;
        background-color: #fff;
        background: url('../../../assets/image/shujujiashicang/head-pc.png') no-repeat center;
        background-size: 100% 100%;
        cursor: pointer;
      }
      .other-box-local {
        margin-left: 8.064px;
        background: url('../../../assets/image/shujujiashicang/head-local.png') no-repeat center;
        background-size: 100% 100%;
      }
    }

    .tab-item-bg {
      width: 172.8px;
      height: 48.576px;
      line-height: 48.576px;
      text-align: center;
      color: #fff;
      font-size: 19.2px;
      font-family: 'shujujiashicang', 'Microsoft YaHei';
      background: url('../../../assets/image/shujujiashicang/tab-bg-right.png') no-repeat center;
      background-size: 100% 100%;
      cursor: pointer;
      &:nth-child(2) {
        margin-left: -14.976px;
      }
    }
    .active {
      background: url('../../../assets/image/shujujiashicang/tab-bg-right-active.png') no-repeat center;
      background-size: 100% 100%;
    }
  }
}
</style>
