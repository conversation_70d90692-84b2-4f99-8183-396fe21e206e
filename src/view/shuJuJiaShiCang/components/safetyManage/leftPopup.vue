<template>
  <div class="leftPopup">
    <div class="part-one h-[30%]">
      <ChartTitle title="接入单位" />
      <accessUnit
        v-loading="loading"
        element-loading-text="加载中..."
        element-loading-background="rgba(29, 53, 100, 0.8)"
        :dataList="accessType"
      />
    </div>
    <div class="part-two h-[30%]">
      <ChartTitle title="组织人员" />
      <organizingPersonnel :dataList="orgManager" />
    </div>
    <div class="part-three h-[30%]" v-if="active == 1">
      <ChartTitle title="规程制度" />
      <regulationsRules :dataList="rules" :message="messageList" />
    </div>
    <div class="part-four h-[30%]" v-if="active == 2">
      <ChartTitle title="教育培训" />
      <educationPage v-if="active == 2" :educationInfo="educationInfo" />
    </div>
    <div class="check-btn-box flex text-[#fff] justify-center items-center">
      <div :class="{ active: active == 1 }" @click="active = 1">规程制度</div>
      <div :class="{ active: active == 2 }" @click="active = 2">教育培训</div>
    </div>
    <!-- -->
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import accessUnit from './components/accessUnit.vue'
import regulationsRules from './components/regulationsRules.vue'
import educationPage from './components/educationPage.vue'
import organizingPersonnel from './components/organizingPersonnel.vue'
import config from '~/config'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
const userInfo = useUserInfo()
const curUnitId = ref(userInfo.value.unitId)
const active = ref(1)
const loading = ref(false)
const accessType = ref([
  { typeName: '接入单位', num: 0, unit: '家', key: 'unitNum' },
  { typeName: '接入项目', num: 0, unit: '个', key: 'projectNum' },
  { typeName: '接入楼栋', num: 0, unit: '栋', key: 'buildingNum' },
  { typeName: '建筑面积', num: 0, unit: '万m2', key: 'totalArea' },
]) as any

const orgManager = ref([]) as any

// map.put('bottomManager', '0') //基层公司相关管理人员
// map.put('bottomParties', '0') //基层公司相关方人员
// map.put('directManager', '0') //直管公司管理人员
// map.put('parentManager', '0') //总部管理人员
// map.put('tenantryManager', '0') //承租方安全管理人员

// 规程制度
const rules = ref([
  { typeName: '法律法规', num: 0, relationType: 1 },
  { typeName: '规章制度', num: 0, relationType: 2 },
  { typeName: '操作规程', num: 0, relationType: 3 },
])
// 规则制度
const messageList = ref([])

const educationInfo = ref({
  complateRate: 0,
  list: [
    { typeName: '培训任务总数', num: 0, key: 'taskTotalCount' },
    { typeName: '进行中任务数', num: 0, key: 'taskingCount' },
    { typeName: '培训通过率', num: 0, key: 'passRate' },
  ],
  imgList: [
    // {
    //   imgUrl: imgUrl1,
    //   state: '进行中',
    //   typeName: '《强化建设专题培训》',
    // },
  ],
})
// 接入单位
const getaccessUnit = async () => {
  loading.value = true
  const res: any = await $API.post({
    url: '/ehs-clnt-internetMonitor-service/safeManage/accessUnit',
    params: {
      orgCode: curUnitId.value,
    },
  })
  if (res.code !== 'success') return
  let _data = res.data
  accessType.value.forEach((i) => {
    if (i.key === 'totalArea') {
      i.num = Number(_data[i.key] / 10000).toFixed(2)
    } else {
      i.num = _data[i.key]
    }
  })
  loading.value = false
}

// 组织人员
const getOrganizationPersonnel = async () => {
  const res: any = await $API.post({
    url: '/safeManage/organizationPersonnel',
    params: {
      orgCode: curUnitId.value,
      modelType: 'unit_base_url',
    },
  })
  if (res.code !== 'success') return
  orgManager.value = [
    { name: '总部管理人员', value: res.data.groupTotal, key: 'groupTotal' },
    { name: '直管公司管理人员', value: res.data.unitUserCount, key: 'unitUserCount' },
    { name: '基层公司安全管理人员', value: res.data.baseManageCount, key: 'baseManageCount' },
    { name: '基层公司相关方人员', value: res.data.baseInterCount, key: 'baseInterCount' },
    { name: '基层公司承租方人员', value: res.data.tenantCount, key: 'tenantCount' },
  ]
  orgManager.value = orgManager.value.filter((item) => {
    return item.value !== null && item.value !== undefined && item.value !== ''
  })

  // let _data = res.data
  // accessType.value.forEach((i) => {
  //   i.num = _data[i.key]
  // })
}

// 规章制度 统计
const getRegulationsStatistics = async () => {
  const res: any = await $API.post({
    url: '/safeManage/regulationsStatistics',
    params: {
      orgCode: curUnitId.value,
      modelType: 'unit_base_url',
      zhId: userInfo.value.zhId,
    },
  })
  if (res.code !== 'success') return

  rules.value.forEach((i) => {
    i.num = res.data.find((j) => j.relationType == i.relationType)?.statuteCount || 0
  })
}

// 规章制度 列表
const getRegulationsList = async () => {
  const res: any = await $API.post({
    url: '/safeManage/regulationsList',
    params: {
      orgCode: curUnitId.value,
      pageNo: 1,
      pageSize: 3,
      modelType: 'unit_base_url',
      zhId: userInfo.value.zhId,
    },
  })
  if (res.code !== 'success') return
  messageList.value =
    res.data.rows?.map((e) => ({
      title: e.statuteName,
      // 1-法律法规 2-规章制度 3-操作规程 4-标准规范
      typeName:
        e.relationType === 1
          ? '法律法规'
          : e.relationType === 2
            ? '规章制度'
            : e.relationType === 3
              ? '操作规程'
              : '标准规范',
      time: e.releaseDate,
      department: e.releaseeptName,
    })) || []
}

// 教育培训
const getTrainPlanStatistics = async () => {
  const res: any = await $API.post({
    url: '/ehs-clnt-internetMonitor-service/safeManage/trainPlanStatistics',
    params: {
      orgCode: curUnitId.value,
    },
  })
  if (res.code !== 'success') return
  let _data = res.data
  educationInfo.value.complateRate = _data.finishRate
  educationInfo.value.list.forEach((i) => {
    i.num = _data[i.key] || 0
  })
  // ehs_file
  let _planTaskPic = _data.planTaskPic || []
  const trainTaskStatusMap = {
    1: '未开始',
    2: '已开始',
  }
  // 创建映射后的列表
  const mappedImgList = _planTaskPic.map((i) => {
    return {
      typeName: i.trainTaskName,
      state: trainTaskStatusMap[i.trainTaskStatus],
      imgUrl: import.meta.env.VITE_BASE_IMG_URL + (i?.picFile?.filePath || ''),
    }
  })

  // 检查数组长度并补充空对象至长度为3
  while (mappedImgList.length < 3) {
    mappedImgList.push({ typeName: '', state: '', imgUrl: '' })
  }

  // 更新 educationInfo.value.imgList
  educationInfo.value.imgList = mappedImgList
}

// /safeManage/regulationsStatistics
const init = () => {
  getaccessUnit()
  getOrganizationPersonnel()
  getRegulationsStatistics()
  getRegulationsList()
  getTrainPlanStatistics()
}
onMounted(() => {
  init()
})
</script>
<style lang="scss" scoped>
.leftPopup {
  width: 468.096px;
  height: 100%;
}
.check-btn-box {
  div {
    background-size: 100% 100%;
    padding: 3px 15px;
    background: #042550;
    box-shadow: inset 0px 0px 18px 0px #65b6ff;
    border: 1px solid #3f9bcf;
  }
  .active {
    background-image: url(../../../../assets/image/tabs-bg.png);
  }
}
</style>
