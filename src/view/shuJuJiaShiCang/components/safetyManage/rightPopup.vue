<template>
  <div class="leftPopup h-full">
    <div class="part-one h-[25%]">
      <ChartTitle title="风险分级管控" />
      <riskClass :quarter="quarter" :yearData="yearData" :riskLevelFourColorList="riskLevelFourColorList" />
    </div>
    <div class="part-two h-[40%]">
      <ChartTitle title="隐患排查治理" />
      <hiddenDanger
        v-loading="loading"
        element-loading-text="加载中..."
        element-loading-background="rgba(29, 53, 100, 0.8)"
        :hiddenInfo="hiddenInfo.rateList"
        :message="messageList"
        :dataList="rules"
      />
    </div>
    <div class="part-three h-[25%]" v-if="active == 1">
      <ChartTitle title="现场管理" />
      <siteManagement
        v-loading="managementInfoLoading"
        element-loading-text="加载中..."
        element-loading-background="rgba(29, 53, 100, 0.8)"
        :dataList="managementInfo"
        :pieData="pieData"
      />
    </div>
    <div class="part-four h-[25%]" v-if="active == 2">
      <ChartTitle title="应急演练" />
      <emergencyDrill :dataInfo="queryEmergencyDrillList" />
    </div>
    <div class="check-btn-box flex text-[#fff] justify-center items-center">
      <div :class="{ active: active == 1 }" @click="xianchang">现场管理</div>
      <div :class="{ active: active == 2 }" @click="active = 2">应急演练</div>
    </div>
    <!-- -->
  </div>
</template>
<script setup lang="ts">
import riskClass from './components/riskClass.vue'
import hiddenDanger from './components/hiddenDanger.vue'
import siteManagement from './components/siteManagement.vue'
import emergencyDrill from './components/emergencyDrill.vue'
import { reactive, ref, computed, onMounted } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
const userInfo = useUserInfo()
const loading = ref(false)
const managementInfoLoading = ref(false)
const active = ref(1)
const orgProps = computed(() => {
  return userInfo.value.unitId
})
const queryEmergencyDrillList = ref()

const yearData = ref()
const quarter = ref()
const riskLevelFourColorList = ref([])
const hiddenInfo = reactive({
  rateList: [],
}) as any

const managementInfo = ref()
const pieData = ref([])

const rules = ref()

// 规则制度
const messageList = ref([])

// 隐患排查治理
const hazardManagement = async () => {
  const res: any = await $API.post({
    url: '/safeManage/hazardManagement',
    params: {
      orgCode: orgProps.value,
      modelType: 'unit_base_url',
    },
  })
  if (res.code !== 'success') return
  hiddenInfo.rateList = [
    {
      title: '本年任务完成率',
      rate: res.data?.taskRate || 0,
      color: '#3e97e8',
      plan: res.data?.planNum || 0,
      task: res.data?.taskNum || 0,
      name1: '本年检查计划',
      name2: '本年检查任务',
    },
    {
      title: '隐患整改率',
      rate: res.data?.hazardRectificationRate || 0,
      color: '#E73C7F',
      plan: res.data?.generalHazardNum || 0,
      task: res.data?.majorHazardNum || 0,
      name1: '一般隐患',
      name2: '重大隐患',
    },
  ]
  rules.value = [
    { typeName: '本年检查计划', num: res.data.allPlanCount ?? 0 },
    { typeName: '本年检查任务', num: res.data.allPlanTaskCount ?? 0 },
    { typeName: '任务完成率', num: (res.data.completePlanTaskRate ?? 0) + '%' },
  ]
}

// 隐患排查治理列表
const hazardManagementPage = async () => {
  loading.value = true

  const res: any = await $API.post({
    url: '/safeManage/hazardManagementList',
    params: {
      orgCode: orgProps.value,
      pageNo: 1,
      pageSize: 3,
      modelType: 'unit_base_url',
    },
  })
  if (res.code !== 'success') return
  messageList.value = res.data.rows.splice(0, 3) || []
  loading.value = false
}

// 风险分级管控
const getRiskLevelRadio = async (e) => {
  const res: any = await $API.post({
    url: '/safeManage/riskLevelRate',
    params: {
      orgCode: orgProps.value,
      modelType: 'unit_base_url',
      // type 1 = 年度 2 = 季度
      type: e,
    },
  })
  if (res.code !== 'success') return
  if (e == 1) {
    yearData.value = res.data.ratio
  } else {
    quarter.value = res.data.ratio
  }
}

//风险分级管控-四色图
const riskLevelFourColor = async () => {
  const res: any = await $API.post({
    url: '/safeManage/riskLevelFourColor',
    params: {
      orgCode: orgProps.value,
      modelType: 'unit_base_url',
    },
  })
  if (res.code !== 'success') return
  riskLevelFourColorList.value =
    res.data.dataMap.map((e) => ({
      name: e.dictLabel,
      value: e.pointNum,
    })) || []
}
const xianchang = () => {
  active.value = 1
  siteManagementPage()
}

// 现场管理
const siteManagementPage = async () => {
  managementInfoLoading.value = true
  const res: any = await $API.post({
    url: '/safeManage/siteManagement',
    params: {
      orgCode: orgProps.value,
      modelType: 'unit_base_url',
    },
  })
  if (res.code !== 'success') return
  managementInfo.value = [
    {
      typeName: '特种设备',
      num: res.data.specialNum,
      rate: res.data.specialRate || 0,
    },
    {
      typeName: '消防设施',
      num: res.data.fireNum,
      rate: res.data.fireRate || 0,
    },
  ]
  pieData.value =
    res.data.safe
      .map((e) => ({
        name: e.type,
        value: e.number,
      }))
      .sort((a, b) => b.value - a.value) || []
  managementInfoLoading.value = false
}

// 应急演练
const queryEmergencyDrillStatistics = async () => {
  const res: any = await $API.post({
    url: '/safeManage/queryEmergencyDrillStatistics',
    params: {
      orgCode: orgProps.value,
      modelType: 'unit_base_url',
    },
  })
  if (res.code !== 'success') return
  queryEmergencyDrillList.value = [
    { title: '应急演练计划', num: res.data.walkthroughPlanCount || 0 },
    { title: '实际演练次数', num: res.data.walkthroughTaskCount || 0 },
    { title: '完成率', num: res.data.walkthroughTaskRate || '0%' },
  ]
}

onMounted(() => {
  getRiskLevelRadio(1)
  getRiskLevelRadio(2)
  queryEmergencyDrillStatistics()
  hazardManagement()
  hazardManagementPage()
  riskLevelFourColor()
  siteManagementPage()
})
</script>
<style lang="scss" scoped>
.check-btn-box {
  div {
    background-size: 100% 100%;
    padding: 3px 15px;
    background: #042550;
    box-shadow: inset 0px 0px 18px 0px #65b6ff;
    border: 1px solid #3f9bcf;
  }
  .active {
    background-image: url(../../../../assets/image/tabs-bg.png);
  }
}
</style>
