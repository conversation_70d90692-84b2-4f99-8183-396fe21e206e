<template>
  <div class="riskClass">
    <Pie :optionData="dataList" />
  </div>
</template>

<script setup lang="ts">
//
import { ref } from 'vue'
import Pie from './Pie.vue'

const props = defineProps({
  dataList: {
    type: Object,
    default: () => ({}),
  },
})
// const props = defineProps({
//   dataList: {
//     type: Array,
//     default: () => [],
//   },
// })
</script>

<style lang="scss" scoped>
.riskClass {
  padding: 10px 0;
  height: 85%;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
}
</style>
