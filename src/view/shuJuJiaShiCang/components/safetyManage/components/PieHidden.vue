<template>
  <div class="line-part w-full h-full" ref="pieChart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
import { isFontSizeRem } from '@/common/utils'
const props = defineProps({
  optionData: {
    type: Object,
    default: () => ({}),
  },
})
const total = ref()
watch(
  () => props.optionData,
  () => {
    total.value = 0
    props.optionData.forEach((item: any) => {
      total.value += item.value
    })
    initEcharts()
  }
)

const pieChart = ref()
let myChart: any

function initEcharts() {
  if (myChart) myChart.dispose()

  // 标记一个对象，使其永远不会再成为响应式对象
  myChart = echarts.init(pieChart.value)

  // 传入数据生成 option

  const option = {
    color: ['#ee001d', '#fe7b08', '#fff300', '#5a88ff'], // 自定义颜色数组
    title: {
      text: `{a|风险等级}\n{b|分布}`,
      x: 'center', //水平安放位置，默认为'left'，可选为：'center' | 'left' | 'right' | {number}（x坐标，单位px）
      y: 'center', //垂直安放位置，默认为'top'，可选为：'center' | 'top' | 'bottom' | {number}（y坐标，单位px）
      textStyle: {
        //主标题文本样式{"fontSize": 18,"fontWeight": "bolder","color": "#333"}
        fontSize: isFontSizeRem(12),
        color: '#fff',
        rich: {
          a: {
            fontSize: isFontSizeRem(12),
            padding: [3, 0],
          },
          b: {
            fontSize: isFontSizeRem(12),
          },
        },
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: `{b} {d}%`,
      confine: true,
    },
    legend: {
      show: false,
      type: 'scroll',
      icon: 'circle',
      top: 'center',
      right: '10%',
      // 竖向排列
      orient: 'vertical',
      textStyle: {
        color: '#fff',
        fontSize: isFontSizeRem(11),
      },
      // itemGap: 5,
      // itemHeight: 11,
      pageIconColor: '#ff781f', // 设置翻页箭头颜色
      pageTextStyle: {
        color: '#999', // 设置翻页数字颜色
      },
      pageIconSize: 11, // 设置翻页箭头大小
      formatter: (name: string) => {
        const currentNum = props.optionData.find((item: any) => item.name === name)?.value
        if (!currentNum) return name
        return `${name.length > 6 ? name.slice(0, 6) + '...' : name} ${((+(currentNum / total.value).toFixed(4) * 10000) / 100).toFixed(2)}% ${currentNum}`
      },
      tooltip: {
        show: true,
      },
    },
    series: [
      {
        type: 'pie',
        // padAngle: 3,
        radius: ['65%', '95%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: false,
            fontSize: isFontSizeRem(11),
            color: '#fff',
            formatter: `{c}\n\n{b}`,
          },
        },
        labelLine: {
          show: false,
        },
        data: props.optionData,
      },
    ],
  }

  myChart.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart) myChart.resize()
  })
}

onMounted(() => {
  initEcharts()
})

// 把initEcharts暴露出去
defineExpose({
  // initEcharts,
})
</script>

<style scoped>
.title-num {
  position: absolute;
  bottom: 10%;
  left: 18%;
  z-index: 500;
  background: linear-gradient(to right, #061d31, #227eb6 60%, #227eb6 60%, #051525);

  .a {
    color: #fff;
    font-size: 14px;
    font-weight: 700;
    margin-right: 10px;
  }
  .b {
    color: #fff;
    font-size: 18px;
    font-weight: 400;
  }
}
</style>
