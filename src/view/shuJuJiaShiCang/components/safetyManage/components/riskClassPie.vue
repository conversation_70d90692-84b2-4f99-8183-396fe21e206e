<template>
  <div class="w-full h-full" :class="{ progress_red: bgimg, pgs: pgsStyle }" ref="progressChart"></div>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts'
import 'echarts-liquidfill' // 引入水球图的组件
import { defineComponent, markRaw, onMounted, onUnmounted, ref, watch } from 'vue'
import { isFontSizeRem } from '@/common/utils'
defineComponent({ name: 'ProgressChart' })

const props = defineProps({
  titleValueLeft: {
    type: String,
    default: '30%',
  },
  titleValueTop: {
    type: String,
    default: '35%',
  },
  titleTextLeft: {
    type: String,
    default: '55%',
  },
  titleTextTop: {
    type: String,
    default: '41%',
  },
  showText: {
    type: Boolean,
    default: true,
  },
  pgsStyle: {
    type: Boolean,
    default: false,
  },
  bgimg: {
    type: Boolean,
    default: true,
  },
  echartsData: {
    type: Number || String,
    default: '',
  },
  type: {
    type: String,
    default: 'red',
  },
  borderColor: {
    type: Array,
    default: () => ['rgba(47, 191, 97, 0.5)', 'rgba(47, 191, 97, 0.2)'],
  },
  color: {
    type: Array,
    default: () => {
      return ['rgba(255, 91, 92, 0.5)', 'rgba(255, 91, 92, 0.8)']
    },
  },
  titleText: {
    type: String,
    default: '%',
  },
})
const formattedRate = (e) => {
  if (Number.isInteger(e)) {
    return e // 如果e是整数，直接返回
  } else {
    return e.toFixed(2) // 否则，保留两位小数
  }
}
const progressChart = ref()
let myChart = ref<any>(null)

function initEcharts(val: number) {
  myChart.value = markRaw(echarts.init(progressChart.value))
  const borderColor: any[] = props.borderColor || []
  const colors = props.color
  const titleText = props.titleText
  const data = [val / 100, val / 100]
  const option = {
    title: [
      // {
      //   show: props.showText,
      //   // text: val + '%',
      //   text: `${val}`,
      //   subtext: '%',
      //   textStyle: {
      //     fontSize: '26px',
      //     fontFamily: 'DS-Digital-jia',
      //     fontWeight: 'bold',
      //     color: '#ffffff',
      //   },
      //   subtextStyle: {
      //     fontSize: '16px',
      //     fontFamily: 'DS-Digital-jia',
      //     fontWeight: 'bold',
      //     color: '#ffffff',
      //     rich: {
      //       newline: {
      //         whiteSpace: 'nowrap',
      //       },
      //     },
      //   },
      //   left: props.titleValueLeft,
      //   top: props.titleValueTop,
      // },
    ],
    series: [
      {
        type: 'liquidFill',
        radius: '90%',
        center: ['50%', '50%'],
        data: data,
        color: borderColor,
        backgroundStyle: {
          color: 'black',
        },
        outline: {
          show: true,
          borderDistance: 0,
          itemStyle: {
            borderWidth: 1,
            borderColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0.05,
                color: borderColor[0],
              },
              {
                offset: 1,
                color: borderColor[1],
              },
            ]),
          },
        },
        label: {
          normal: {
            rich: {
              a: {
                fontSize: isFontSizeRem(26),
                fontFamily: 'DS-Digital-jia',
                fontWeight: 'bold',
                color: '#ffffff',
              },
              b: {
                fontSize: isFontSizeRem(16),
                fontFamily: 'DS-Digital-jia',
                fontWeight: 'bold',
                color: '#ffffff',
                padding: [5, 0, 0, -2],
              },
            },
            formatter: function () {
              return '{a|' + formattedRate(Number(val)) + '}' + ' {b|%}'
            },
            show: true,
            textStyle: {
              fontSize: isFontSizeRem(14),
              fontWeight: 'normal',
              color: '#fff',
            },
          },
        },
      },
      {
        name: '外边框',
        type: 'pie',
        z: 5,
        radius: ['97%', '100%'],
        center: ['50%', '50%'],
        label: {
          show: false,
        },
        data: [
          {
            value: 70,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0.05,
                  color: borderColor[0],
                },
                {
                  offset: 1,
                  color: borderColor[1],
                },
              ]),
            },
          },
        ],
      },
    ],
  }
  myChart.value.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart.value) myChart.value.resize()
  })
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

onMounted(() => {
  initEcharts(props.echartsData)
})

onUnmounted(() => {
  destroyEcharts()
})

watch(
  () => props.echartsData,
  (val: any) => {
    initEcharts(props.echartsData)
  }
)
</script>

<style scoped>
.pgs {
  width: 100%;
  height: 100%;
}
.progress_red {
  background: url('@/assets/image/bigScreen/progress-red-bg.png') no-repeat center;
  background-size: contain;
}
</style>
