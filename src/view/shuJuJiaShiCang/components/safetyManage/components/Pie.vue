<template>
  <div class="line-part w-full h-full" ref="pieChart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
import { isFontSizeRem } from '@/common/utils'
const props = defineProps({
  optionData: {
    type: Object,
    default: () => ({}),
  },
})
const total = ref(0)
let outCricle: any = []
watch(
  () => props.optionData,
  (newOptionData) => {
    newOptionData.forEach((item: any) => {
      total.value += item.value
    })
    outCricle = JSON.parse(JSON.stringify(newOptionData)) // 更新outCricle的值
    initEcharts()
  }
)

// 深拷贝props.optionData
outCricle = JSON.parse(JSON.stringify(props.optionData))
const colorArray = [
  ['#3158D5', 'rgba(49, 88, 213, 0.3)'],
  ['#1AAFCA', 'rgba(26, 175, 202, 0.3)'],
  ['#D1D94D', 'rgba(209, 217, 77, 0.3)'],
  ['#D16E4F', 'rgba(209, 110, 79, 0.3)'],
  ['#D53131', 'rgba(213, 49, 49, 0.3)'],
  ['#02A305', 'rgba(2, 163, 5, 0.3)'],
  ['#F06713', 'rgba(240, 103, 19, 0.3)'],
  ['#1AAFCA', 'rgba(26, 175, 202, 0.3)'],
  ['#23CAFF', 'rgba(49, 88, 213, 0.3)'],
  ['#438CFF', 'rgba(49, 88, 213, 0.3)'],
  ['#FFB800', 'rgba(49, 88, 213, 0.3)'],
  ['#00ECB3', 'rgba(49, 88, 213, 0.3)'],
  ['#B2C3C5', 'rgba(49, 88, 213, 0.3)'],
]
const handelData = () => {
  // 内部圆的数据
  props.optionData.forEach((item: any, index: number) => {
    item.itemStyle = {
      color: colorArray[index][0],
      borderColor: colorArray[index][0],
    }
  })
  return props.optionData
}
const handelData2 = () => {
  // 外圈的圆数据
  outCricle.forEach((item: any, index: number) => {
    item.itemStyle = {
      // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //   { offset: 0, color: colorArray[index][0] },
      //   { offset: 1, color: colorArray[index][1] },
      // ]),
      color: colorArray[index][1],
      borderColor: colorArray[index][1],
    }
  })

  return outCricle
}

const pieChart = ref()
let myChart: any

function initEcharts() {
  if (myChart) myChart.dispose()

  // 标记一个对象，使其永远不会再成为响应式对象
  myChart = echarts.init(pieChart.value)

  // 传入数据生成 option
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: `{b} {c}`,
    },
    title: {
      text: `{a|${total.value}}\n{b|安全管理\n人员}`,
      // subtext: '设备总数',
      left: '13%',
      top: 'center',
      textStyle: {
        color: '#fff',
        fontSize: isFontSizeRem(14),
        rich: {
          a: {
            fontSize: isFontSizeRem(24),
            padding: [3, 0],
            color: '#D3E5FF',
            align: 'center',
            fontWeight: 'bold',
            fontFamily: 'DS-Digital-jia',
          },
          b: {
            fontSize: isFontSizeRem(14),
            color: '#D3E5FF',
            align: 'center',
          },
        },
      },
    },
    legend: {
      type: 'scroll',
      icon: 'circle',
      top: 'center',
      left: '40%',
      // 竖向排列
      orient: 'vertical',
      textStyle: {
        color: '#fff',
        fontSize: isFontSizeRem(12),
      },
      itemStyle: {
        borderWidth: 2,
        // borderColor: 'red',
        color: 'transparent',
      },
      // itemGap: 5,
      // itemHeight: 11,
      pageIconColor: '#ff781f', // 设置翻页箭头颜色
      pageTextStyle: {
        color: '#999', // 设置翻页数字颜色
      },
      pageIconSize: 11, // 设置翻页箭头大小
      formatter: (name: string) => {
        const currentNum = props.optionData.find((item: any) => item.name === name)?.value || 0
        // if (!currentNum) return name
        return `${name.length > 15 ? name.slice(0, 15) + '...' : name}   ${currentNum}   ${((+(currentNum / (total.value || 1)).toFixed(4) * 10000) / 100).toFixed(2)}%`
      },
      tooltip: {
        show: true,
      },
    },
    series: [
      {
        type: 'pie',
        // padAngle: 3,
        radius: ['40%', '30%'],
        center: ['20%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: false,
            fontSize: isFontSizeRem(11),
            color: '#fff',
            formatter: `{c}\n\n{b}`,
          },
        },
        labelLine: {
          show: false,
        },
        data: handelData(),
      },
      {
        type: 'pie',
        // padAngle: 3,
        radius: ['60%', '50%'],
        center: ['20%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: false,
            fontSize: isFontSizeRem(11),
            color: '#fff',
            formatter: `{c}\n\n{b}`,
          },
        },
        labelLine: {
          show: false,
        },
        data: handelData2(),
      },
    ],
  }

  myChart.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart) myChart.resize()
  })
}

onMounted(() => {
  initEcharts()
})

// 把initEcharts暴露出去
defineExpose({
  initEcharts,
})
</script>

<style scoped>
.title-num {
  position: absolute;
  bottom: 10%;
  left: 18%;
  z-index: 500;
  background: linear-gradient(to right, #061d31, #227eb6 60%, #227eb6 60%, #051525);

  .a {
    color: #fff;
    font-size: 14px;
    font-weight: 700;
    margin-right: 10px;
  }
  .b {
    color: #fff;
    font-size: 18px;
    font-weight: 400;
    background-color: rgb(26, 175, 202);
  }
}
</style>
