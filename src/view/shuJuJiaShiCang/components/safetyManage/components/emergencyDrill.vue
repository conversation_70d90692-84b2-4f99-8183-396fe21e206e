<template>
  <div class="riskClass">
    <div class="riskClass-content">
      <div class="riskClass-content-left">
        <div class="num">{{ dataInfo[0]?.num }}</div>
        <div class="name">{{ dataInfo[0]?.title }}</div>
      </div>
      <div class="riskClass-content-middle">
        <ProgressChart
          :bgimg="false"
          :pgsStyle="true"
          :echartsData="dataInfo[2]?.num"
          :borderColor="colorGress"
          :titleValueLeft="p.titleValueLeft"
          :titleValueTop="p.titleValueTop"
          :titleTextLeft="p.titleTextLeft"
          :titleTextTop="p.titleTextTop"
        />
        <span class="_ch_con">完成率</span>
      </div>
      <div class="riskClass-content-right">
        <div class="num">{{ dataInfo[1]?.num }}</div>
        <div class="name">{{ dataInfo[1]?.title }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ProgressChart from '../../ProgressChart.vue'
const props = defineProps({
  dataInfo: {
    type: Object,
    default: () => ({}),
  },
})

const p = {
  titleValueLeft: 'center',
  titleValueTop: '40%',
  titleTextLeft: '53%',
  titleTextTop: '44%',
}
//  84,214,158
const colorGress = ['rgba(80,201,25, 0.6)', 'rgb( 23,203,112)']
//
</script>

<style lang="scss" scoped>
.riskClass {
  padding: 0 24.96px;
  height: 83%;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  .riskClass-content {
    position: relative;
    width: 100%;
    height: 100%;
    background: url('@/assets/image/shujujiashicang/safetyManage/drill.png') no-repeat center/cover;
    .riskClass-content-left,
    .riskClass-content-right {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: absolute;
      width: 103.296px;
      transform: translateY(-50%);
      height: 61.248px;
      .num {
        font-weight: bold;
        font-size: 25.92px;
        color: #ffffff;
      }
      .name {
        font-size: 14.016px;
        color: #ffffff;
      }
    }
    .riskClass-content-left {
      left: 9.6px;
      top: 50%;
    }
    .riskClass-content-right {
      right: 9.6px;
      top: 50%;
    }
    .riskClass-content-middle {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-51%, -50%);
      width: 85.056px;
      height: 85.056px;
      border-radius: 50%;
      // background-color: #fff;;
      width: 159.936px;
      height: 159.936px;
      ._ch_con {
        position: absolute;
        bottom: 49.152px;
        left: 57.984px;
        font-size: 14.016px;
      }
    }
  }
}
</style>
