<template>
  <div class="riskClass">
    <div class="left">
      <Pie :optionData="riskLevelFourColorList" />
    </div>
    <!-- <div class="right"> -->
    <!-- <div v-for="item in progressChartList" :key="item.id" class="item">
        <div class="pie">
          <riskClassPie
            :bgimg="false"
            :pgsStyle="true"
            :echartsData="item.value"
            :borderColor="item.colorGress"
            :titleValueLeft="p.titleValueLeft"
            :titleValueTop="p.titleValueTop"
            :titleTextLeft="p.titleTextLeft"
            :titleTextTop="p.titleTextTop"
          />
        </div>
        <div class="title" style="white-space: pre-wrap">{{ item.name }}</div>
      </div> -->
    <!-- </div> -->
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import Pie from './PieHidden.vue'
import riskClassPie from './riskClassPie.vue'
const props = defineProps({
  quarter: {
    type: Number,
    default: 0,
  },
  yearData: {
    type: Number,
    default: 0,
  },
  riskLevelFourColorList: {
    type: Object,
    default: () => ({}),
  },
})

// const optionData = ref<{ name: string; value: number }[]>([
//   { name: '基层公司安全管理人员', value: 34 },
//   { name: '基层公司相关方人员', value: 54 },
//   { name: '直管公司管理人员', value: 14 },
//   { name: '总部管理人员', value: 42 },
//   { name: '承租方安全管理人员', value: 25 },
// ])

const p = {
  titleValueLeft: 'center',
  titleValueTop: '40%',
  titleTextLeft: '53%',
  titleTextTop: '44%',
}
//

const progressChartList = ref([
  {
    colorGress: ['rgba(6,143,227, 0.3)', 'rgb(6,143,226)'],
    value: 0,
    id: 2,
    // name: '本年风险辨识\n更新率',
  },
  {
    colorGress: ['rgba(80,201,25, 0.6)', 'rgb( 23,203,112)'],
    value: 0,
    id: 1,
    // name: '本季度风险辨识\n更新率'
  },
]) as any

watch(
  () => props.yearData,
  () => {
    progressChartList.value[1].value = props.yearData.toFixed(2)
  },
  {
    immediate: true,
    deep: true,
  }
)

watch(
  () => props.quarter,
  () => {
    progressChartList.value[0].value = props.quarter.toFixed(2)
  },
  {
    immediate: true,
    deep: true,
  }
)
</script>

<style lang="scss" scoped>
.riskClass {
  height: 83%;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  display: grid;
  // grid-template-columns: auto 1fr;
  align-items: center;
  .left {
    margin: 9 auto;
    width: 100%;
    height: 128.064px;
  }
  // .right {
  //   height: 100%;
  //   display: flex;
  //   justify-content: space-around;
  //   align-items: center;
  //   .item {
  //     width: 112.32px;
  //     margin-top: 40px;
  //     height: 100%;
  //     display: flex;
  //     flex-direction: column;
  //     justify-content: space-evenly;
  //     .pie {
  //       width: 100%;
  //       height: 112.32px;
  //     }
  //     .title {
  //       margin-top: -9.984px;
  //       font-size: 14.016px;
  //       width: 100%;
  //       height: 38.016px;
  //       text-align: center;
  //     }
  //   }
  // }
}
</style>
