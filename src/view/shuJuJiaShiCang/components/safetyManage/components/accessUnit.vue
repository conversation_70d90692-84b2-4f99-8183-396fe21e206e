<template>
  <div class="access-unite">
    <div v-for="(item, index) in dataList as any" :key="item.typeName" class="access-item">
      <div class="icon" :class="'icon-bg' + index"></div>
      <div class="right">
        <div class="item-title">{{ item.typeName }}</div>
        <div class="item-num number-text">
          {{ item.num }} <span class="unit">{{ item.unit }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const props = defineProps({
  dataList: {
    type: Array,
    default: () => [],
  },
})
</script>

<style lang="scss" scoped>
.access-unite {
  padding: 10px 20px;
  height: 85%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  .access-item {
    display: flex;
    align-items: center;
    .right {
      .item-title {
        font-size: 14.016px;
      }
      .item-num {
        width: 130px;
        font-weight: bold;
        font-size: 28.032px;
        color: #a4e4ff;
        .unit {
          font-size: 14.016px;
          color: #fff;
        }
      }
    }
  }
  .icon {
    // 宽100 高80
    width: 96px;
    height: 76.8px;
    background: url(@/assets/image/shujujiashicang/safetyManage/unit.png) no-repeat center/cover;
  }
  .icon-bg1 {
    background: url(@/assets/image/shujujiashicang/safetyManage/project.png) no-repeat center/cover;
  }
  .icon-bg2 {
    background: url(@/assets/image/shujujiashicang/safetyManage/build.png) no-repeat center/cover;
  }
  .icon-bg3 {
    background: url(@/assets/image/shujujiashicang/safetyManage/area.png) no-repeat center/cover;
  }
}
</style>
