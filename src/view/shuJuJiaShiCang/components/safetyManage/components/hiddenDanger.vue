<template>
  <div class="hidden-danger">
    <!-- 完成率 -->
    <div class="top">
      <div v-for="item in hiddenInfo" :key="item.title" class="top-item">
        <div class="left">
          <div class="pie">
            <hiddenInstrumentPanel :dataInfo="item" />
          </div>
          <div class="title">{{ item.title }}</div>
        </div>
        <div class="right">
          <div class="right-item">
            <div class="picture" :class="item.name1 == '一般隐患' ? 'right-top' : ''"></div>
            <div class="content">
              <div class="name">{{ item.name1 }}</div>
              <div class="num">{{ item.plan }}</div>
            </div>
          </div>
          <div class="right-item">
            <div class="picture" :class="item.name2 == '重大隐患' ? 'great' : ''"></div>
            <div class="content">
              <div class="name">{{ item.name2 }}</div>
              <div class="num">{{ item.task }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="middle message-content">
      <div class="info w-full h-full">
        <div v-for="item in message as any" :key="item.title" class="message-for">
          <div class="content">{{ toState(item.planState) }}</div>
          <div class="w-200px">
            <myTooltip :str="item.planName" />
          </div>
          <div class="time w-50px">
            {{
              item.taskNumMap?.allPlanTaskCount > 0
                ? (function () {
                    const percentage = (
                      (item.taskNumMap.completePlanTaskCount / item.taskNumMap.allPlanTaskCount) *
                      100
                    ).toFixed(2)
                    return Number.isInteger(parseFloat(percentage))
                      ? percentage.replace(/\.\d+/, '') + '%'
                      : percentage + '%'
                  })()
                : '0%'
            }}
          </div>
          <div class="w-150px">
            <myTooltip :str="item.planTypeName" />
          </div>
        </div>
      </div>
      <!-- <div class="pic"> -->
      <!-- <el-image src="@/assets/image/shujujiashicang/safetyManage/temp-1.png" /> -->
      <!-- </div> -->
    </div>
    <div class="bottom">
      <div v-for="(item, index) in dataList as any" :key="item.typeName" class="access-item">
        <div class="icon" :class="'icon-bg' + index"></div>
        <div class="right">
          <div class="item-title">{{ item.typeName }}</div>
          <div class="item-num">
            <span>{{ item.num }}</span>
            <span class="unit">{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import hiddenInstrumentPanel from './hiddenInstrumentPanel.vue'
const props = defineProps({
  hiddenInfo: {
    type: Object,
    default: () => ({}),
  },
  message: {
    type: Array,
    default: () => [],
  },
  dataList: {
    type: Array,
    default: () => [],
  },
})
console.log('hiddenInfo -----> 🚀', props.hiddenInfo)

const toState = (e) => {
  if (e == 1) {
    return '未发布'
  }
  if (e == 2) {
    return '进行中'
  }
  if (e == 3) {
    return '已结束'
  }
  if (e == 4) {
    return '已停用'
  }
}
</script>

<style lang="scss" scoped>
.hidden-danger {
  height: 90%;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  display: grid;
  grid-template-rows: 131.52px 1fr 1fr;
  .top {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    // background-color: rgba(255, 255, 255, 0.3);
    .top-item {
      width: 100%;
      height: 100%;
      display: grid;
      grid-template-columns: 110.016px 1fr;
      .left {
        .pie {
          width: 100px;
          height: 100px;
          transform: scale(0.7);
        }
        .title {
          margin-top: -21.12px;
          width: 100%;
          font-size: 14.016px;
          text-align: center;
        }
      }
      .right {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
        .right-item {
          width: 100%;
          display: flex;
          .picture {
            width: 44.928px;
            height: 43.968px;
            background: url('../../../../../assets/image/shujujiashicang/safetyManage/right-task.png') no-repeat
              center/cover;
          }
          .right-top {
            background: url('../../../../../assets/image/shujujiashicang/safetyManage/right-normal.png') no-repeat
              center/cover;
          }
          .great {
            background: url('../../../../../assets/image/shujujiashicang/safetyManage/right-hidden.png') no-repeat
              center/cover;
          }
          .content {
            .name {
              font-size: 12.096px;
            }
            .num {
              font-size: 18.048px;
              font-weight: bold;
              color: #a4e4ff;
            }
          }
        }
      }
    }
  }
  .middle {
    width: 100%;
    height: 100%;
  }
  .message-content {
    padding-top: 14.976px;
    border-width: 0 0 11.904px 0; /* 只给底部设置边框 */
    border-style: solid; /* 边框样式必须设置 */
    border-image-source: url('@/assets/image/shujujiashicang/safetyManage/hight-light.png'); /* 图片路径 */
    border-image-slice: 0 0 100% 0; /* 切割图片，仅用于底部 */
    border-image-repeat: stretch; /* 图片的重复模式，可以设置为 stretch, repeat, round */
    background: linear-gradient(90deg, transparent 0%, rgba(42, 156, 252, 0.2) 51%, transparent 100%);
    display: grid;
    .message-for {
      padding: 0 9.984px;
      display: flex;
      align-items: center;
      font-size: 14.016px;
      margin-bottom: 4.992px;
      .content {
        width: 47.04px;
        height: 17.088px;
        font-size: 9.984px;
        text-align: center;
        margin-right: 4px;
        background: url('@/assets/image/shujujiashicang/safetyManage/tips.png') no-repeat center/cover;
      }
      .time {
        justify-self: center;
        white-space: nowrap;
        overflow: hidden;
      }
    }
    .pic {
      height: 80.064px;
      border-radius: 6px;
      background: url('@/assets/image/shujujiashicang/safetyManage/temp-1.png') no-repeat center/cover;
    }
  }
  .bottom {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    justify-items: center;

    .access-item {
      display: flex;
      align-items: center;
      .right {
        .item-title {
          font-size: 14.016px;
        }
        .item-num {
          font-weight: bold;
          font-size: 24px;
          color: #a4e4ff;
          .unit {
            font-size: 14.016px;
            color: #fff;
          }
        }
      }
    }
    .icon {
      // 宽100 高80
      width: 63.936px;
      height: 63.936px;
      background: url('../../../../../assets/image//shujujiashicang/safetyManage/wind.png') no-repeat center/cover;
    }
    .icon-bg1 {
      background: url('../../../../../assets/image//shujujiashicang/safetyManage/check-list.png') no-repeat center/cover;
    }
    .icon-bg2 {
      background: url('../../../../../assets/image//shujujiashicang/safetyManage/hidden-list.png') no-repeat
        center/cover;
    }
  }
}
</style>
