<template>
  <div class="riskClass">
    <div class="left-part">
      <div v-for="item in dataList as any" :key="item.typeName" class="access-item">
        <div class="icon">
          <div class="rate">
            <span>{{ formattedRate(Number(item.rate)) }}</span>
            <span class="text-[13px]">%</span>
          </div>
          <div class="name">维护率</div>
        </div>
        <div class="right">
          <div class="item-title">{{ item.typeName }}</div>
          <div class="item-num">
            <span>{{ item.num }}</span>
            <span class="unit">{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="right-part">
      <Pie :optionData="pieData" />
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  dataList: {
    type: Array,
    default: () => [],
  },
  pieData: {
    type: Object,
    default: () => ({}),
  },
})

import { ref } from 'vue'
import Pie from './PieManage.vue'

const formattedRate = (e) => {
  if (Number.isInteger(e)) {
    return e // 如果e是整数，直接返回
  } else {
    return e.toFixed(2) // 否则，保留两位小数
  }
}
</script>

<style lang="scss" scoped>
.riskClass {
  height: 83%;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  display: grid;
  grid-template-columns: auto 1fr;
  .right-part {
    // background-color: #e77979;
  }
  .left-part {
    width: 149.952px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    .access-item {
      display: flex;
      justify-content: space-around;
      align-items: center;
      .right {
        .item-title {
          font-size: 14.016px;
        }
        .item-num {
          font-weight: bold;
          font-size: 24px;
          color: #a4e4ff;
          .unit {
            font-size: 14.016px;
            color: #fff;
          }
        }
      }
    }
    .icon {
      // 宽100 高80
      width: 63.936px;
      height: 63.936px;
      background: url('../../../../../assets/image//shujujiashicang/safetyManage/nice.png') no-repeat center/cover;
      display: flex;
      flex-direction: column;
      align-items: center;
      .name {
        font-size: 12.096px;
      }
      .rate {
        font-weight: bold;
        font-size: 24px;
        color: #ffffff;
      }
    }
  }
}
</style>
