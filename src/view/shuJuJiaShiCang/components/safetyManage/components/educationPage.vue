<template>
  <div class="educationPagg">
    <div class="left">
      <div class="complateRate">
        <educationPagePie :dataInfo="educationInfo" />
      </div>
    </div>
    <div class="right">
      <div class="right-top">
        <div v-for="item in educationInfo.list" :key="item.typeName" class="right-top-item">
          <div class="right-top-item-title">{{ item.typeName }}</div>
          <div class="right-top-item-content">{{ item.num }}</div>
        </div>
      </div>
      <div class="right-bottom">
        <div v-for="item in educationInfo.imgList" :key="item.typeName" class="exam-list">
          <div class="right-bottom-content">
            <!-- <img :src="item.imgUrl" alt="" /> -->
            <el-image
              class="block w-90px h-70px"
              style="width: 100%"
              :src="item.imgUrl"
              :preview-src-list="[item.imgUrl]"
              fit="cover"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
            >
              <template #error>
                <div class="image-slot w-full h-full flex justify-center items-center">
                  <img src="@/assets/image/no-data.png" alt="" />
                </div>
              </template>
            </el-image>
            <div class="state" v-if="item.state" :class="item.state === '已完成' ? 'complate' : ''">
              {{ item.state }}
            </div>
          </div>
          <div class="right-bottom-title">
            <myTooltip :str="item.typeName" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import educationPagePie from './educationPagePie.vue'
const props = defineProps({
  educationInfo: {
    type: Object,
    default: () => ({}),
  },
})
</script>

<style lang="scss" scoped>
.educationPagg {
  height: 85%;
  display: grid;
  grid-template-columns: 129.984px 1fr;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  .left {
    width: 126.144px;
    padding: 0 4.992px 0 15.36px;
    display: flex;
    align-items: center;
    .complateRate {
      width: 120px;
      height: 120px;
    }
  }
  .right {
    margin: 9.984px 0 0;
    width: 100%;
    height: calc(100% - 9.984px);
    display: grid;
    grid-template-rows: repeat(2, 1fr);
    .right-top {
      display: flex;
      justify-content: space-around;
      .right-top-item {
        font-size: 14.016px;
      }
      .right-top-item-content {
        width: 105.024px;
        height: 77.952px;
        background: url(@/assets/image/shujujiashicang/safetyManage/task-bg.png) no-repeat center/cover;
        font-weight: bold;
        font-size: 24px;
        color: #ffffff;
        text-align: center;
      }
      .right-top-item-title {
        text-align: center;
        font-size: 14.016px;
      }
    }
    .right-bottom {
      font-size: 12.096px;
      display: flex;
      justify-content: space-around;
      .exam-list {
        width: 103.872px;
        height: 86.208px;
        // display: flex;
        // flex-direction: column;
        // align-items: center;
        display: grid;
        grid-template-rows: 1fr 23.04px;

        .right-bottom-content {
          position: relative;
          width: 100%;
          height: 100%;
          // background: url('../../../../../assets/image/shujujiashicang/safetyManage/temp.png') no-repeat center/cover;
          img {
            width: 100%;
            height: 100%;
          }
          .state {
            position: absolute;
            font-size: 9.984px;
            top: 4.992px;
            right: 0;
            width: 38.016px;
            height: 17.088px;
            text-align: center;
            background-color: #dd9c37;
          }
          .complate {
            background-color: #30e56f;
          }
        }
        .right-bottom-title {
          justify-self: center;
          width: 100%;
          height: 23.04px;
          // 溢出...表示
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
      }
    }
  }
}
</style>
