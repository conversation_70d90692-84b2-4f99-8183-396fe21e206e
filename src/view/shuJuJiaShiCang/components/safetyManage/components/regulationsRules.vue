<template>
  <div class="rules-unite">
    <div class="top">
      <div v-for="(item, index) in dataList as any" :key="item.typeName" class="access-item">
        <div class="icon" :class="'icon-bg' + index"></div>
        <div class="right">
          <div class="item-title">{{ item.typeName }}</div>
          <div class="item-num">
            <span>{{ item.num }}</span>
            <span class="unit">{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="message-content" v-if="message.length > 0">
      <div v-for="item in message as any" :key="item.title" class="message-for">
        <div class="content">
          <myTooltip :str="item.typeName" />
        </div>
        <div class="title">
          <myTooltip :str="item.title" />
        </div>
        <div class="time">
          <myTooltip :str="item.time" />
        </div>
        <div class="department">
          <myTooltip :str="item.department" />
        </div>
      </div>
    </div>
    <div v-else class="w-full h-full flex justify-center items-center">
      <img class="w-160px h-120px" src="@/assets/image/no-data.png" alt="" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const props = defineProps({
  dataList: {
    type: Array,
    default: () => [],
  },
  message: {
    type: Array,
    default: () => [],
  },
})

defineOptions({ name: 'regulationsRules' })
</script>

<style lang="scss" scoped>
.rules-unite {
  height: 85%;
  display: grid;
  grid-template-rows: repeat(2, 1fr);
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  .top {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    justify-items: center;

    .access-item {
      display: flex;
      align-items: center;
      .right {
        .item-title {
          font-size: 14.016px;
        }
        .item-num {
          font-weight: bold;
          font-size: 24px;
          color: #a4e4ff;
          .unit {
            font-size: 14.016px;
            color: #fff;
          }
        }
      }
    }
    .icon {
      // 宽100 高80
      width: 63.936px;
      height: 63.936px;
      background: url('../../../../../assets/image//shujujiashicang/safetyManage/regulations.png') no-repeat
        center/cover;
    }
    .icon-bg1 {
      background: url('../../../../../assets/image//shujujiashicang/safetyManage/rules.png') no-repeat center/cover;
    }
    .icon-bg2 {
      background: url('../../../../../assets/image//shujujiashicang/safetyManage//Controls.png') no-repeat center/cover;
    }
  }
  .message-content {
    padding-top: 14.976px;
    border-width: 0 0 11.904px 0; /* 只给底部设置边框 */
    border-style: solid; /* 边框样式必须设置 */
    border-image-source: url('@/assets/image/shujujiashicang/safetyManage/hight-light.png'); /* 图片路径 */
    border-image-slice: 0 0 100% 0; /* 切割图片，仅用于底部 */
    border-image-repeat: stretch; /* 图片的重复模式，可以设置为 stretch, repeat, round */
    background: linear-gradient(90deg, transparent 0%, rgba(42, 156, 252, 0.2) 51%, transparent 100%);
    .message-for {
      padding: 0 9.984px;
      display: grid;
      grid-template-columns: 1fr 3fr 3fr 2fr;
      font-size: 14.016px;
      margin-bottom: 4.992px;
      align-items: center;
      .content {
        width: 47.04px;
        height: 17.088px;
        font-size: 9.984px;
        text-align: center;
        background: url('@/assets/image/shujujiashicang/safetyManage/tips.png') no-repeat center/cover;
      }
      .title {
        // 溢出...展示
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        font-size: 14.016px;
        margin-left: 5px;
      }
      .time {
        justify-self: center;
      }
    }
  }
}
</style>
