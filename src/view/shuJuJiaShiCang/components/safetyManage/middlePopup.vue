<template>
  <div class="middle-content">
    <div class="top-part">
      <div class="search">
        <!-- <input v-model="input" class="input-content" style="width: 100%" placeholder="请输入单位名称" /> -->
      </div>
      <div class="title">
        <div class="name">
          <span class="tip">外运长航安全生产天数</span>
          <span class="date">（{{ startTime }}）</span>
        </div>
        <div class="days">
          <div v-for="(num, idx) in totalDaysArr" :key="idx" class="single">{{ num }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'

const userInfo = useUserInfo()
const totalDays = ref('')
const startTime = ref('')
const input = ref('')

const getDay = () => {
  $API
    .get({
      url: '/ehs-clnt-platform-service/workbench/msg/getSafeDays',
      params: {
        unitId: userInfo.value.unitId,
      },
    })
    .then((res: any) => {
      startTime.value = res.data.startTime
      let days = String(res.data.days || 0)
      const targetLength = 6
      while (days.length < targetLength) {
        days = '0' + days
      }
      totalDays.value = days
    })
    .catch((error) => {
      console.error('Error fetching safe days:', error)
    })
}
const totalDaysArr = computed(() => {
  return totalDays.value.split('')
})

onMounted(() => {
  getDay()
})
</script>

<style lang="scss" scoped>
.middle-content {
  // pointer-events: none;
  padding: 0 19.968px;
  .top-part {
    color: #fff;
    height: 149.952px;
    display: grid;
    grid-template-columns: 280px 1fr;
    column-gap: 19.968px;
    .title {
      // background-color: #baafaf;
      .days {
        display: flex;
        justify-content: start;
        .single {
          margin-left: 33.408px;
          font-family: 'DS-Digital-shu';
          font-weight: bold;
          font-size: 72px;
          // text-shadow: 0px 2px 13px #1e5ed4;
          width: 48.96px;
          height: 70.08px;
          line-height: 70.08px;
          background: rgba(24, 64, 107, 0.8);
          border-radius: 4px;
          border: 1px solid #00b4ff;
          text-align: center;
          &:first-child {
            margin-left: 0;
          }
        }
      }
      .name {
        .date {
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 14.016px;
          color: #08a5ff;
          text-shadow: 0px 2px 8px rgba(5, 28, 55, 0.42);
          background: linear-gradient(0deg, #0ec5ec 0%, #31beff 0%, #effcfe 58.7646484375%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .tip {
          font-weight: 600;
          font-size: 26px;
          font-style: initial;
          font-family: DOUYU;
          background: linear-gradient(0deg, #0ec5ec 0%, #31beff 0%, #effcfe 68%);
          //文字渐变色
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          // 文字毛玻璃效果
        }
      }
      .num {
        font-family: 'DS-Digital';
        font-size: 30px;
        font-style: initial;
      }
    }
    .search {
      margin-top: 19.968px;
    }
  }
  :deep(.el-input__wrapper) {
    background-color: transparent !important;
    border: #0921da 1px solid !important;
    .el-input__inner::placeholder {
      color: #3686d8;
    }
  }
}
.input-content {
  background-color: transparent !important;
  border: #3686d8 1px solid !important;
  border-radius: 3px;
  padding: 3px 10px 5px;
  &::placeholder {
    color: #3686d8;
    font-size: 12px;
    margin-left: 10px;
  }
}
</style>
