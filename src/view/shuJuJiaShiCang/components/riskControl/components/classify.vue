<template>
  <div class="line-part w-full h-full" ref="pieChart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
import { isFontSizeRem } from '@/common/utils'
const props = defineProps<{
  optionData: any
}>()
let total = ref(300)
watch(
  () => props.optionData,
  () => {
    total.value = 0
    props.optionData.forEach((item: any) => {
      total.value += item.value
    })
    initEcharts()
  }
)
defineComponent({ name: 'pieChart' })

const pieChart = ref()
let myChart: any
function formatter(params) {
  const seriesName = params.seriesName // 系列名称
  let dataName = params.name // 数据项名称
  const value = params.value // 数据值
  let tooltipHtml

  // 检查数据项名称（b的文案）的长度
  if (dataName.length > 15) {
    // 如果超过10个字，则在第10个字后换行
    dataName = dataName.slice(0, 15) + '<br/>' + dataName.slice(15)
  }

  // 构建tooltip的HTML内容
  tooltipHtml = dataName + '</div>'
  tooltipHtml += '<span>' + ': ' + value + '</span>'

  return tooltipHtml
}
function initEcharts() {
  if (myChart) myChart.dispose()

  // 标记一个对象，使其永远不会再成为响应式对象
  myChart = echarts.init(pieChart.value)

  // 传入数据生成 option
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: formatter,
      confine: true,
      show: true,
    },
    legend: {
      show: true,
      type: 'scroll',
      icon: 'circle',
      top: 'center',
      right: '-2%',
      // 竖向排列
      orient: 'vertical',
      textStyle: {
        color: '#fff',
        fontSize: isFontSizeRem(11),
      },
      // itemGap: 5,
      // itemHeight: 11,
      pageIconColor: '#ff781f', // 设置翻页箭头颜色
      pageTextStyle: {
        color: '#999', // 设置翻页数字颜色
      },
      pageIconSize: isFontSizeRem(12), // 设置翻页箭头大小
      formatter: (name: string) => {
        const currentNum = props.optionData.find((item: any) => item.name === name)?.value
        if (!currentNum) return name
        return `${name.length > 6 ? name.slice(0, 6) + '...' : name}  ${currentNum}  ${((+(currentNum / total.value).toFixed(4) * 10000) / 100).toFixed(2)}%`
      },
      tooltip: {
        show: true,
      },
    },
    series: [
      {
        type: 'pie',
        // padAngle: 3,
        radius: ['30%', '45%'],
        center: ['6%', '50%'],
        left: '13%',
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
          emphasis: {
            show: false, // 突出显示时不显示标签
          },
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 11,
            color: '#fff',
            formatter: `{c}\n\n{b}`,
          },
        },
        labelLine: {
          show: false,
        },
        data: props.optionData,
      },
    ],
  }

  myChart.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart) myChart.resize()
  })
}

onMounted(() => {
  initEcharts()
})

// 把initEcharts暴露出去
defineExpose({
  initEcharts,
})
</script>

<style scoped></style>
