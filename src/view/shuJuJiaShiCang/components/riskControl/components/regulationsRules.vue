<template>
  <div class="rules-unite">
    <div class="top h-100px flex justify-around items-center">
      <div
        v-for="(item, index) in dataList as any"
        :key="item.typeName"
        class="access-item"
        :class="index == 2 ? 'icon-bg2' : ''"
      >
        <div class="icon" :class="index != 2 ? 'icon-bg' + index : ''"></div>
        <div class="right">
          <div class="item-num">
            <span>{{ item.num }}</span>
            <span class="unit">{{ item.unit }}</span>
          </div>
          <div class="item-title">{{ item.typeName }}</div>
        </div>
      </div>
    </div>
    <div class="message-content h-330px overflow-y-auto">
      <div v-for="item in message as any" :key="item.title" class="message-for flex">
        <div class="left cursor-pointer" @click="goDetail(item)">
          <div class="flex justify-between items-center">
            <div class="type w-100px"><myTooltip :str="item.type" /></div>
            <div class="title w-180px text-16px"><myTooltip :str="item.title" /></div>
            <div
              class="statusName flex justify-center items-center"
              :style="{ backgroundImage: `url(${item.status == 1 ? end_bg : ing_bg})` }"
            >
              {{ item.statusName }}
            </div>
          </div>
          <div class="other">计划周期：{{ item.planCycle }}</div>
          <div class="other">检查对象：{{ item.checkObj }}</div>
        </div>
        <el-image
          class="block w-92px h-92px"
          :preview-src-list="[item.r_img]"
          :src="item.r_img"
          fit="cover"
          :zoom-rate="1.2"
          :max-scale="7"
          :min-scale="0.2"
        >
          <template #error>
            <div class="image-slot w-full h-full flex justify-center items-center">
              <img src="@/assets/image/no-data.png" alt="" />
            </div>
          </template>
        </el-image>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import end_bg from './end_bg.png'
import ing_bg from './ing_bg.png'
import { useUserInfo } from '~/store'
import $API from '~/common/api'

const userInfo = useUserInfo()

const props = defineProps({
  dataList: {
    type: Array,
    default: () => [],
  },
  message: {
    type: Array,
    default: () => [],
  },
})

async function goDetail(e) {
  try {
    const res = await $API.post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: 'hazard_inves',
        userId: userInfo.value.id,
      },
    })
    const url = `${window.location.origin}/ehs-hazard-web/#/inspection-planning/planned-management/details/${e.planId}?token=${res.data.token}&sysCode=hazard_inves`
    window.open(url, '_blank')
  } catch (error) {}
}

defineOptions({ name: 'regulationsRules' })
</script>

<style lang="scss" scoped>
.rules-unite {
  height: calc(100% - 9.6px);
  // background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  .top {
    justify-items: center;
    align-items: center;

    .access-item {
      display: flex;
      align-items: center;
      .right {
        margin-left: 7.0003px;
        .item-title {
          font-size: 14.0006px;
        }
        .item-num {
          font-weight: 600;
          font-size: 24px;
          color: #a4e4ff;
          .unit {
            font-size: 14.016px;
            color: #fff;
          }
        }
      }
    }
    .icon-bg0 {
      width: 58.9997px;
      height: 54px;
      background: url('../../../../../assets/image//shujujiashicang/safetyManage/InspectPlan.png') no-repeat
        center/cover;
    }
    .icon-bg1 {
      width: 58.9997px;
      height: 54px;
      background: url('../../../../../assets/image//shujujiashicang/safetyManage/InspectTask.png') no-repeat
        center/cover;
    }
    .icon-bg2 {
      position: relative;
      width: 131.8406px;
      height: 55.92px;
      padding-left: 48.24px;
      background: url('../../../../../assets/image//shujujiashicang/safetyManage//percentageComplete.png') no-repeat
        center/cover;
      .item-title {
        position: relative;
        top: -4.9997px;
        font-size: 14.0006px;
      }
      .item-num {
        font-size: 20.0006px !important ;
      }
    }
  }
  .message-content {
    .message-for {
      padding: 0 9.984px;
      font-size: 14.016px;
      margin-bottom: 4.992px;
      padding: 3.84px;
      background: linear-gradient(0deg, #152956 0%, #2c559c 100%);
      border: 1px solid #2c51a4;
      .type {
        background: rgba(110, 88, 2, 0.21);
        border-radius: 2px;
        border: 1px solid;
        border-image: linear-gradient(0deg, #cda400, #fff0b2) 10 10;
        color: #eabc06;
        padding: 3.0797px 4.08px;
        border-radius: 2px;
        margin-right: 8.832px;
      }
      .other {
        color: #dee6f0;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
      .statusName {
        margin-left: 4.992px;
        width: 57.9994px;
        height: 21.9994px;
        font-size: 14.0006px;
        background: url('./end_bg.png') no-repeat center/cover;
        margin-right: 8.832px;
      }
      .img {
        width: 92.0006px;
        height: 100%;
        border-radius: 2px;
      }
      .content {
        width: 47.04px;
        height: 17.088px;
        font-size: 9.984px;
        background: url('@/assets/image/shujujiashicang/safetyManage/tips.png') no-repeat center/cover;
      }

      .time {
        justify-self: center;
      }
    }
  }
}

/* 隐藏默认滚动条 */
::-webkit-scrollbar {
  width: 3px;
}

/* 定义滚动条轨道样式 */
::-webkit-scrollbar-track {
  background-color: #182338;
}

/* 定义滚动条滑块样式 */
::-webkit-scrollbar-thumb {
  background-color: #194287;
}

/* 鼠标悬停在滚动条上时的样式 */
::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}
</style>
