<template>
  <div class="riskClass">
    <div class="tab flex justify-between items-center">
      <div class="taba">异常{{ systemOperationCount?.leaveUnitNum }}家</div>
      <div class="tabb">正常{{ systemOperationCount?.onUnitNum }}家</div>
    </div>
    <template v-if="waterOpts.length > 0">
      <div v-for="(item, index) in waterOpts" :key="index" class="tab-item flex justify-start items-center">
        <div :class="['left', getStatusClass(item.statusName)]">
          <div class="numberVal">
            <div>{{ item.value || 0 }}</div>
            <div>{{ item.mpa }}</div>
          </div>
        </div>
        <div class="right cursor-pointer" @click="goDetail(item)">
          <div class="flex justify-between">
            <div>{{ item.projectName }}</div>
            <div class="warning flex-end">{{ item.statusName }}</div>
          </div>
          <div style="color: #dee6f0; font-size: 14.0006px">持续时长{{ item.continueTime }}</div>
          <div style="color: #dee6f0; font-size: 14.0006px">{{ item.createTime }}</div>
        </div>
      </div>
    </template>
    <div v-else class="w-full h-full flex justify-center items-center">
      <img src="@/assets/image/no-data.png" alt="" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
// import GaugePie from './gaugePie.vue'
import GaugePie from './ybp.vue'
import config from '~/config'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
const userInfo = useUserInfo()
const props = defineProps<{
  waterOpts: {
    projectName: string
    status: number
    statusName: string
    continueTime: string
    createTime: string
    value: number
    mpa: string
  }[]
  systemOperationCount: any
}>()
async function goDetail(e) {
  const res = await $API.post({
    url: `ehs-clnt-platform-service/login/checkSysPower`,
    data: {
      sysCode: 'iot_monitoring',
      userId: userInfo.value.id,
    },
  })
  const url = `${config.base_prefix}/ehs-internet-monitor/#/index?token=${res.data.token}&sysCode=iot_monitoring&eventType=4&eventId=${e.eventId}&page=/monitor/realTimeMonitorPage`
  window.open(url, '_blank')
}

function getStatusClass(statusName: string): string {
  if (statusName === '水位上限报警') {
    return 'left1'
  } else if (statusName === '水位下限报警') {
    return 'left2'
  }
  return '' // 默认情况返回空字符串，或者可以根据需要返回其他类名
}
</script>

<style lang="scss" scoped>
.left-common {
  display: flex;
  justify-content: center;
  margin-right: 10px;
  width: 120px;
  height: 80px;
  .numberVal {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 11px;
    margin-top: 10px;
  }
}
.tab {
  background: rgba(25, 42, 75, 0.75);
  border: 1px solid #405486;
  padding: 3.9994px;
  font-size: 14.0006px;
  color: #fff;
  cursor: pointer;

  .taba {
    width: 40%;
    background: linear-gradient(0deg, #8a1515 0%, #dc3838 100%);
    border: 1px solid #e53f45;
    text-align: center;
    border-right: none;
  }
  .tabb {
    width: 60%;
    background: linear-gradient(0deg, #00534a 0%, #009a8a 100%);
    border: 1px solid #029b8b;
    text-align: center;
    border-left: none;
  }
}
.tab-item {
  font-size: 15.9994px;
  padding: 9.001px 15.9994px;
  background: linear-gradient(0deg, #152956 0%, #2c559c 100%);
  border: 1px solid #2c51a4;
  margin: 8.0006px 0;

  .left {
    @extend .left-common;
    background: url(@/assets/image/shujujiashicang/safetyManage/mpg.png) no-repeat center;
    background-size: 100% 100%;
  }

  .left1 {
    @extend .left-common;
    background: url(@/assets/image/shujujiashicang/safetyManage/high.png) no-repeat center;
    background-size: 100% 100%;
  }

  .left2 {
    @extend .left-common;
    background: url(@/assets/image/shujujiashicang/safetyManage/lowest.png) no-repeat center;
    background-size: 100% 100%;
  }

  .right {
    width: 100%;

    .warning {
      background: url(./warning.png) no-repeat center;
      background-size: 100% 100%;
      padding: 0 8px;
      width: 130px;
      height: 24px;
      text-align: center;
    }
  }
}
.riskClass {
  height: calc(100% - 101.76px);
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  overflow-y: scroll;
}

/* 隐藏默认滚动条 */
::-webkit-scrollbar {
  width: 3px;
}

/* 定义滚动条轨道样式 */
::-webkit-scrollbar-track {
  background-color: #182338;
}

/* 定义滚动条滑块样式 */
::-webkit-scrollbar-thumb {
  background-color: #194287;
}

/* 鼠标悬停在滚动条上时的样式 */
::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}
</style>
