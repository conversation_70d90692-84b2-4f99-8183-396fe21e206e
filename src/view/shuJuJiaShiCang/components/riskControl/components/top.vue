<template>
  <div class="riskClass">
    <div class="message-content">
      <div
        v-for="(item, index) in topOpts as any"
        :key="item.title"
        class="flex justify-between align-center message-for"
      >
        <div class="flex justify-start align-center">
          <div style="position: relative">
            <img :src="getTop(index)" class="img" alt="" />
            <span
              style="position: absolute; top: 1.92px; left: 5.76px; color: #fff"
              :style="{ color: index < 3 ? '#fff' : '#7C9BC6' }"
              >{{ index + 1 }}</span
            >
          </div>
          <div class="title">{{ item.hazardTypeName }}</div>
        </div>
        <div class="other">{{ item.recordCount }}次</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import top1 from './top1.png'
import top2 from './top2.png'
import top3 from './top3.png'
import top4 from './top4.png'

const props = defineProps({
  topOpts: {
    type: Array,
    default: () => [],
  },
})

function getTop(index: number) {
  let tops = [top1, top2, top3, top4]
  return index < 3 ? tops[index] : tops[3]
}
</script>

<style lang="scss" scoped>
.riskClass {
  height: calc(100% - 46.08px);
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  padding: 19.2px 9.6px;
  color: #fff;
  width: 100%;
}
.message-content {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  .message-for {
    width: 100%;
    font-size: 14.016px;
    background: linear-gradient(90deg, #3c5b9a 52%);
    border-radius: 4px;
    padding: 7.872px 0;
    margin-bottom: 6px;
    .other {
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 14.0006px;
      color: #dee6f0;
    }
    .title {
      // 溢出...展示
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      font-family: D-DIN-PRO;
      font-weight: 400;
      color: #ffffff;
      line-height: 30px;
      font-size: 15.9994px;
      max-width: 268.8px;
    }
    .img {
      width: 20.0006px;
      height: 26.0006px;
      margin-right: 8.832px;
    }

    .time {
      justify-self: center;
    }
  }
}

/* 隐藏默认滚动条 */
::-webkit-scrollbar {
  width: 3px;
}

/* 定义滚动条轨道样式 */
::-webkit-scrollbar-track {
  background-color: #182338;
}

/* 定义滚动条滑块样式 */
::-webkit-scrollbar-thumb {
  background-color: #194287;
}

/* 鼠标悬停在滚动条上时的样式 */
::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}
</style>
