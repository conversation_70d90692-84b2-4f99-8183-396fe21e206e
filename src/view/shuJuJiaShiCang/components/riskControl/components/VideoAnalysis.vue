<template>
  <div class="riskClass">
    <div class="tab flex justify-between items-center">
      <div class="taba">离岗{{ leaveRecordStatisticsList?.leaveUnitNum }}家</div>
      <div class="tabb">在岗{{ leaveRecordStatisticsList?.onUnitNum }}家</div>
    </div>
    <template v-if="guardOpts.length > 0">
      <div v-for="(item, index) in guardOpts" :key="index" class="tab-item flex justify-start items-center">
        <div class="left">
          <el-image
            class="block w-92px h-92px"
            :preview-src-list="[item.img]"
            :src="item.img"
            fit="cover"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
          >
            <template #error>
              <div class="image-slot w-full h-full flex justify-center items-center">
                <img src="@/assets/image/no-data.png" alt="" />
              </div>
            </template>
          </el-image>
          <div class="status" :class="{ goback: item.status == 1 }">{{ item.statusName }}</div>
        </div>
        <div class="right cursor-pointer" @click="goDetail(item)">
          <div class="flex justify-start items-center">
            <div>{{ item.projectName }}</div>
          </div>
          <div style="color: #dee6f0; font-size: 14.0006px">离岗时长{{ item.leaveTime }}</div>
          <div style="color: #dee6f0; font-size: 14.0006px">离岗时间：{{ item.createTime }}</div>
        </div>
      </div>
    </template>
    <div v-else class="w-full h-full flex justify-center items-center">
      <img src="@/assets/image/no-data.png" alt="" />
    </div>
  </div>
</template>

<script setup lang="ts">
import config from '~/config'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
const userInfo = useUserInfo()
const props = defineProps<{
  guardOpts: {
    projectName: string
    status: number
    statusName: string
    leaveTime: string
    createTime: string
    img: string
  }[]
  leaveRecordStatisticsList: any
}>()

async function goDetail(e) {
  const res = await $API.post({
    url: `ehs-clnt-platform-service/login/checkSysPower`,
    data: {
      sysCode: 'fcr_manage',
      userId: userInfo.value.id,
    },
  })
  const url = `${config.base_prefix}/ehs-controlroommanagement/#/index?token=${res.data.token}&sysCode=fcr_manage&page=/departure-record/index`
  window.open(url, '_blank')
}
</script>

<style lang="scss" scoped>
.tab {
  background: rgba(25, 42, 75, 0.75);
  border: 1px solid #405486;
  padding: 3.9994px;
  font-size: 14.0006px;
  color: #fff;
  cursor: pointer;

  .taba {
    width: 40%;
    background: #505b79;
    border: 1px solid #505b79;
    text-align: center;
    border-right: none;
  }
  .tabb {
    width: 60%;
    background: #017165;
    border: 1px solid #017165;
    text-align: center;
    border-left: none;
  }
}
.tab-item {
  font-size: 15.9994px;
  padding: 9.001px 15.9994px;
  background: linear-gradient(0deg, #152956 0%, #2c559c 100%);
  border: 1px solid #2c51a4;
  margin: 8.0006px 0;
  .left {
    position: relative;
    .image-slot {
      position: absolute;
      top: 20px;
    }

    img {
      width: 94.201px;
      height: 63.1603px;
      border-radius: 18.4397px;
    }
    .status {
      position: absolute;
      top: 0;
      left: 0;
      background: #6c7b9e;
      color: #fff;
      padding: 3.001px 7.0003px;
      border-radius: 3.001px;
      border-top-left-radius: 0;
    }
    .goback {
      background: #016d61;
    }
  }

  .right {
    margin-left: 18.5203px;
    .warning {
      background: url(./warning.png) no-repeat center;
      background-size: 100% 100%;
      padding: 0 9.001px;
    }
  }
}
.riskClass {
  height: calc(100% - 101.76px);
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  overflow-y: scroll;
}

/* 隐藏默认滚动条 */
::-webkit-scrollbar {
  width: 3px;
}

/* 定义滚动条轨道样式 */
::-webkit-scrollbar-track {
  background-color: #182338;
}

/* 定义滚动条滑块样式 */
::-webkit-scrollbar-thumb {
  background-color: #194287;
}

/* 鼠标悬停在滚动条上时的样式 */
::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}
</style>
