<template>
  <div class="line-part w-full h-full" ref="pieChart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
const props = defineProps<{
  value: number
}>()
watch(
  () => props.value,
  () => {
    initEcharts()
  }
)
defineComponent({ name: 'pieChart' })

const pieChart = ref()
let myChart: any

function initEcharts() {
  if (myChart) myChart.dispose()

  // 标记一个对象，使其永远不会再成为响应式对象
  myChart = echarts.init(pieChart.value)

  // 传入数据生成 option
  const option = {
    tooltip: {
      formatter: '{a} <br/>{b} : {c}',
    },
    series: [
      {
        name: '刻度',
        type: 'gauge',
        radius: '70%',
        min: 0,
        max: 100,
        startAngle: 220,
        endAngle: -40,
        z: 10,
        splitNumber: 4,
        axisLine: {
          show: true,
          lineStyle: {
            width: 1,
            color: [[1, 'rgba(0,255,0,1)']],
          },
        }, //仪表盘轴线

        axisLabel: {
          show: true,
          color: '#f0f',
          distance: 30,
        }, //刻度标签。
        axisTick: {
          show: true,
          splitNumber: 15,
          lineStyle: {
            color: '#f0f',
            width: 1,
          },
          length: 8,
        }, //刻度样式
        splitLine: {
          show: true,
          length: 20,
          lineStyle: {
            color: '#f0f',
          },
        }, //分隔线样式
        detail: {
          show: false,
        },
        pointer: {
          show: false,
        },
      },
      {
        name: '业务指标',
        type: 'gauge',
        startAngle: 220,
        endAngle: -40,
        radius: '95%',

        axisLine: {
          //仪表盘轴线样式
          lineStyle: {
            width: 20,
            color: [
              [0.2, '#E31415'],
              [0.4, '#0E90FF'],
              [0.6, '#03B865'],
              [0.8, '#E31415'],
              [1, '#0E90FF'],
            ],
            opacity: 0.4, //盘的颜色变成透明
          },
        },
        axisLabel: { show: false },
        splitLine: {
          //分割线样式,是大分割线
          show: false,
          length: 15,
          width: 1,
        },
        // 刻度线
        axisTick: {
          show: false,
          length: 20,
          splitNumber: 5, // 每个分割线内的刻度线分为5份
          lineStyle: {
            //color: "auto",
            width: 3,
          },
        },
        data: [{ value: 65 }],

        markPoint: {
          symbol: 'emptyCircle',
          symbolSize: 25,
          data: [
            //跟你的仪表盘的中心位置对应上，颜色可以和画板底色一样
            { value: '80\nMfg', x: 'center', y: 'center', itemStyle: { color: '#5686FF' } },
          ],
          itemStyle: {
            normal: {
              label: {
                show: true,
                color: '#0f0', //气泡中字体颜色
                fontSize: 16,
              },
            },
          },
        },

        detail: {
          // 仪表盘详情，用于显示数据。(仪表盘的数值字体样式和显示位置)
          show: false, // 是否显示详情,默认 true。
          // offsetCenter: [0,0],// 相对于仪表盘中心的偏移位置，数组第一项是水平方向的偏移，第二项是垂直方向的偏移。可以是绝对的数值，也可以是相对于仪表盘半径的百分比。
          color: '#00f', // 文字的颜色,默认 auto。
          fontSize: 15, // 文字的字体大小,默认 15。
          formatter: 'Mp', // 格式化函数或者字符串
        },
        pointer: {
          // 仪表盘指针。
          show: true, // 是否显示指针,默认 true。
          length: '70%', // 指针长度，可以是绝对数值，也可以是相对于半径的百分比,默认 80%。
          width: 8, // 指针宽度,默认 8。
        },
        itemStyle: {
          // 仪表盘指针样式。
          color: '#5686FF', // 指针颜色，默认(auto)取数值所在的区间的颜色
          opacity: 1, // 图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。
          borderWidth: 0, // 描边线宽,默认 0。为 0 时无描边。
          borderType: 'solid', // 柱条的描边类型，默认为实线，支持 'solid', 'dashed', 'dotted'。
          borderColor: '#000', // 图形的描边颜色,默认 "#000"。支持的颜色格式同 color，不支持回调函数。
          shadowBlur: 10, // (发光效果)图形阴影的模糊大小。该属性配合 shadowColor,shadowOffsetX, shadowOffsetY 一起设置图形的阴影效果。
          shadowColor: '#fff', // 阴影颜色。支持的格式同color。
        },
      },
    ],
  }
  // 随机数据

  myChart.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart) myChart.resize()
  })
}

onMounted(() => {
  initEcharts()
})

// 把initEcharts暴露出去
defineExpose({
  initEcharts,
})
</script>

<style scoped>
.title-num {
  position: absolute;
  bottom: 10%;
  left: 18%;
  z-index: 500;
  background: linear-gradient(to right, #061d31, #227eb6 60%, #227eb6 60%, #051525);

  .a {
    color: #fff;
    font-size: 14px;
    font-weight: 700;
    margin-right: 10px;
  }
  .b {
    color: #fff;
    font-size: 18px;
    font-weight: 400;
  }
}
</style>
