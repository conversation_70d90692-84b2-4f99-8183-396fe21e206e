<template>
  <div class="line-part w-full h-full" ref="line1Chart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { isFontSizeRem } from '@/common/utils'
const props = defineProps({
  optionData: {
    type: Object,
    default: () => ({}),
  },
})

watch(
  () => props.optionData,
  (val) => {
    initEcharts()
  }
)
defineComponent({ name: 'publicPuline1Chart' })
const line1Chart = ref()
let arrName: string[] = []
let arrValue: number[] = []
let sum = 0
let pieSeries: any[] = [],
  lineYAxis: any[] = []
const colorObj = ['#FF3E6C', '#CCCC00', '#00CC03', '#0091FF', '#009494', '#e23f1b']

let myChart: any
let option = {}
function initEcharts() {
  pieSeries = []
  if (myChart) myChart.dispose()
  // 数据处理
  arrName.length = 0
  arrValue.length = 0
  props.optionData.forEach((v: any, i: any) => {
    arrName.push(v.name)
    arrValue.push(v.value)
    sum = sum + v.value
  })
  pieSeries.length = 0
  lineYAxis.length = 0
  props.optionData.forEach((v: any, i: any) => {
    props.optionData.forEach((i: any) => {
      i.num = i.value
      i.reat = ((i.value || 0) / (sum || 1)).toFixed(2)
    })
    pieSeries.push({
      name: '隐患等级',
      type: 'pie',
      clockWise: false,
      hoverAnimation: false,
      radius: [65 - i * 15 + '%', 57 - i * 15 + '%'],
      center: ['35%', '50%'],
      label: {
        show: false,
      },
      data: [
        {
          value: v.reat * 0.75,
          num: v.value,
          isShow: true,
          name: v.name,
        },
        {
          value: 1 - v.reat * 0.75,
          name: '',
          itemStyle: {
            color: 'rgba(0,0,0,0)',
          },
        },
      ],
    })
    pieSeries.push({
      name: '',
      type: 'pie',
      silent: true,
      z: 1,
      clockWise: false, //顺时加载
      hoverAnimation: false, //鼠标移入变大
      radius: [65 - i * 15 + '%', 57 - i * 15 + '%'],
      center: ['35%', '50%'],
      label: {
        show: false,
      },
      data: [
        {
          value: 7.5,
          itemStyle: {
            color: '#E3F0FF',
          },
        },
        {
          value: 2.5,
          name: '',
          itemStyle: {
            color: 'rgba(0,0,0,0)',
          },
        },
      ],
    })
    v.percent = (((v.value || 0) / (sum || 1)) * 100).toFixed(1) + '%'
    lineYAxis.push({
      value: i,
      textStyle: {
        rich: {
          circle: {
            color: colorObj[i],
            padding: [0, 5],
          },
        },
      },
    })
  })
  myChart = echarts.init(line1Chart.value)
  option = {
    color: colorObj,
    grid: {
      top: '15%',
      bottom: '54%',
      left: '30%',
      containLabel: false,
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          formatter: function (params: any) {
            let item = props.optionData[params]
            return (
              '{line|}{circle|●}' +
              '{rate|' +
              (((item.value || 0) / (sum || 1)) * 100).toFixed(2) +
              '%}' +
              '{name|' +
              item.name +
              '}'
            )
          },
          interval: 0,
          inside: true,
          textStyle: {
            color: '#eee',
            fontSize: isFontSizeRem(12),
            rich: {
              name: {
                color: '#eee',
                fontSize: isFontSizeRem(10),
              },
              rate: {
                fontSize: isFontSizeRem(10),
                width: 40,
              },
              bd: {
                color: '#ccc',
                padding: [0, 5],
                fontSize: isFontSizeRem(12),
              },
              percent: {
                color: '#333',
                fontSize: isFontSizeRem(12),
              },
              value: {
                color: '#333',
                fontSize: isFontSizeRem(14),
                fontWeight: 500,
                padding: [0, 0, 0, 20],
              },
              unit: {
                fontSize: isFontSizeRem(14),
              },
            },
          },
          show: true,
        },
        data: lineYAxis,
      },
    ],
    xAxis: [
      {
        show: false,
      },
    ],
    series: pieSeries,
    tooltip: {
      trigger: 'item',
      formatter: (prams: any) => {
        console.log(prams)
        const str = `<div> ${prams.marker}&nbsp ${prams.name} &nbsp  ${prams.data.num}  &nbsp ${prams.percent}%</div>`
        if (prams.data.isShow) return str
        return false
      },
      position: function (point: any) {
        return point
      },
    },
  }
  myChart.setOption(option)
  window.addEventListener('resize', () => {
    if (myChart) myChart.resize()
  })
}

onMounted(() => {
  initEcharts()
})
defineExpose({
  initEcharts,
})
</script>

<style scoped></style>
