<template>
  <div class="riskClass">
    <Pie :optionData="dataList" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Pie from './Pie.vue'

const props = defineProps({
  dataList: {
    type: Object,
    default: () => ({}),
  },
})

const optionData = ref<{ name: string; value: number }[]>([
  { name: '安全检查', value: 58 },
  { name: '巡检巡查', value: 25 },
  { name: '随手拍', value: 16 },
  { name: '智能识别', value: 13 },
])
</script>

<style lang="scss" scoped>
.riskClass {
  height: 200px;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
}
</style>
