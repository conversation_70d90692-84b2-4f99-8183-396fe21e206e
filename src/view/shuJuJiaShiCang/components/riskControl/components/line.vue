<template>
  <div class="line-part w-full h-full" ref="pieChart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
import { isFontSizeRem } from '@/common/utils'
const props = defineProps({
  dataList: {
    type: Object,
    default: () => {},
  },
})

const pieChart = ref()

let myChart: any

async function initEcharts() {
  if (myChart) myChart.dispose()
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart = echarts.init(pieChart.value)

  // 传入数据生成 option
  const option = {
    //   backgroundColor: '#101736', // 背景色
    title: {
      // text: '隐患上报与整改统计',
      textStyle: {
        color: '#ffffff', // 标题文字颜色
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        lineStyle: {
          color: '#57617B',
        },
      },
    },
    legend: {
      data: ['隐患上报数', '隐患整改数'],
      textStyle: {
        color: '#ffffff', // 图例文字颜色
        fontSize: isFontSizeRem(10),
      },
      itemWidth: 20,
      itemHeight: 3,
      height: '3px',
      right: '10%', // 图例靠右
      top: '5%', // 图例靠上
      icon: 'rect',
    },
    grid: {
      left: '0',
      right: '5%',
      bottom: '8%',
      top: '18%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.dataList?.data_x, // x轴展示最近五个月
      axisLabel: {
        textStyle: {
          color: '#ffffff', // 坐标轴刻度标签颜色
          fontSize: isFontSizeRem(10),
        },
      },
      axisLine: {
        lineStyle: {
          color: '#57617B',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        textStyle: {
          color: '#ffffff', // Y轴刻度标签颜色
        },
      },
      axisLine: {
        lineStyle: {
          color: '#57617B',
        },
      },
      splitLine: {
        lineStyle: {
          color: '#57617B',
        },
      },
    },
    series: [
      {
        name: '隐患上报数',
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#F6B30C', // 上报数线条颜色
          width: 2,
        },
        itemStyle: {
          color: '#F6B30C', // 上报数节点颜色
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(12,192,0 ,0.3)',
            },
            {
              offset: 1,
              color: 'rgba(12,192,0 ,0)',
            },
          ]),
        },
        // data: reportData, // 上报数数据
        data: props.dataList?.data_y[0],
      },
      {
        name: '隐患整改数',
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#276ADE', // 整改数线条颜色
          width: 2,
        },
        itemStyle: {
          color: '#276ADE', // 整改数节点颜色
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(26,133,255,0.3)',
            },
            {
              offset: 1,
              color: 'rgba(26,133,255,0)',
            },
          ]),
        },
        // data: rectifyData, // 整改数数据
        data: props.dataList?.data_y[1],
      },
    ],
  }

  myChart.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart) myChart.resize()
  })
}

onMounted(() => {
  initEcharts()
})
watch(
  () => props.dataList,
  (newData, oldData) => {
    initEcharts()
  }
)
// 把initEcharts暴露出去
defineExpose({
  initEcharts,
})
</script>

<style scoped>
.title-num {
  position: absolute;
  bottom: 10%;
  left: 18%;
  z-index: 500;
  background: linear-gradient(to right, #061d31, #227eb6 60%, #227eb6 60%, #051525);

  .a {
    color: #fff;
    font-size: 14px;
    font-weight: 700;
    margin-right: 10px;
  }
  .b {
    color: #fff;
    font-size: 18px;
    font-weight: 400;
  }
}
</style>
