<template>
  <div class="riskClass">
    <Pie :dataList="dataList" />
  </div>
</template>

<script setup lang="ts">
//
import { ref } from 'vue'
import Pie from './line.vue'

const props = defineProps({
  dataList: {
    type: Object,
    default: () => {},
  },
})
</script>

<style lang="scss" scoped>
.riskClass {
  height: 200px;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
}
</style>
