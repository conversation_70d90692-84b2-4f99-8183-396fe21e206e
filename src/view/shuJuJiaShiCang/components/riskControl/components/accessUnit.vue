<template>
  <div class="access-unite">
    <div v-for="(item, index) in dataList as any" :key="item.typeName" class="access-item">
      <div class="icon" :class="'icon-bg' + index"></div>
      <div class="right">
        <div class="item-title">{{ item.typeName }}</div>
        <div class="item-num">
          {{ item.num }} <span class="unit">{{ item.unit }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const props = defineProps({
  dataList: {
    type: Array,
    default: () => [],
  },
})
</script>

<style lang="scss" scoped>
.access-unite {
  padding: 0 1.04vw;
  height: calc(100% - 2.4vw);
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  .access-item {
    display: flex;
    align-items: center;
    .right {
      .item-title {
        font-size: 0.73vw;
      }
      .item-num {
        font-weight: bold;
        font-size: 1.46vw;
        color: #a4e4ff;
        .unit {
          font-size: 0.73vw;
          color: #fff;
        }
      }
    }
  }
  .icon {
    // 宽100 高80
    width: 5.21vw;
    height: 4.17vw;
    background: url(@/assets/image/shujujiashicang/safetyManage/unit.png) no-repeat center/cover;
  }
  .icon-bg1 {
    background: url(@/assets/image/shujujiashicang/safetyManage/project.png) no-repeat center/cover;
  }
  .icon-bg2 {
    background: url(@/assets/image/shujujiashicang/safetyManage/build.png) no-repeat center/cover;
  }
  .icon-bg3 {
    background: url(@/assets/image/shujujiashicang/safetyManage/area.png) no-repeat center/cover;
  }
}
</style>
