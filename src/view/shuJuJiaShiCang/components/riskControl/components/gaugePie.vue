<template>
  <div class="line-part w-full h-full" ref="pieChart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
const props = defineProps<{
  value: number
}>()
watch(
  () => props.value,
  () => {
    initEcharts()
  }
)
defineComponent({ name: 'pieChart' })

const pieChart = ref()
let myChart: any

function initEcharts() {
  if (myChart) myChart.dispose()

  // 标记一个对象，使其永远不会再成为响应式对象
  myChart = echarts.init(pieChart.value)

  // 传入数据生成 option
  const option = {
    // backgroundColor: '#485C6D', //背景色
    series: [
      {
        type: 'liquidFill', //水位图
        radius: '90%', //显示比例
        center: ['50%', '50%'], //中心点
        amplitude: 20, //水波振幅
        data: [props.value], // data个数代表波浪数
        color: ['#23cc72'], //波浪颜色
        backgroundStyle: {
          borderWidth: 1, //外边框
          borderColor: '#23cc72', //边框颜色
          color: '#485C6D', //边框内部填充部分颜色
        },
        label: {
          //标签设置
          normal: {
            position: ['50%', '30%'],
            textStyle: {
              fontSize: '12px', //文本字号,
              color: '#23cc72',
            },
          },
        },
        outline: {
          show: false, //最外层边框显示控制
        },
      },
    ],
  }

  myChart.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart) myChart.resize()
  })
}

onMounted(() => {
  initEcharts()
})

// 把initEcharts暴露出去
defineExpose({
  initEcharts,
})
</script>

<style scoped>
.title-num {
  position: absolute;
  bottom: 10%;
  left: 18%;
  z-index: 500;
  background: linear-gradient(to right, #061d31, #227eb6 60%, #227eb6 60%, #051525);

  .a {
    color: #fff;
    font-size: 14px;
    font-weight: 700;
    margin-right: 10px;
  }
  .b {
    color: #fff;
    font-size: 18px;
    font-weight: 400;
  }
}
</style>
