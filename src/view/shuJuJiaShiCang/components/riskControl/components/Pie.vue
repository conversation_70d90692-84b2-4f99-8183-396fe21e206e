<template>
  <div class="line-part w-full h-full" ref="pieChart"></div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
import { isFontSizeRem } from '@/common/utils'
const props = defineProps({
  optionData: {
    type: Object,
    default: () => {},
  },
})
let total = ref(300)
watch(
  () => props.optionData,
  () => {
    total.value = 0
    props.optionData.forEach((item: any) => {
      total.value += item.value
    })
    initEcharts()
  }
)
defineComponent({ name: 'pieChart' })

const handelData = () => {
  const colorArray = [
    ['#3158D5', '#061E38'],
    ['#1AAFCA', '#061E38'],
    ['#D1D94D', '#061E38'],
    ['#D16E4F', '#061E38'],
    ['#D53131', '#061E38'],
    ['#02A305', '#061E38'],
    ['#F06713', '#061E38'],
    ['#1AAFCA', '#061E38'],
    ['#23CAFF', '#061E38'],
    ['#438CFF', '#08234C'],
    ['#FFB800', '#2A2F3C'],
    ['#00ECB3', '#152A34'],
    ['#B2C3C5', '#1B2E36'],
  ]

  props.optionData.forEach((item: any, index: number) => {
    item.itemStyle = {
      // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //   { offset: 0, color: colorArray[index][0] },
      //   { offset: 1, color: colorArray[index][1] },
      // ]),
      color: colorArray[index][0],
      borderColor: colorArray[index][0],
    }
  })

  return props.optionData
}

const pieChart = ref()
let myChart: any

function initEcharts() {
  if (myChart) myChart.dispose()

  // 标记一个对象，使其永远不会再成为响应式对象
  myChart = echarts.init(pieChart.value)

  // 传入数据生成 option
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: `{b} {d}%`,
    },
    title: {
      text: `{a|${props.optionData.reduce((accumulator, item: any) => accumulator + item.value, 0)}} \n {b|隐患总数\n}`,
      // subtext: '设备总数',
      left: '18%',
      top: 'center',
      textStyle: {
        color: '#fff',
        fontSize: isFontSizeRem(15),
        rich: {
          a: {
            fontSize: isFontSizeRem(24),
            padding: [isFontSizeRem(3), 0],
            color: '#D3E5FF',
            align: 'center',
            fontWeight: 'bold',
          },
          b: {
            fontSize: isFontSizeRem(14),
            color: '#D3E5FF',
            align: 'center',
          },
        },
      },
    },
    legend: {
      type: 'scroll',
      icon: 'circle',
      top: 'center',
      right: '10%',
      // 竖向排列
      orient: 'vertical',
      textStyle: {
        color: '#fff',
        fontSize: isFontSizeRem(12),
      },
      itemStyle: {
        borderWidth: 2,
        // borderColor: 'red',
        color: 'transparent',
      },
      // itemGap: 5,
      // itemHeight: 11,
      pageIconColor: '#ff781f', // 设置翻页箭头颜色
      pageTextStyle: {
        color: '#999', // 设置翻页数字颜色
      },
      pageIconSize: 12, // 设置翻页箭头大小
      formatter: (name: string) => {
        const currentNum = props.optionData.find((item: any) => item.name === name)?.value
        if (!currentNum) return name
        return `${name.length > 8 ? name.slice(0, 8) + '...' : name}   ${currentNum}   ${((+(currentNum / total.value).toFixed(4) * 10000) / 100).toFixed(2)}% `
      },
      tooltip: {
        show: true,
      },
    },
    series: [
      {
        type: 'pie',
        // padAngle: 3,
        radius: ['55%', '80%'],
        center: ['25%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 11,
            color: '#fff',
            formatter: `{c}\n\n{b}`,
          },
        },
        labelLine: {
          show: false,
        },
        data: handelData(),
      },
    ],
  }

  myChart.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart) myChart.resize()
  })
}

onMounted(() => {
  initEcharts()
})

// 把initEcharts暴露出去
defineExpose({
  initEcharts,
})
</script>

<style scoped>
.title-num {
  position: absolute;
  bottom: 10%;
  left: 18%;
  z-index: 500;
  background: linear-gradient(to right, #061d31, #227eb6 60%, #227eb6 60%, #051525);

  .a {
    color: #fff;
    font-size: 14px;
    font-weight: 700;
    margin-right: 10px;
  }
  .b {
    color: #fff;
    font-size: 18px;
    font-weight: 400;
  }
}
</style>
