<template>
  <div class="riskClass">
    <Grading class="left" :optionData="hazardSourceLeave" />
    <classify class="right" :optionData="hazardSourceList" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Grading from './grading.vue'
import classify from './classify.vue'
// import Pie3d from './Pie3d.vue'

const props = defineProps({
  hazardSourceList: {
    type: Object,
    default: () => {},
  },
  hazardSourceLeave: {
    type: Object,
    default: () => {},
  },
})

const optionData = ref<{ name: string; value: number }[]>([
  {
    name: '一般隐患',
    value: 55,
  },
  {
    name: '较大隐患',
    value: 23,
  },
  {
    name: '重大隐患',
    value: 12,
  },
])

const accessType = ref<{ name: string; value: number }[]>([
  { name: '消防管理', value: 265 },
  { name: '设施实施', value: 566 },
  { name: '特殊作业', value: 4533 },
  { name: '特种作业', value: 265 },
  { name: '消防设施', value: 265 },
])
</script>

<style lang="scss" scoped>
.riskClass {
  height: 200px;
  padding: 10px;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  .left {
    width: 75%;
    height: 100%;
  }
  .right {
    width: 100%;
    height: 100%;
  }
}
</style>
