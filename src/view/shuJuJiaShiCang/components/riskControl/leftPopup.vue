<template>
  <div class="leftPopup">
    <div class="part-one">
      <ChartTitle title="隐患整改趋势" />
      <RectificationTrend :dataList="eventCustomTrendList" />
    </div>
    <div class="part-two">
      <ChartTitle title="隐患来源分布" />
      <SourceDistribution :dataList="groupHazardSourceList" />
    </div>
    <div class="part-three">
      <ChartTitle title="安全检查计划" />
      <regulationsRules :dataList="rules" :message="messageList" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import $API from '~/common/api'
import regulationsRules from './components/regulationsRules.vue'
import RectificationTrend from './components/rectificationTrend.vue'
import SourceDistribution from './components/sourceDistribution.vue'
import image from '@/assets/image/shujujiashicang/safetyManage/temp-1.png'
import { useUserInfo } from '~/store'

const userInfo = useUserInfo()
const eventCustomTrendList = ref()
const groupHazardSourceList = ref([])
const accessType = ref([
  { typeName: '接入单位', num: 265, unit: '家' },
  { typeName: '接入项目', num: 566, unit: '个' },
  { typeName: '接入楼栋', num: 4533, unit: '栋' },
  { typeName: '建筑面积', num: 265, unit: '万m2' },
])
// 规程制度
const rules = ref()
// 规则制度
const messageList = ref([
  {
    type: '',
    title: '',
    status: 1,
    statusName: '',
    planCycle: '',
    checkObj: '',
    r_img: '',
  },
])

// 隐患整改趋势
const eventCustomTrend = async () => {
  const res: any = await $API.post({
    url: '/riskControl/eventCustomTrend',
    params: {
      orgCode: userInfo.value.unitId,
      modelType: 'unit_base_url',
    },
  })
  if (res.code !== 'success') return
  eventCustomTrendList.value = {
    data_x: res?.data?.timeList || [],
    data_y: [res?.data?.filedValueList?.total, res?.data?.filedValueList?.disposedNum],
  }
}

// 隐患来源分布
const groupHazardSource = async () => {
  const res: any = await $API.post({
    url: '/riskControl/groupHazardSource',
    params: {
      orgCode: userInfo.value.unitId,
      modelType: 'unit_base_url',
      groupBy: 1,
    },
  })
  if (res.code !== 'success') return
  groupHazardSourceList.value = (
    res.data.map((e) => ({
      name: e.groupByName,
      value: e.total,
    })) || []
  ).sort((a, b) => b.value - a.value)
}

// 安全检查统计
const getPlanCount = async () => {
  const res: any = await $API.post({
    url: '/riskControl/getPlanCount',
    params: {
      orgCode: userInfo.value.unitId,
      modelType: 'unit_base_url',
    },
  })
  if (res.code !== 'success') return
  rules.value = [
    { typeName: '本年检查计划', num: res.data.allPlanCount || 0 },
    { typeName: '本年检查任务', num: res.data.allPlanTaskCount || 0 },
    { typeName: '任务完成率', num: (res.data.completePlanTaskRate || 0) + '%' },
  ]
}

const getPlanList = async () => {
  const res: any = await $API.post({
    url: '/riskControl/getPlanList',
    params: {
      orgCode: userInfo.value.unitId,
      modelType: 'unit_base_url',
      pageNo: 1,
      pageSize: 3,
    },
  })
  if (res.code !== 'success') return
  messageList.value =
    res.data.map((e) => ({
      type: e.planTypeName,
      title: e.planName,
      status: e.planState,
      planId: e.planId,
      statusName:
        e.planState === '1' ? '未发布' : e.planState === '2' ? '进行中' : e.planState === '3' ? '已结束' : '已停用',
      planCycle: e.planStartDate + ' ~ ' + e.planEndDate,
      checkObj: e.planUnitList[0].unitName,
      r_img: import.meta.env.VITE_BASE_IMG_URL + e.planTaskClockInList[0]?.filePath || '',
    })) || []
}

// { typeName: '接入单位', num: 265, unit: '家' },
const getUnit = () => {
  const params = {}
  $API.post({
    url: '/device/monitor/queryDeviceStateById',
    params: {
      orgCode: userInfo.value.unitId,
    },
  })
}

onMounted(() => {
  eventCustomTrend()
  groupHazardSource()
  getPlanCount()
  getPlanList()
})
</script>
<style lang="scss" scoped>
.leftPopup {
  width: 468px;
  height: 100%;
}
</style>
