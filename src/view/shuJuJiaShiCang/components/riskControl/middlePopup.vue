<template>
  <div class="middle-content">
    <div
      v-for="(item, index) in cards"
      :key="index"
      :class="item.label === '待整改数' ? ['card1', item.color] : ['card', item.color]"
      @click="jump(item.label)"
    >
      <div>
        <div class="number">{{ item.number }}</div>
        <div class="label">{{ item.label }}</div>
      </div>
      <div v-if="item.tags" class="tags cursor-pointer">
        <span
          v-for="(tag, tagIndex) in item.tags"
          :class="{ active: selectedTag === tagIndex }"
          :key="tagIndex"
          @click="viewDetail(tagIndex)"
          >{{ tag }}</span
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import dayjs from 'dayjs'
const userInfo = useUserInfo()
const data = ref({}) as any
const startTime = ref('')
const endTime = ref('')

const selectedTag = ref(0) // 默认选中“累计”
const cards = computed(() => {
  return [
    {
      number: data.value?.num ?? 0,
      label: '上报数',
      color: 'blue',
      tags: ['累计', '本年'],
    },
    {
      number: data.value?.unHazardRectificationNum ?? 0,
      label: '待整改数',
      color: 'yellow',
    },
    {
      number: data.value?.timeoutNum,
      label: '超期待整改数',
      color: 'red',
    },
    {
      number: (data.value?.hazardRectificationRate || 0) + '%',
      label: '整改率',
      color: 'green',
    },
  ]
})
const jump = (value: string) => {
  if (value === '待整改数') {
    $API
      .post({
        url: `ehs-clnt-platform-service/login/checkSysPower`,
        data: {
          sysCode: 'hazard_inves',
          userId: userInfo.value.id,
        },
      })
      .then((res: any) => {
        if (res && res.code == 'success') {
          window.open(
            getApiBase() +
              '/ehs-hazard-web/#/hazard-management?status=0' +
              '?token=' +
              res.data.token +
              '&sysCode=' +
              'hazard_inves',
            '_blank'
          )
        }
      })
  } else {
    return
  }
}
function getApiBase() {
  // return window.location.hostname == 'localhost'
  //   ? 'http://*************:9862'
  //   : window.location.origin;
  if (window.location.hostname == 'localhost') {
    // return 'http://*************:9862';
    return 'https://test-bw.gsafetycloud.com'
  } else if (window.location.hostname == 'agjp.tanzervas.com') {
    return window.location.origin + '/aqsc/v1'
  } else {
    return window.location.origin
  }
}
const viewDetail = (index: number) => {
  selectedTag.value = index
  if (index == 0) {
    startTime.value = ''
    endTime.value = ''
    getPointList()
  } else if (index == 1) {
    startTime.value = getTimeRange()[0]
    endTime.value = getTimeRange()[1]
    getPointList()
  }
}
const getPointList = () => {
  const params = {
    orgCode: userInfo.value.orgCode,
    eventType: '',
    modelType: 'unit_base_url',
    startTime: startTime.value,
    endTime: endTime.value,
  }
  $API
    .post({
      url: '/riskControl/topCount',
      params,
    })
    .then((res: any) => {
      // console.log(res, '>>>>>>>>')
      data.value.num = res.data.total || 0
      data.value.unHazardRectificationNum = res.data.unDisposedNum + res.data.disposingNum || 0
      data.value.timeoutNum = res.data.timeoutNum || 0
      data.value.hazardRectificationRate = res.data.hazardDisposeRate || 0
    })
}
onMounted(() => {
  getPointList()
})

const getTimeRange = (str = 'YYYY-MM-DD HH:mm:ss') => {
  // 获取当前日期时间
  const now = dayjs()
  // 获取本年年初的日期时间（即当前年份的1月1日）
  const startOfYear = now.startOf('year')

  // 格式化当前日期时间和本年年初的日期时间
  const today = now.format(str)
  const startOfThisYear = startOfYear.format(str)

  // 返回本年的时间区间
  return [startOfThisYear, today]
}
</script>

<style lang="scss" scoped>
.middle-content {
  color: #fff;
  display: flex;
  margin-left: 260px;
  .card1 {
    display: flex;
    flex-direction: column;
    cursor: pointer;
    width: 150px;
    height: 70px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    position: relative;
    background-image: url(@/view/shuJuJiaShiCang/components/cockpitGis/image/left.png);
    background-size: 100% 100%;
    padding-left: 10px;
    justify-content: center;
  }
  .card {
    display: flex;
    flex-direction: column;
    width: 150px;
    height: 70px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    position: relative;
    background-image: url(@/view/shuJuJiaShiCang/components/cockpitGis/image/left.png);
    background-size: 100% 100%;
    padding-left: 10px;
    justify-content: center;
  }
  .blue {
    background-image: url(@/view/shuJuJiaShiCang/components/cockpitGis/image/left.png);
    background-size: 100% 100%;
  }

  .yellow {
    background-image: url(@/view/shuJuJiaShiCang/components/cockpitGis/image/yellow.png);
    background-size: 100% 100%;
  }

  .red {
    background-image: url(@/view/shuJuJiaShiCang/components/cockpitGis/image/red.png);
    background-size: 100% 100%;
  }

  .green {
    background-image: url(@/view/shuJuJiaShiCang/components/cockpitGis/image/blue.png);
    background-size: 100% 100%;
  }
  .number {
    font-size: 24px;
  }

  .label {
    font-size: 14px;
  }

  .tags {
    position: absolute;
    top: 50%;
    right: 5px;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    span.active {
      background-color: rgba(114, 161, 255, 1); /* 蓝色背景 */
    }
  }

  .tags span {
    background-color: rgba(114, 161, 255, 0.5);
    padding: 2px 5px;
    border-radius: 4px;
    margin-top: 2px;
    font-size: 12px;
  }
}
</style>
