<template>
  <div class="leftPopup">
    <div class="part-one h-240px">
      <ChartTitle title="隐患分级分类" />
      <classification :hazardSourceList="hazardSourceList" :hazardSourceLeave="hazardSourceLeave" />
    </div>
    <div class="part-two h-400px" :lodaing="loading">
      <ChartTitle title="隐患智能监测">
        <template #right>
          <div class="flex items-center justify-end">
            <div class="tab-item" :class="{ active: activeTab == 'ai' }" @click="activeTab = 'ai'">AI视频分析</div>
            <div class="tab-item" :class="{ active: activeTab == 'wlw' }" @click="activeTab = 'wlw'">物联网监测</div>
          </div>
        </template>
      </ChartTitle>
      <div class="flex justify-start items-center title-two">
        <img src="./title-two-ico.png" alt="" />
        <div>{{ activeTab == 'ai' ? '消控室值守' : '水压水位' }}</div>
      </div>
      <VideoAnalysis
        v-loading="loading"
        element-loading-text="加载中..."
        element-loading-background="rgba(29, 53, 100, 0.8)"
        v-if="activeTab == 'ai'"
        :guardOpts="guardOpts"
        :leaveRecordStatisticsList="leaveRecordStatisticsList"
      />
      <IntelligentMonitoring v-else :waterOpts="waterOpts" :systemOperationCount="systemOperationCount" />
    </div>
    <div class="part-three">
      <ChartTitle title="常见隐患TOP" />
      <Top :topOpts="familiarHazardTopList" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import $API from '~/common/api'
import classification from './components/classification.vue'
import IntelligentMonitoring from './components/IntelligentMonitoring.vue'
import VideoAnalysis from './components/VideoAnalysis.vue'
import Top from './components/top.vue'
import { useUserInfo } from '~/store'

const userInfo = useUserInfo()
const activeTab = ref('ai')
const loading = ref(false)
const leaveRecordStatisticsList = ref() as any
const hazardSourceList = ref([])
const hazardSourceLeave = ref([])
const systemOperationCount = ref({
  leaveUnitNum: 0,
  onUnitNum: 0,
})
const orgParams = {
  orgCode: userInfo.value.unitId,
  modelType: 'unit_base_url' as const,
}
// 消控室值守
const guardOpts = ref([])
// 水压水位
const waterOpts = ref([])
// 常见隐患TOP
const familiarHazardTopList = ref()

// 隐患分级分类
const groupHazardSource = async () => {
  const res: any = await $API.post({
    url: '/riskControl/groupHazardSource',
    params: { ...orgParams, groupBy: 2 },
  })
  if (res.code !== 'success') return
  const mappedAndSortedData = res.data
    .map((e) => ({ name: e.groupByName, value: e.total }))
    .sort((a, b) => {
      return b.value - a.value
    })

  // 赋值给hazardSourceList.value
  hazardSourceList.value = mappedAndSortedData
}

const groupHazardSourceLeave = async () => {
  const res: any = await $API.post({
    url: '/riskControl/groupHazardLevel',
    params: { ...orgParams },
  })
  if (res.code !== 'success') return
  hazardSourceLeave.value = res.data.map((e) => ({ name: e.hazardLevelName, value: e.total }))
}

// 常见隐患TOP
const familiarHazardTop = async () => {
  const res: any = await $API.post({
    url: '/riskControl/familiarHazardTop',
    params: { ...orgParams, groupBy: 2 },
  })
  if (res.code !== 'success') return
  familiarHazardTopList.value = res.data
}

// 消控室值守
const getLeaveRecordStatistics = async () => {
  loading.value = true
  const res: any = await $API.post({
    url: '/riskControl/getLeaveRecordStatistics',
    params: { ...orgParams },
  })
  if (res.code !== 'success') return
  leaveRecordStatisticsList.value = {
    leaveUnitNum: res?.data?.leaveUnitNum,
    onUnitNum: res?.data?.onUnitNum,
  }
  loading.value = false
}

const getLeaveList = async () => {
  loading.value = true
  const res: any = await $API.post({
    url: '/riskControl/leaveList',
    params: { ...orgParams, page: 1, pageSize: 200 },
  })
  if (res.code !== 'success') return
  guardOpts.value = res.data.rows.map((e) => ({
    projectName: e.unitName,
    status: e.leaveStatus,
    statusName: e.leaveStatus === 1 ? '已返岗' : '离岗',
    leaveTime: e.leaveDuration,
    createTime: e.leaveTime,
    // 如果e.picUrl是以/开头，则把/删除
    img: import.meta.env.VITE_BASE_IMG_URL + e.picUrl?.replace(/^\//, ''),
  }))
  loading.value = false
}

// 物联网监测统计
const systemOperation = async () => {
  const res: any = await $API.post({
    url: '/riskControl/systemOperationCount',
    params: { ...orgParams, systemTypeId: 3 },
  })
  if (res.code !== 'success') return
  systemOperationCount.value = {
    leaveUnitNum: res?.data?.unusualUnitNum || 0,
    onUnitNum: res?.data?.normalUnitNum || 0,
  }
}
// 物联网监测统计列表
const systemOperationList = async () => {
  const res: any = await $API.post({
    url: '/riskControl/systemOperationList',
    params: { ...orgParams, page: 1, pageSize: 100 },
  })
  if (res.code !== 'success') return
  waterOpts.value =
    res.data.rows?.map((e) => ({
      projectName: e.unitName,
      statusName: e.description,
      leaveTime: e.leaveDuration,
      continueTime: e.durationNumNew,
      createTime: e.createTime,
      img: e.img,
      value: e.monitorItemValue,
      mpa: e.monitorItemUnit,
      eventId: e.eventId,
      subCenterCode: e.subCenterCode,
      id: e.id,
    })) || []
}
onMounted(() => {
  systemOperation()
  familiarHazardTop()
  getLeaveRecordStatistics()
  getLeaveList()
  groupHazardSource()
  groupHazardSourceLeave()
  systemOperationList()
})
</script>

<style lang="scss" scoped>
.tab-item {
  font-family: Alibaba PuHuiTi;
  background: #091130;
  border: 1px solid #3b5390;
  padding: 0 15.9994px;
  font-size: 15.9994px;
  color: #c6d5f1;
  cursor: pointer;
}
.active {
  color: #fff;
  background: url(./tab-item-active.png) no-repeat center;
  background-size: 100% 100%;
}
.title-two {
  background: linear-gradient(90deg, #2f5ed3 0%, rgba(17, 58, 140, 0.68) 32%);
  padding: 4.9997px 2.0006px;
  font-size: 15.9994px;
  color: #fff;
  margin: 14.0006px 0;
  img {
    width: 16.9997px;
    height: 24px;
  }
}
</style>
