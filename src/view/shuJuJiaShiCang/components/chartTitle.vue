<template>
  <div class="chartTitle flex items-center justify-between">
    <div>{{ title }}</div>
    <slot name="right"></slot>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: '我是图表表头',
  },
})
//
</script>

<style lang="scss" scoped>
.chartTitle {
  padding-left: 24px;
  width: 465.984px;
  height: 36.864px;
  background: url('../../../assets/image/shujujiashicang/title-bg.png') no-repeat center/cover;
  font-size: 17.472px;
  line-height: 36.864px;
  font-family: 35--Regular;
  font-weight: 400;
  color: #ffffff;
  font-family: 'shujujiashicang', 'Microsoft YaHei';
}
</style>
