// import config from '~/config';

import rotationBorder1 from './image/rotationBorder1.png'
import rotationBorder2 from './image/rotationBorder2.png'

const dom = document.createElement('div')
// dom.className = 'h-full w-full';
// dom.style.height = '100%'
// dom.style.width = '100%'
dom.style.height = '100%'
dom.style.width = '100%'
dom.style.overflow = 'hidden'
dom.style.position = 'absolute'
dom.style.left = '0'
dom.style.top = '0'
document.querySelector('body')?.appendChild(dom)
dom.style.opacity = '0'
dom.style.zIndex = '-1'

CameraControls.install({ THREE })
let LMSDisplay: any = null
IndoorMap.init()
LMSDisplay = new IndoorThree.LMSDisplay({
  target: dom,
  // scale: 0.000001,
  worker: true,
  // polar: 0.804725609259662,
  polar: 1.0964240914871268,
  azimuth: 0,
  dplane_img: [rotationBorder1, rotationBorder2],
  baseMap: CONST_GeoData_China,
  baseMap_line: CONST_GeoData_China_line,
  userMap: CONST_GeoData_Item,
  userMap_inline: CONST_GeoData_Item_inline,
  userMap_outline: CONST_GeoData_Item_outline,
  bgTransparent: true, //背景透明（为true需关闭泛光）
  closeFog: true, //雾
  closeBloom: true, //泛光
  closeGridPoint: true, //网格
  closeCubePlane: true, //关闭扩散波纹
  closeUpParticles: true, //上升粒子
  // closeBaseMap: true,//基础底图
  geouser_item_filter: (i: any) => {
    // console.log("🚀 ~ i :", i )
  },
  // expand: 0.5,
  dataType: 0,
  // bgTransparent: true,//背景透明（为true需关闭泛光）
  grid_pointColor: 'rgba(53,70,88,0.1)',

  wall_scale: 1.1, // 3d图层高度
  dplane_scale: 1.1,
  dplane_opacity: 0.1,
  upHeight_scale: 1, // 鼠标滑过 凸起高度
  cylinder_scale: 2.5,
  cylinderSize_scale: 1.5,
  boxSize_scale: 2,
  particles_scale: 0.5,
  particles_dot_scale: 0.3,
})

export const initDom = (traget: HTMLElement) => {
  traget.appendChild(dom)
  dom.style.position = ''
  dom.style.opacity = '1'
  dom.style.zIndex = ''
}
;(window as any).LMSDisplay = LMSDisplay
export { LMSDisplay }
// todo
