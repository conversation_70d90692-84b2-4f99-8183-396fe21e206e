import { first } from 'rxjs'
import { createVNode, render } from 'vue'
let el = null
export function setPopup(config: { [property: string]: any }) {
  const { elment, coms, ...options } = config
  console.log('🚀 ~ setPopup ~ elment:', elment)
  if (!elment) return
  if (elment.firstChild) elment.firstChild.remove()
  const PopUpConstructor = createVNode(coms, {
    ...options,
  }) // 返回一个vue子类
  //创建实例并且挂载
  el = document.createElement('div')
  render(PopUpConstructor, el)
  elment.appendChild(el)
  console.log('🚀 ~ setPopup ~ el:', el)

  return el
}
