<template>
  <div class="w-full h-full">
    <div class="w-full h-full" ref="Elgis"></div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, nextTick } from 'vue'
import { useUserInfo } from '~/store'
import $API from '~/common/api'
import { useRoute } from 'vue-router'
import { GISOBJ } from '../../js/gisGlobal'
import buildPopup from './buildPopup.vue'
const userInfo = useUserInfo()
const Elgis = ref()
interface Props {
  data: any
}
const props = withDefaults(defineProps<Props>(), {
  name: '',
  hasBtn: false,
  onClick: () => {},
  data: () => {},
  type: '1',
  // onClose:() => { },
})
const unitData = ref({
  unitId: props.data.unitId,
  buildingId: '',
  floorId: '',
  aerialviewImg: props.data.aerialviewImg, // 'install/110000DW1832753728612990976/nkt.jpg',
  subCenterCode: props.data.subCenterCode, // '340104YYZX1832719154583437312',
  aerialMapType: props.data.aerialMapType,
})
const buildList = ref<any[]>([])

const colorFill = [
  {
    fill: { color: 'rgba(207, 38, 27, 0.7)' },
    stroke: { color: 'rgba(207, 38, 27, 1)', width: 1 },
  },
  {
    fill: { color: 'rgba(245, 154, 34, 0.7)' },
    stroke: { color: 'rgba(245, 154, 34, 1)', width: 1 },
  },
  {
    fill: { color: 'rgba(244, 244, 16, 0.7)' },
    stroke: { color: 'rgba(244, 244, 16, 1)', width: 1 },
  },
  {
    fill: { color: 'rgba(39, 101, 255, 0.7)' },
    stroke: { color: 'rgba(39, 101, 255, 1)', width: 1 },
  },
]

const showNkt = async () => {
  GISOBJ.getIndoorMap().clearAll()
  GISOBJ.initDom(Elgis.value)
  await GISOBJ.showNkt(
    {
      unitId: unitData.value.unitId || '',
      aerialviewImg: unitData.value.aerialviewImg || '',
      viewType: unitData.value.aerialMapType,
      subCenterCode: unitData.value.subCenterCode,
      pointerList: [],
    },
    (data) => {
      //   GISOBJ.addBuildTip(data, {
      //     coms: buildPopup,
      //     buildingName: _build.buildingName || data.text || data.modelText || data.buildingName || data.buildingName2,
      //     isShowInfo: false,
      //   })
    }
  )
  GISOBJ.getIndoorMap().setGridVisible(true)
  //   queryFloorBgColor()
}

// /aqsc/v1/api/v3/ehs-clnt-rmc-service/riskPointLocation/queryRiskPointLocationIsDivide

// 接口查询楼层批量上色
const queryFloorBgColor = async (floorId?: string) => {
  const useKey = 'currentRiskLevel'
  const params = {
    pageNo: 1,
    pageSize: -1,
    riskLevel: '1,2,3,4',
    unitId: unitData.value.unitId,
  }
  const res = await $API.post({
    url: '/riskPointLocation/queryRiskPointLocationIsDivide',
    params: {
      modelType: 'risk_base_server',
    },
    data: params,
  })

  // const res: any = {}
  // || FxRes
  if (res.data.length) {
    for (let index = 0; index < res.data.length; index++) {
      const element = res.data[index]
      if (element.locationGisId) {
        const level = element[useKey] && Number(element[useKey])
        const pSymbol = GISOBJ.getIndoorMap().InitStyleInfoValueItemByGSDataToSymbol(
          colorFill[level - 1],
          undefined,
          'fill'
        )
        const pElement = GISOBJ.getIndoorMap()
          .getCurrentGridAreaLayer()
          .GetGeoByFieldNV('gridNo', element.locationGisId)
        pElement.setSymbol(pSymbol)
        pElement.GeoUpdate()
        GISOBJ.render()
      }
    }
  }
}

onMounted(() => {
  nextTick(() => {
    showNkt()
  })
})
</script>
