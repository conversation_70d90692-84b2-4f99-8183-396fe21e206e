<template>
  <div class="gisPopup flex-row justify-between w-[484px]">
    <div class="close-box text-[#fff]">
      <div class="title">
        <!-- <myTooltip :str="data.name"></myTooltip> -->
        {{ data.name }}
      </div>
      <div class="close flex-shrink-0 p-[10px] cursor-pointer" @click="closeUnitInfo">x</div>
    </div>
    <div class="text-[#fff] p-[10px] flex unitAddress">
      地址：
      <myTooltip :str="data.provinceName + data.countyName + (data.unitAddress || '')"></myTooltip>
      <!-- 辽宁省鞍山市 -->
    </div>
    <div class="p-[10px] h-[225px]">
      <gisMap :data="buildInfo" v-if="IsShowMap"></gisMap>
    </div>
    <div class="footer p-[10px]">
      <div class="flex items-center justify-between" v-if="type == '1'">
        <div
          class="w-[85px] h-[32px] text-[14px] footerbtn cursor-pointer flex items-center justify-center"
          :class="{ active: active == n.key }"
          v-for="n in buttonList"
          :key="n.key"
          @click="setActive(n)"
        >
          <div>
            {{ n.label }}
          </div>
        </div>
      </div>
      <div v-if="type == '2'">
        <div class="dashboard">
          <div v-for="(item, index) in cards" :key="index" :class="['card', item.color]">
            <div>
              <div class="number">{{ item.number }}</div>
              <div class="label">{{ item.label }}</div>
            </div>
            <div v-if="item.tags" class="tags cursor-pointer">
              <span
                v-for="(tag, tagIndex) in item.tags"
                :class="{ active: selectedTag === tagIndex }"
                :key="tagIndex"
                @click="viewDetail(tagIndex)"
                >{{ tag }}</span
              >
            </div>
          </div>
        </div>
      </div>
      <div class="access-unite" v-if="type == '3'">
        <div
          v-for="(item, index) in dataList as any"
          :key="item.typeName"
          class="access-item icon"
          :class="'icon-bg' + index"
        >
          <div class="item-title">{{ item.typeName }}</div>
          <div class="item-num">{{ item.num }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ToolTip as myTooltip, ToolTips as myTooltips } from '@tanzerfe/tanzer-ui'
// import { Close } from '@element-plus/icons-vue';
import { computed, watch, ref, onMounted } from 'vue'
import config from '~/config'
import { useUserInfo } from '~/store'
import $API from '~/common/api'
import { useRoute } from 'vue-router'
const userInfo = useUserInfo()

import gisMap from './gis.vue'
// const { userInfo } = useStore()
const IsShowMap = ref(false)

interface Props {
  hasBtn: boolean
  name: string
  onClick: () => void
  onClose: () => void
  data: any
  type: string
}
const selectedTag = ref(0) // 默认选中“累计”
const props = withDefaults(defineProps<Props>(), {
  name: '',
  hasBtn: false,
  onClick: () => {},
  data: () => {},
  type: '1',
  // onClose:() => { },
})
const active = ref('')
interface Item {
  sysCode: string
  key: string
  label: string
  project: string
}
const dataList = ref([
  { typeName: '接入设备总数', num: 0, key: 'total' },
  { typeName: '火警报警设备', num: 0, key: 'alarmNum' },
  { typeName: '电气火灾设备', num: 0, key: 'electricalNum' },
  { typeName: '消防水系统设备', num: 0, key: 'fireControlNum' },
  { typeName: '视频设备', num: 0, key: 'videoNum' },
  { typeName: '其他设备', num: 0, key: 'otherNum' },
])
const cards = computed(() => {
  return [
    {
      number: props.data.num ?? 0,
      label: '上报数',
      color: 'blue',
      tags: ['累计', '本年'],
    },
    {
      number: props.data.unDisposedNum ?? 0,
      label: '待整改数',
      color: 'yellow',
    },
    {
      number: props.data.timeoutNum,
      label: '超期隐患数',
      color: 'red',
    },
    {
      number: (props.data.disposeRate || 0) + '%',
      label: '整改率',
      color: 'green',
    },
  ]
})

const viewDetail = (index: number) => {
  selectedTag.value = index
  if (index == 0) {
    cards.value[0].number = props.data.num
    cards.value[1].number = props.data.unDisposedNum
    cards.value[2].number = props.data.timeoutNum
    cards.value[3].number = props.data.disposeRate + '%'
  } else if (index == 1) {
    cards.value[0].number = props.data.numYear
    cards.value[1].number = props.data.unDisposedNumYear
    cards.value[2].number = props.data.timeoutNumYear
    cards.value[3].number = props.data.disposeRateYear + '%'
  }
}
const buttonList = ref<Item[]>([
  {
    sysCode: 'scene_manage',
    key: '1',
    label: '现场一张图',
    project: 'onSiteMgr',
  },
  {
    sysCode: 'risk_level',
    key: '2',
    label: '风险一张图',
    project: 'ehsRiskMgr',
  },
  {
    sysCode: 'hazard_inves',
    key: '3',
    label: '隐患一张图',
    project: 'hazardMgr',
  },
  {
    sysCode: 'safe-operation',
    key: '4',
    label: '作业一张图',
    // project: 'SafeHomeWork',
    project: 'SafeHomeWork',
  },
])

const buildInfo = ref<any>({})

const setActive = (val: Item) => {
  active.value = val.key
  getJumpUrl(val)
}

const getJumpUrl = async (val: Item) => {
  let goUrl
  const res = await $API.post({
    url: `ehs-clnt-platform-service/login/checkSysPower`,
    data: {
      sysCode: val.sysCode,
      userId: userInfo.value.id,
    },
  })
  goUrl =
    `${config.base_prefix}/ehs-gis/#/` +
    '?token=' +
    res.data.token +
    '&sysCode=' +
    val.sysCode +
    '&project=' +
    val.project +
    '&unitId=' +
    props.data.value
  window.open(goUrl, '_blank')
}
const closeUnitInfo = () => {
  IsShowMap.value = false
  props.onClose()
}
const clickHandle = () => {
  props.onClick()
}

const getTrainPlanStatistics = async () => {
  const res: any = await $API.post({
    url: '/device/management/deviceDataOverview',
    params: {
      orgCode: userInfo.value.unitId,
      unitId: props.data.value,
      modelType: 'unit_base_url',
    },
  })
  if (res.code !== 'success') return
  let _data = res.data
  dataList.value.forEach((i) => {
    i.num = _data[i.key] || '0'
  })
}

onMounted(() => {
  getTrainPlanStatistics()
  buildInfo.value = {
    unitId: props.data.value,
    ...props.data,
  }
  IsShowMap.value = true
})
// defineExpose({ myname,myhasBtn })
defineOptions({ name: 'pointerPopup' })
</script>

<style lang="scss" scoped>
.unitAddress {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.gisPopup {
  position: relative;
  background-color: rgba(15, 25, 44, 1);

  .close-box {
    display: flex;
    justify-content: space-between;
    background-image: url(../../image/title-bg.png);
    background-size: 100% 100%;
    height: 45px;
    align-items: center;

    .title {
      font-size: 18px;
      padding-left: 40px;
    }
    .close-icon {
      padding-right: 20px;
    }
  }
  .footerbtn {
    text-align: center;
    color: #fff;
    background-image: url(../../image/bj-nor.png);
    background-size: 100% 100%;
  }
  .active {
    background-image: url(../../image/bj-pre.png);
  }
}
.dashboard {
  display: flex;
}

.card {
  display: flex;
  flex-direction: column;
  width: 150px;
  height: 70px;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  position: relative;
  background-image: url(../../image/left.png);
  background-size: 100% 100%;
  padding-left: 10px;
  justify-content: center;
}

.blue {
  background-image: url(../../image/left.png);
  background-size: 100% 100%;
}

.yellow {
  background-image: url(../../image/yellow.png);
  background-size: 100% 100%;
}

.red {
  background-image: url(../../image/red.png);
  background-size: 100% 100%;
}

.green {
  background-image: url(../../image/blue.png);
  background-size: 100% 100%;
}

.number {
  font-size: 24px;
}

.label {
  font-size: 14px;
}

.tags {
  position: absolute;
  top: 50%;
  right: 5px;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  span.active {
    background-color: rgba(114, 161, 255, 1); /* 蓝色背景 */
  }
}

.tags span {
  background-color: rgba(114, 161, 255, 0.5);
  padding: 2px 5px;
  border-radius: 4px;
  margin-top: 2px;
  font-size: 12px;
}
.access-unite {
  padding: 14.208px;
  height: calc(100% - 46.08px);
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 11.136px;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  .access-item {
    padding: 7.68px 12.288px;
    width: 139.968px;
    height: 66.048px;
    .item-title {
      font-size: 14.016px;
    }
    .item-num {
      font-weight: bold;
      font-size: 24px;
    }
  }
  .icon {
    background: url(@/assets/image/shujujiashicang/deviceRun/d-tools.png) no-repeat center/cover;
  }
  .icon-bg1 {
    background: url(@/assets/image/shujujiashicang/deviceRun/d-fire.png) no-repeat center/cover;
  }
  .icon-bg2 {
    background: url(@/assets/image/shujujiashicang/deviceRun/d-book.png) no-repeat center/cover;
  }
  .icon-bg3 {
    background: url(@/assets/image/shujujiashicang/deviceRun/d-dan.png) no-repeat center/cover;
  }
  .icon-bg4 {
    background: url(@/assets/image/shujujiashicang/deviceRun/d-video.png) no-repeat center/cover;
  }
  .icon-bg5 {
    background: url(@/assets/image/shujujiashicang/deviceRun/d-room.png) no-repeat center/cover;
  }
}
</style>
