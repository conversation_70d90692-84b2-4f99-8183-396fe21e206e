<template>
  <div class="gisPopup flex-row justify-between">
    <span class="title text-ellipsis">{{ buildingName }}</span>
  </div>
</template>
<script lang="ts" setup>
interface Props {
  buildingName: string
}

const props = withDefaults(defineProps<Props>(), {
  buildingName: '',
})
</script>

<style lang="scss" scoped>
.gisPopup {
  position: relative;
  display: flex;
  align-items: center;
  padding: 5px 18px;
  background: #fff;
  z-index: 1;

  border: 1px solid #0080ff;
  background: linear-gradient(0deg, #02336b 0%, #0262cf 100%);
  box-shadow: 0px 0px 4px 0px rgba(21, 109, 211, 0.5);
  border-radius: 4px;

  // &::before,
  &::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border: 10px solid transparent;
    bottom: -21px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  // &::before{
  //     border-top: 10px solid rgba(2, 51, 107, 1);
  //     bottom: -19px;
  //     z-index: 2;
  // }
  &::after {
    border-top: 10px solid rgba(0, 128, 255, 1);
  }

  .title {
    // width: 180px;
    white-space: nowrap;
    font-size: 16px;
    color: #fff;
    font-weight: 600;
  }

  .detailBtn {
    display: inline-block;
    white-space: nowrap;
    cursor: pointer;
    text-align: center;
    box-sizing: border-box;
    font-size: 12px;
    margin-left: 20px;
    color: #fff;
    width: 50px;
    padding: 5px 0;
    background: #0d417c;
    border: 1px solid #49b8ff;
    border-radius: 4px;
  }
}
</style>
