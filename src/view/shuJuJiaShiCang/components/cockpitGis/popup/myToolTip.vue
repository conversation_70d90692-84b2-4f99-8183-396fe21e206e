<template>
  <div class=""></div>
  <n-tooltip placement="bottom" trigger="hover" :disabled="isDisabled" @update:show="handleUpdateShow">
    <template #trigger>
      <div class="str-content" ref="tipBox" @mouseover="onMouseOver">
        <div class="str-span">
          <span style="font-size: inherit"> {{ title || '-' }}</span>
        </div>
        <span class="elStr" ref="elStr" style="visibility: hidden">
          {{ title || '-' }}
        </span>
      </div>
    </template>
    <span> {{ title || '-' }} </span>
  </n-tooltip>
</template>
<script lang="ts" setup>
import { ref, onMounted, computed, nextTick } from 'vue'
const props = defineProps<{ title: string }>()
const tipBox = ref()
const elStr = ref()
const isDisabled = ref(true)
const fomartStyle = computed(() => {
  //   if (props.size) return `font-size:${props.size}px;font-weight:${props.weight}`
  return ''
})
const handleUpdateShow = (val: boolean) => {
  console.log('🚀 ~ handleUpdateShow ~ val:', val)
}
const onMouseOver = () => {
  let pw = elStr.value?.parentNode.offsetWidth
  let sw = elStr.value?.offsetWidth
  isDisabled.value = sw <= pw
}
onMounted(() => {})
defineOptions({ name: 'MyToolTip' })
</script>
<style lang="scss" scoped>
.str-content {
  width: 100%;
  display: grid;
  grid-template-columns: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;

  .elStr {
    position: absolute;
    right: 100%;
    top: 0;
  }
}

.str-span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
