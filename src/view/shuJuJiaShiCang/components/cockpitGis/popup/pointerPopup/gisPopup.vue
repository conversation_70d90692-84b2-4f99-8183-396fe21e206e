<template>
  <div @click="clickHandle" class="flex justify-center items-center relative" style="flex-direction: column">
    <div
      class="pointerPopup flex-row justify-between cursor-pointer"
      @click.stop="clickHandle"
      @mouseenter="mouseenter"
      @mouseleave="onMouseleave"
    >
      <img :src="setPointStyle(data)" alt="" />
      <!-- <span class="title text-ellipsis">{{ data.name }} </span> -->
    </div>
    <div class="gis-temp-Popup unitname flex-row justify-between cursor-pointer" v-if="isShowUnitName" :style="bgStyle">
      <span class="title text-ellipsis">{{ name }} </span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, watch, ref } from 'vue'
import { unitLayer } from '../pointerIcon'

interface Props {
  name: string
  onClick: (val: any) => void
  data: any
  code: string | number
  bgColor: string
  popup: any
  type: string
}

const props = withDefaults(defineProps<Props>(), {
  name: '',
  hasBtn: false,
  onClick: (val: any) => {},
  data: {},
  bgClass: '',
  bgColor: '',
  popup: () => {},
  type: '',
})
const isShowUnitName = ref<boolean>(false)

const bgStyle = computed(() => {
  let _style = ''
  let _bgColor = props.bgColor
  let _borderColor = colorToRGBA(_bgColor, 0.8)
  if (props.bgColor)
    _style = `background-color:${_borderColor};border-color:${props.bgColor};box-shadow: 0px 0px 4px 0px ${_bgColor};`
  return _style
})
const mouseenter = () => {
  if (isShowUnitName.value) return
  isShowUnitName.value = true
  props.popup?.Update()
}
const onMouseleave = () => {
  if (!isShowUnitName.value) return
  isShowUnitName.value = false
  props.popup?.Update()
}

function colorToRGBA(color: string, transparent = 1) {
  let regEx = /^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/
  let match = color.match(regEx)
  if (match) {
    return `rgba(${parseInt(match[1], 10)},${parseInt(match[2], 10)},${parseInt(match[3], 10)},${transparent})`
  }
  // 处理十六进制格式
  if (/^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6})$/.test(color)) {
    let hex = color.substring(1)
    let r, g, b
    if (hex.length === 3) {
      r = parseInt(hex.substring(0, 1) + hex.substring(0, 1), 16)
      g = parseInt(hex.substring(1, 2) + hex.substring(1, 2), 16)
      b = parseInt(hex.substring(2, 3) + hex.substring(2, 3), 16)
    } else {
      r = parseInt(hex.substring(0, 2), 16)
      g = parseInt(hex.substring(2, 4), 16)
      b = parseInt(hex.substring(4, 6), 16)
    }
    return `rgba(${r},${g},${b},${transparent})`
  }

  return null // 不是有效的颜色格式
}

const clickHandle = () => {
  props.onClick(props.data)
}
const setPointStyle = (data: any) => {
  let _eventType: any = data.eventType
  let _img = unitLayer[_eventType]['icon']

  if (_eventType) _img = unitLayer[_eventType]['icon']
  if (props?.type == '2') {
    // 重大1 一般2 无隐患3
    _eventType = data.hazardType == 2 ? 3 : data.hazardType == 3 ? 0 : data.hazardType
    _img = unitLayer[_eventType]['icon']
  }
  if (props?.type == '4') {
    // 0 5 4 1 7
    let _eventType = 7
    if (data.socre >= 90) {
      _eventType = 0
    } else if (data.socre >= 80) {
      _eventType = 5
    } else if (data.socre >= 60) {
      _eventType = 3
    } else if (data.socre > 0) {
      _eventType = 1
    }
    _img = unitLayer[_eventType]['icon']
  }
  // hazardType
  return _img
}
</script>

<style lang="scss" scoped>
.gis-temp-Popup {
  position: relative;
  display: flex;
  align-items: center;
  padding: 5px 18px;
  z-index: 5;
  // border: 1px solid rgba(31, 133, 241, 1);
  // background-color: rgba(31, 133, 241, .8);
  // box-shadow: 0px 0px 4px 0px rgba(31, 133, 241, 1);
  border-radius: 8px;
  flex-direction: column;

  // &::before,

  .title {
    // width: 180px;
    white-space: nowrap;
    font-size: 16px;
    color: #fff;
    font-weight: 600;
  }
}
.unitname {
  position: absolute;
  top: 0%;
  left: 50%;
  transform: translate(-50%, -100%);
}

.pointerPopup {
  position: relative;
  display: flex;
  // align-items: center;
  // padding: 5px 18px;
  // background: #fff;
  // z-index: 5;
  // border: 1px solid rgba(31, 133, 241, 1);
  // background-color: rgba(31, 133, 241, .8);
  // box-shadow: 0px 0px 4px 0px rgba(31, 133, 241, 1);
  // border-radius: 8px;

  // &::before,
  // &::after {
  //   content: '';
  //   position: absolute;
  //   width: 0;
  //   height: 0;
  //   border: 10px solid transparent;
  //   bottom: -18px;
  //   left: 50%;
  //   transform: translateX(-50%);
  //   z-index: 1;
  // }

  // &::after {
  //   border-top: 10px solid rgba(31, 133, 241, 1);
  // }

  .title {
    // width: 180px;
    white-space: nowrap;
    font-size: 16px;
    color: #fff;
    font-weight: 600;
  }
}

.warning {
  border: 1px solid rgba(164, 27, 143, 1);
  background-color: rgba(164, 27, 143, 0.8);
  box-shadow: 0px 0px 4px 0px rgba(164, 27, 143, 1);

  &::after {
    border-top: 10px solid rgba(164, 27, 143, 1);
  }
}

.fire {
  border: 1px solid rgba(252, 54, 54, 1);
  background-color: rgba(252, 54, 54, 0.8);
  box-shadow: 0px 0px 4px 0px rgba(252, 54, 54, 1);

  &::after {
    border-top: 10px solid rgba(252, 54, 54, 1);
  }
}
</style>
