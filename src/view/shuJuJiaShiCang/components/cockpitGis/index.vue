<template>
  <div class="gis">
    <div class="gisMap" ref="elGisMap" id="gisMap" @click="onClickGisWrap"></div>
    <div class="search-box w-200px h-40px absolute top-[120px] left-[530px] z-6">
      <searchBox :options="searchOption" v-model="searchValue"></searchBox>
    </div>
    <div
      class="w-100px absolute bottom-[30px] right-[530px] z-40"
      v-if="route.query.tab == '2' || route.query.tab == '4'"
    >
      <legendBox :options="legendOptions"></legendBox>
    </div>
  </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from 'vue'
import { LMSDisplay, initDom } from './LMSDisplay'
import searchBox from './coms/search.vue'
import legendBox from './coms/legend.vue'

import pointer from './popup/pointerPopup/gisPopup.vue'
import unitPopup from './popup/unitPopup/gisPopup.vue'
import $API from '~/common/api'
// import { setPopup as setPointerPopupTip } from './pointerPopup/index';
// import { setPopup as setUnitPopup } from './popup/unitPopup/index';
import { setPopup } from './popup/index'
import { useUserInfo } from '~/store'
import { useRoute } from 'vue-router'
import { unitLayer } from './popup/pointerIcon'

const userInfo = useUserInfo()
// unitLayer[_eventType]['icon']
//    // 重大1 一般2 无隐患3

// 定义搜索选项接口
interface SearchOption {
  value: string | number
  unitPointX: string | number
  unitPointY: string | number
  [key: string]: any // 其他可能的属性
}

// 定义图例选项接口
interface LegendOption {
  imgUrl: string
  label: string
}

// 定义单位数据接口
interface UnitData {
  name?: string
  unitName?: string
  unitPointX: string | number
  unitPointY: string | number
  hazardType?: string
  socre?: number
  score?: number
  eventType?: string
  bgColor?: string
  type?: string | number
  [key: string]: any
}

const legendOptions = ref<LegendOption[]>([])

// 地图参数
const elGisMap = ref<HTMLElement>()
const route = useRoute()
const isCollapse = ref<boolean>(false)
const searchOption = ref<SearchOption[]>([])
const searchValue = ref<string>('')

function onClickGisWrap() {
  const inputs = document.querySelectorAll('input')
  inputs.forEach(function (input) {
    input.blur()
  })
}

watch(
  () => searchValue.value,
  (val) => {
    console.log('🚀 ~ watch ~ val:', val)
    if (val) {
      const unitItem = searchOption.value.find((i) => i.value == val)
      if (!unitItem) return false
      let { unitPointX, unitPointY } = unitItem
      let _unitPointX = toNum(unitPointX)
      let _unitPointY = toNum(unitPointY)

      if (!_unitPointX || !_unitPointY) return false
      let _pointer = IndoorMap.projectXY_FromTo([_unitPointX, _unitPointY], CONST_SRID_BD09MC, CONST_SRID_WebMC)
      console.log('🚀 ~ isChinaPointer:', _pointer)

      if (!_pointer) return false
      console.log({
        x: _pointer[0],
        y: _pointer[1],
        z: LMSDisplay.getDepth(),
      })

      LMSDisplay.getMap().getView().setCenter({
        x: _pointer[0],
        y: _pointer[1],
        z: LMSDisplay.getDepth(),
      })
    }
  }
)
// 添加点位图标
const fomartLiveColor = (item: UnitData): string => {
  // 【低风险】90分以上含90分、【中风险】80分至90分，含80分、【高风险】60分至80分，含60分、【危险】60分以下。
  if (route.query.tab == '2') {
    if (item.hazardType == '1') return `rgb(195,32,33)`
    if (item.hazardType == '2') return `rgba(255, 109, 0,1)`
    if (item.hazardType == '3') return `rgb(28,140,215)`
    return `rgb(28,140,215)`
  }
  if (route.query.tab == '4') {
    if (item?.socre && item?.socre >= 90) return `rgb(3,198,84)`
    if (item?.socre && item?.socre >= 80) return `rgb(28,140,215)`
    if (item?.socre && item?.socre >= 60) return `rgba(255, 109, 0,1)`
    if (item?.socre && item?.socre > 0) return `rgb(195,32,33)`
    return `rgb(156,156,156)`
  }

  if (item.eventType == '1') return `rgb(195,32,33)`
  if (item.eventType == '3') return `rgba(255, 109, 0,1)`
  if (item.eventType == '4') return `rgba(255, 249, 0, 1)`
  if (item.eventType == '5') return `rgb(28,140,215)`
  return 'rgba(28, 140, 215, 1)'
}
const toNum = (num: string | number): number | '' => {
  let _num = Number(num)
  if (!isNaN(_num)) {
    return _num
  }
  return ''
}
const addPointerPopup = (i: UnitData): void => {
  let _container = document.createElement('div')
  const popup = LMSDisplay?.m_ThreeMap.getView().getPopupManager().Add({
    element: _container,
  })
  setPopup({
    elment: _container,
    coms: pointer,
    onClick: pointerClick,
    name: i.name || i.unitName,
    data: i,
    bgColor: i.bgColor || '',
    popup: popup,
    type: route.query.tab,
  })
  let { unitPointX, unitPointY } = i
  let _unitPointX = toNum(unitPointX)
  let _unitPointY = toNum(unitPointY)
  if (!_unitPointX || !_unitPointY) return
  let isChinaPointer = window.GISShare.SMap.Fitting.CHINA.OutOfChinaExtent_WebMercator_BD09MC(_unitPointX, _unitPointY)
  if (isChinaPointer) return
  let _pointer = IndoorMap.projectXY_FromTo([_unitPointX, _unitPointY], CONST_SRID_BD09MC, CONST_SRID_WebMC)
  popup.Show([..._pointer, LMSDisplay.getDepth()])
  // popup.Update()
}

const pointerClick = (val: UnitData): void => {
  addPointerDetail(val)
}

const getPointList = async (): Promise<any> => {
  // 事件（0：正常 1：火警 2：预警 3：故障 4：隐患 5：动作 7：离线） ','隔开
  const params = {
    orgCode: userInfo.value.orgCode,
    eventType: '',
    modelType: 'unit_base_url',
    type: Number(route.query.tab) - 1,
  }
  return $API.post({
    url: '/unit/distribution/mapLocation',
    params,
  })
}
let unitDetailPopup: any = null
const creaerDetailPopup = () => {
  if (unitDetailPopup) return
  let _container = document.createElement('div')
  _container.style.height = '385px'

  const options_popup = {
    offset: [0, -40],
    positioning: 'bottom-center',
    element: _container,
  }
  unitDetailPopup = LMSDisplay.m_ThreeMap.getView().getPopupManager().Add(options_popup)
}

const addPointerDetail = (data: any) => {
  creaerDetailPopup()
  unitDetailPopup.m_Div.style.zIndex = 7
  setPopup({
    elment: unitDetailPopup.m_Div,
    data: data,
    type: route.query.tab,
    coms: unitPopup,
    onClose: () => {
      unitDetailPopup?.RemoveSelf()
      unitDetailPopup = null
    },
  })

  let { unitPointX, unitPointY } = data
  let _unitPointX = toNum(unitPointX)
  let _unitPointY = toNum(unitPointY)
  if (!_unitPointX || !_unitPointY) return false
  let _pointer = window.IndoorMap.projectXY_FromTo([_unitPointX, _unitPointY], CONST_SRID_BD09MC, CONST_SRID_WebMC)
  unitDetailPopup.Show([..._pointer, LMSDisplay.getDepth()])
  nextTick(() => {
    unitDetailPopup.Update()
  })
  setTimeout(() => {
    LMSDisplay.getMap().getView().getPopupManager().Update()
  }, 200)
  // detailPopup.Show();
}

const clearPopup = () => {
  LMSDisplay.m_ThreeMap.getView().getPopupManager().Clear()
  unitDetailPopup = null
}

onMounted(async () => {
  if (elGisMap.value) {
    initDom(elGisMap.value)
    const res = await getPointList()
    setLegendOptions(route.query.tab as string)
    if (res.data.unitList) {
      let _list = res.data.unitList
      searchOption.value = [...res.data.unitList]
      _list.forEach((i: any) => {
        i.bgColor = fomartLiveColor(i)
        addPointerPopup({
          ...i,
          type: route.query.tab,
          bgColor: fomartLiveColor(i),
        })
      })
      setTimeout(() => {
        LMSDisplay.getMap().getView().getPopupManager().Update()
      }, 500)
    }
  }
})
const setLegendOptions = (type: string | number | string[] | undefined | null): void => {
  if (type == '2') {
    legendOptions.value = [
      {
        imgUrl: unitLayer[1]['icon'],
        label: '重大隐患',
      },
      {
        imgUrl: unitLayer[3]['icon'],
        label: '一般隐患',
      },
      {
        imgUrl: unitLayer[0]['icon'],
        label: '无隐患',
      },
    ]
  }
  if (type == '4') {
    legendOptions.value = [
      {
        imgUrl: unitLayer[0]['icon'],
        label: '优秀',
      },
      {
        imgUrl: unitLayer[5]['icon'],
        label: '良好',
      },
      {
        imgUrl: unitLayer[3]['icon'],
        label: '及格',
      },
      {
        imgUrl: unitLayer[1]['icon'],
        label: '危险',
      },
      {
        imgUrl: unitLayer[7]['icon'],
        label: '未评估',
      },
    ]
  }
}

watch(
  () => route.query.tab,
  async (val) => {
    if (val) {
      clearPopup()
      setLegendOptions(val as string)
      const res = await getPointList()
      if (res.data.unitList) {
        let _list = res.data.unitList
        searchOption.value = [...res.data.unitList]

        _list.forEach((i: any) => {
          i.bgColor = fomartLiveColor(i)
          addPointerPopup({
            ...i,
            type: route.query.tab,
            bgColor: fomartLiveColor(i),
          })
        })
        LMSDisplay.getMap().getView().getPopupManager().Update()
      }
    }
  }
)

defineOptions({ name: 'cockpitGisIndex' })
</script>

<style lang="scss" scoped>
.gis {
  position: relative;
  width: 100%;
  height: 100%;
  background-image: url(./image/bj.png);
  background-size: 100% 100%;
  .gisMap {
    width: 100%;
    height: 100%;
  }
}
</style>
