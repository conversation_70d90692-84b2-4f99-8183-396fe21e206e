// 显示四色图配置
// gisMap3DM.setGridVisible(true);
// gridTypeIds: IndoorMap.GridAreaType.GridAreaLayerZRGK
import { createVNode, render as vRender, h } from 'vue'
// /aqsc/v1/api/v1/ehs-clnt-internetM
const base_url = import.meta.env.VITE_BASE_PREFIX
const gisConfig = {
  gis_serve_url: '/api/v3/bw-svc-enterprise-gis-service',
}
const theme_h = h
const dom = document.createElement('div')
dom.style.height = '100%'
dom.style.width = '100%'
dom.style.overflow = 'hidden'
dom.style.position = 'relative'

window.IndoorThree.init()

const mapOnLoad = () => {
  window.IndoorThree.initInspectionPlugin()
}
const CONST_GSCache = {
  adminCodeDicCache: new window.DicCache(50), //2D & 2.5D & 3DM
  indoorAreaDicCache: new window.DicCache(16), //2D & 2.5D & 3DM
  indoorAreaExtentDicCache: new window.DicCache(32), //2D & 2.5D & 3DM
  gridAreaDicCache: new window.DicCache(16), //2D & 2.5D & 3DM
  ovBuildAreaDicCache: new window.DicCache(16), //2D & 2.5D & 3DM
  ovUnitModelInfoDicCache: new window.DicCache(2), //2.5D & 3DM
  floorModelInfoDicCache: new window.DicCache(2), //2.5D & 3DM
  indoorArea3DMDicCache: new window.DicCache(4), //3DM
}
const CONST_GSOptions = {
  deviceFieldNameX: 'mapX', //设备室内GIS模式X轴坐标字段，默认值：mapX（按实际情况填写）
  deviceFieldNameY: 'mapY', //设备室内GIS模式Y轴坐标字段，默认值：mapY（按实际情况填写）
  deviceFieldNameX0: 'longitude', //设备室内图纸模式X轴坐标字段，默认值：longitude（按实际情况填写）
  deviceFieldNameY0: 'latitude', //设备室内图纸模式Y轴坐标字段，默认值：latitude（按实际情况填写）
  deviceFieldNameState: 'priorityEventType', // eventType priorityEventType
  videoBufferQueryVideoTypeCode: '25030000',
}
//
const CONST_GSOptions2: any = {
  dbService: window.newIndoorService(base_url + '/api/v3/bw-svc-indoor-gis-service/indoorMap'),
  dbService_Record: window.newIndoorService(base_url + '/api/v3/bw-svc-indoor-gis-service/record'),
  dbService_GeoSpatial: window.newIndoorService(base_url + '/api/v3/bw-svc-indoor-gis-service/GeoSpatial'),
  dbService_Analysis: window.newIndoorService(base_url + '/api/v3/bw-svc-indoor-gis-service/indoorMapAnalysis'),
  unitUrlHeader: base_url + '/img1/floorImage',
  deviceIconUrlHeader: base_url + '/img1/deviceIcons/_v3.0',
  gridTypeIds: IndoorMap.GridAreaType.GridAreaLayerFX,
  sky: false,
  deviceIconAlarmGifUrl: base_url + '/img1/deviceIcons/gif/alarm.gif',
  skyUrl: [
    base_url + '/img1/deviceIcons/z/sky/box_z/7/right.jpg',
    base_url + '/img1/deviceIcons/z/sky/box_z/7/left.jpg',
    base_url + '/img1/deviceIcons/z/sky/box_z/7/back.jpg',
    base_url + '/img1/deviceIcons/z/sky/box_z/7/front.jpg',
    base_url + '/img1/deviceIcons/z/sky/box_z/7/up.jpg',
    base_url + '/img1/deviceIcons/z/sky/box_z/7/down.jpg',
  ],
  ovUnitModelUrlHeader: base_url + '/img1/indoor',
  wmsURL: base_url + '/geoserver/GS/wms',
  deviceFieldNameState: 'priorityEventType', // eventType priorityEventType
  deviceFieldNameOnlineState: undefined,
  deviceStateValueConvertFun: window.CONST_Function_DeviceStateValueConvertFun_Default_3,
  maxAvailableZoom: 17,
  // gridLoad: false,
  // 楼栋上色
  // uiCompass: true,
  // uiCompassOptions: {
  //   //控制位置
  //   visible: true,
  //   style: {
  //     bottom: '50px',
  //     right: '50px',
  //   },
  // },
  //【3.0 使用该配置】
  videoBufferQueryVideoTypeCode: '25030000',

  // videoBufferQueryVideoTypeCode: '25030000',//【3.0 使用该配置】
  // deviceFieldNameState: 'priorityEventType', //【3.0 使用该配置】 eventType priorityEventType
  // deviceFieldNameOnlineState: undefined,//【3.0 使用该配置】
  // deviceStateValueConvertFun: CONST_Function_DeviceStateValueConvertFun_Default_3,//【3.0 使用该配置】
  deviceFieldNameX: 'mapX', //设备室内GIS模式X轴坐标字段，默认值：mapX（按实际情况填写）
  deviceFieldNameY: 'mapY', //设备室内GIS模式Y轴坐标字段，默认值：mapY（按实际情况填写）
  deviceFieldNameX0: 'mapX', //设备室内图纸模式X轴坐标字段，默认值：longitude（按实际情况填写）
  deviceFieldNameY0: 'mapY', //设备室内图纸模式Y轴坐标字段，默认值：latitude（按实际情况填写）

  deviceIconUrlFilter: function (deviceIconUrl: string, data: any) {
    if (data.deviceTypeId === this.videoBufferQueryVideoTypeCode) {
      return {
        src: this.videoBufferQueryVideoImage,
        size: this.videoBufferQueryVideoImageSize,
        anchor: this.videoBufferQueryVideoImageAnchor,
      }
    } else {
      return GISOBJ.deviceIconUrlFilter(deviceIconUrl, data)
    }
  },
}
const deviceIconUrlFilter = (deviceIconUrl: string, data: any) => {
  if (deviceIconUrl) return deviceIconUrl
  return ''
}
// window.CONST_StyleInfo_Default_Deep.value[1].fill.color = 'rgba(29, 118, 182, 1.0)';
const option = window.IndoorMap.Merge([{}, CONST_GSOptions2, CONST_GSOptions, CONST_GSCache], {
  tile: false, //底图是否可见
  sky: true, //开启天空
  target: dom,
  // tileURL: tileURL,
  maxZoom: 26,
  minZoom: 4,
  zoom: 15,
  // center: center,
  isVector: true,
  deviceIconAlarmGifUrl: '',
  multiWorld: false,
  styleInfo: window.CONST_StyleInfo_Default_Deep,
  gridLoadViewTypes: window.IndoorMap.ViewType.toViewTypeArray(), // 打开网格,
  onLoad: mapOnLoad,
})
const gisMap3DM = new window.IndoorThree(option)
;(window as any).gisMap3DM = gisMap3DM

gisMap3DM.setGridPlaneVisible(false) // 深色主题 平面图网格关闭

const getIndoorMap = () => {
  return gisMap3DM
}
const getDom = () => {
  return dom
}
const render = () => {
  gisMap3DM.render()
}
const initDom = (traget: HTMLElement) => {
  traget.appendChild(dom)
}

const showNkt = (
  params: {
    unitId: string
    aerialviewImg: string
    viewType: number
    subCenterCode: string
    pointerList?: any[]
    isShowVideo?: boolean
  },
  cb?: (item: any) => any
) => {
  clearPopup()
  let viewType = params.viewType
  if (viewType >= 0) {
    viewType = -1
  }
  return new Promise<void>((resolve, reject) => {
    gisMap3DM.setTileVisible(false)
    gisMap3DM.showFloorData(
      viewType, //数据类型
      params.unitId,
      null, //楼栋ID（鸟瞰图不填）
      '', //楼层ID（鸟瞰图不填）
      params.aerialviewImg ? base_url + '/img1/floorImage/' + params.aerialviewImg : undefined, //鸟瞰图地址
      async function (mapType: string | number, success: string) {
        const buildDataList: any = []
        // if (params.isShowPointer) {
        //   try {
        //     const buildDataRes = await queryDeviceNumsByUnitId({
        //       subCenterCode: params.subCenterCode,
        //       ownerId: params.unitId,
        //     })
        //     buildDataList = buildDataRes?.data || []
        //   } catch (error) {
        //     buildDataList = []
        //   }
        // }
        //加载第三方数据
        resolve()

        if (mapType === window.IndoorMap.ViewType.OVUnitModel) {
          gisMap3DM.setTileVisible(true)
          //模型请求成功
          gisMap3DM.showOVDataBuild(
            params.pointerList ??
              gisMap3DM.getIndoorDataState().source.unitModelInfo.map((i: any) => {
                const _build = buildDataList.find((item: any) => i.buildId == item.buildingId)
                return {
                  ...i,
                  mapX: i.modelInfoPointX,
                  mapY: i.modelInfoPointY,
                  mapZ: i.modelInfoPointZ,
                  unitId: params.unitId,
                  buildingName2: _build?.buildingName,
                  buildingId: _build?.buildingId || _build?.buildId,
                  subCenterCode: params.subCenterCode,
                }
              }),
            'mapX',
            'mapY',
            'mapZ',
            function (data: any, markFieldNameX: string, markFieldNameY: string, markFieldNameZ: string) {
              if (
                typeof data[markFieldNameX] !== 'number' ||
                typeof data[markFieldNameY] !== 'number' ||
                typeof data[markFieldNameZ] !== 'number'
              )
                return true
              // 设置mark的样式
              data.imageStyle = window.IndoorMap.StateType.Normal
              if (cb && typeof cb === 'function') {
                cb(data)
                return
              }
              // addBuildTip(data, {
              //   coms: buildPopup,
              //   name: data.modelText || data.buildingName || data.buildingName2,
              // })
            },
            false,
            -2023857
          )
        } else if (mapType === window.IndoorMap.ViewType.OVUnitImage) {
          //模型请求失败，自动切换鸟瞰图
          const fieldNameXYZ = gisMap3DM.getOVBuildFieldNameXYZ(gisMap3DM.mapType)
          const fieldNameX = fieldNameXYZ[0]
          const fieldNameY = fieldNameXYZ[1]
          const fieldNameZ = fieldNameXYZ[2]
          if (params.pointerList) {
            gisMap3DM.showOVDataBuild(
              params.pointerList ?? [],
              fieldNameX,
              fieldNameY,
              fieldNameZ,
              function (data: any, markFieldNameX: string, markFieldNameY: string, markFieldNameZ: string) {
                if (typeof data[markFieldNameX] !== 'number' || typeof data[markFieldNameY] !== 'number') return true
                // // 设置mark的样式
                // data.imageStyle = window.IndoorMap.StateType.Normal;
                if (cb && typeof cb === 'function') {
                  cb(data)
                  return
                }
                // addBuildTip(data, {
                //   coms: buildPopup,
                //   name: data.modelText || data.buildingName,
                // })
              },
              false
            )
          } else {
            gisMap3DM.showOVDataBuild(
              buildDataList ?? [],
              fieldNameX,
              fieldNameY,
              fieldNameZ,
              function (data: any, markFieldNameX: string, markFieldNameY: string, markFieldNameZ: string) {
                if (typeof data[markFieldNameX] !== 'number' || typeof data[markFieldNameY] !== 'number') return true
                if (cb && typeof cb === 'function') {
                  cb(data)
                  return
                }
                // addBuildTip(data, {
                //   coms: buildPopup,
                //   name: data.modelText || data.buildingName,
                // })
              }
            )
          }
          //   鸟瞰图视频点位撒点
        } else if (mapType === window.IndoorMap.ViewType.OVBuildArea) {
          gisMap3DM.setTileVisible(true)
          gisMap3DM.showOVDataBuild(
            params.pointerList ??
              gisMap3DM.getIndoorDataState().source.ovBuildArea.map((i: any) => {
                return {
                  ...i,
                  mapX: i.modelInfoPointX || i.mapX,
                  mapY: i.modelInfoPointY || i.mapY,
                  mapZ: i.modelInfoPointZ || i.mapZ,
                  unitId: params.unitId,
                  buildingId: i?.buildId,
                  subCenterCode: params.subCenterCode,
                }
              }),
            'mapX',
            'mapY',
            'mapZ',
            function (data: any, markFieldNameX: number, markFieldNameY: number, markFieldNameZ: number) {
              if (typeof data[markFieldNameX] !== 'number' || typeof data[markFieldNameY] !== 'number') return true
              // // 设置mark的样式
              // data.imageStyle = window.IndoorMap.StateType.Normal;

              if (cb && typeof cb === 'function') {
                cb(data)
                return
              }
              // if (name.trim()) {
              //   addBuildTip(data, {
              //     coms: buildPopup,
              //     name,
              //   })
              // }
            },
            false,
            4326
          )
        }
        if (params.isShowVideo) {
          const _videoRes = gisMap3DM.showOVDataVideoOnLoadGeoData(
            mapType, //数据类型
            success,
            base_url + '/api/v3/bw-svc-enterprise-gis-service/deviceEvent/queryVideoMonitorByPage',
            `unitId=${params.unitId}&ownerId=${params.unitId}&ownerType=0&subCenterCode=${params.subCenterCode}&pageNo=1&pageSize=-1&status=0&queryOnlineState=1`,
            'POST',
            undefined,
            function (data: any) {
              //过滤器,过滤出需要的data
              data.imageStyle = 'vline' //此处可修改图表样式
            }
          )
          _videoRes.onLoad = function () {}
        }

        //   鸟瞰图视频点位撒点
        // var extent = indoor.showOVDataVideo(resultInfo.data.rows ? resultInfo.data.rows : resultInfo.data, fieldNameX, fieldNameY, fieldNameZ, sender.dataFilterFun)
      }
    )
  })
}
const isOnload = false

const showOVDataVideo = async (videoList: any[], cb?: (item: any) => any) => {
  await gisMap3DM.showOVDataVideo(videoList, 'longitude', 'latitude', undefined, function (item: any) {
    if (cb && typeof cb === 'function') {
      cb(item)
      return
    }
    item.imageStyle = 'vline' //此处可修改图表样式
  })
}
const AppendInspectionArrayToTargetLayer = async (inspectionArray: any[]) => {
  await gisMap3DM.AppendInspectionArrayToTargetLayer(inspectionArray)
  render()
}

let zIndex = 1
// 添加楼栋详情（通过楼栋撒点 回调 调用撒点方法）
const addBuildTip = (
  data: any,
  { coms, ...other }: { coms: any; viewType?: number | string; [property: string]: any }
) => {
  zIndex = 1
  const _container = document.createElement('div')
  _container.classList.add('Build-popup')
  const p = gisMap3DM.addPopup({
    width: 32,
    height: 32,
    custom: true,
    offset: [0, -50],
    positioning: 'bottom-center',
    element: _container,
  })
  data.gisPopup = p
  p.getElement().firstChild
  p.gisData = other
  setBuildPopup({ coms: coms, options: other }, p.getElement().firstChild, p)
  let pPoint
  if (gisMap3DM.getViewType() == -3) {
    pPoint = window.IndoorMap.projectXY_FromTo([data.mapX, data.mapY], 4326, window.IndoorThree.CONST_SRID_Default)
    p.show([pPoint[0], pPoint[1], data.mapZ])
    return
  }
  if (data.mapX && data.mapY) {
    pPoint = [data.mapX, data.mapY, data.mapZ]
    p.show(pPoint)
  } else {
    pPoint = new window.GISShare.SMap.Geometry.Point(data.longitude, data.latitude)
    p.show([pPoint.getX(), pPoint.getY(), data?.mapZ || 0])
  }
  gisMap3DM.getMap().Refresh(true, true, true)
  return p
}

let upPopop: any = null
const setBuildPopup = (config: { options: any; coms: any }, buildDom: any, popup?: any) => {
  const { options, coms } = config
  const PopUpConstructor = theme_h(coms, {
    ...options,
    gisData: options,
    close: () => {
      popup.close()
    },
    updateSize: () => {
      popup.Update()
    },
    onSetShow: () => {
      const cdom = popup.getElement().querySelector('.content')
      if (cdom.style.display === 'none') {
        cdom.style.display = 'block'
        popup.getElement().querySelector('.header').classList.remove('radius')
        popup.getElement().querySelector('.img-icon').style.transform = 'rotate(-180deg)'
      } else {
        cdom.style.display = 'none'
        popup.getElement().querySelector('.header').classList.add('radius')
        popup.getElement().querySelector('.img-icon').style.transform = 'rotate(0)'
      }
      if (upPopop) {
        const dom = upPopop.getElement()?.querySelector('.content')
        if (dom && dom.getAttribute('buildingName') != cdom.getAttribute('buildingName')) {
          upPopop.getElement().querySelector('.content').style.display = 'none'
          upPopop.getElement().querySelector('.header').classList.remove('radius')
          popup.getElement().querySelector('.img-icon').style.transform = 'rotate(0)'
          upPopop.Update()
        }
      }
      upPopop = popup
      popup.gisData.isShowInfo = !popup.gisData.isShowInfo

      popup.getElement().style.zIndex = zIndex++
      popup.Update()
    },
  })

  let el: any = null
  el = document.createElement('div')
  vRender(PopUpConstructor, el)
  buildDom.appendChild(el)
  return el
}

const showFloor = (params: {
  unitId: string
  buildingId: string
  floorId?: string
  floorAreaImg?: string
  subCenterCode?: string
  deviceList?: any[]
  isShowDevicePointer?: boolean
  isFloor?: boolean
  pointerList?: any[]
}) => {
  return new Promise((resolve, reject) => {
    let res: any
    const floorData = res?.data.length ? res.data.reverse()[0] : { floorId: params.floorId }

    if (!floorData.floorId) {
      reject(false)
      console.log('🚀 ~ returnnewPromise ~ 暂无楼层信息:', floorData)
      return
    }
    clearPopup()
    gisMap3DM.showFloorData(
      window.IndoorMap.ViewType.IndoorAreaVector, //数据类型
      floorData.unitId ?? undefined, //单位id
      floorData.buildingId ?? undefined, //楼栋id
      floorData.floorId ?? '', //楼层id
      // undefined, //图纸地址 （用于查询不到室内GIS数据时自动跳转，可缺省）
      floorData?.floorAreaImg ? base_url + '/img1/floorImage/' + params?.floorAreaImg : undefined,
      async function (mapType: string, success: any, objArgs: any, indoor: any) {
        let resd: any
        const deviceList = resd?.data || params.pointerList || []
        indoor.showFloorDataDevice(deviceList ?? [], 'mapX', 'mapY', 'mapZ', function filter() {
          // 过滤条件，可对数据做修改，返回true表示去掉此条数据
          // item, markFieldNameX, markFieldNameY
          return false
        })
        resolve({
          unitId: floorData.unitId,
          buildId: floorData.buildingId,
          floorId: floorData.floorId,
          ownerId: floorData.unitId,
          subCenterCode: params.subCenterCode,
        })
      }
    )
  })
}

let devicePopup: any
let el: HTMLElement | null = null
let strHTML: any = null
strHTML = document.createElement('div')
const showFloorPopup = (data: any, { coms, ...options }: { coms: any; [property: string]: any }) => {
  if (!devicePopup) {
    devicePopup = gisMap3DM.addPopup({
      width: 32,
      height: 32,
      custom: true,
      offset: [0, -10],
      positioning: 'bottom-center',
      element: strHTML,
    })
  }

  if (el) {
    el.remove()
    el = null
  }
  const PopUpConstructor = theme_h(coms, {
    ...options,
    onClose: () => {
      devicePopup.close()
    },
    tplData: data,
  }) // 返回一个vue子类
  //创建实例并且挂载
  el = document.createElement('div')
  vRender(PopUpConstructor, el)
  strHTML.appendChild(el)
  devicePopup.show([data.mapX, data.mapY, gisMap3DM.getIndoorDataState().floorHeight || 0])
  return el
}

// 楼栋图标点击事件 start
const onOVBuildSelected = (data?: any, e?: any, obj?: any, target?: any) => {
  // console.log('🚀 ~ onOVBuildSelected ~', data, e, obj, target);
}
gisMap3DM.onOVBuildSelected = (data: any, e: any, obj: any, target: any) => {
  GISOBJ.onOVBuildSelected(data, e, obj, target)
}
// 楼栋图标点击事件 end

// 楼栋图层点击事件 start
const onOVBuildAreaSelected = (data?: any, e?: any, obj?: any, target?: any) => {
  // console.log('🚀 ~ onOVBuildSelected ~', data, e, obj, target);
}
gisMap3DM.onOVBuildAreaSelected = (data: any, e: any, obj: any, target: any) => {
  GISOBJ.onOVBuildAreaSelected(data, e, obj, target)
}
// 楼栋图层点击事件 end

// 楼层图斑点击事件
const onGridSelected = (data?: any, e?: any, obj?: any, target?: any) => {
  // console.log('🚀 ~ onOVBuildSelected ~', data, e, obj, target);
}
gisMap3DM.onGridSelected = function (data, e, obj, target) {
  GISOBJ.onGridSelected(data, e, obj, target)
}

// 设备点击事件 star
const onDeviceSelected = (data: any, e: any, obj: any, target: any) => {
  console.log('🚀 ~ data, e, obj, target:设备点击事件')
}
gisMap3DM.onDeviceSelected = (data: any, e: any, obj: any, target: any) => {
  GISOBJ.onDeviceSelected(data, e, obj, target)
  render()
}
// 设备点击事件 end

const clearPopup = () => {
  devicePopup = null
  gisMap3DM?.clearPopup()
}

const layerObj: any = {}

const creatLayer = (name: string) => {
  layerObj[name] = gisMap3DM.createCustomizeVectorLayer({
    name: name,
  })
  return layerObj[name]
}
const clearLayer = (name: string) => {
  clearPopup()
  gisMap3DM && layerObj[name] && gisMap3DM.clearCustomizeVectorLayer(layerObj[name])
}

// 添加点位
const addPointsToLayer = async (points: any[], layerName: string, cb?: (data: any) => any) => {
  // isShowLoading.value = false
  const layer = layerObj[layerName]
  if (!layer) return console.error('未找到' + layerName + '图层')
  await gisMap3DM.addPointsToCustomizeVectorLayer(layer, points, 'mapX', 'mapY', 'mapZ', function (data: any) {
    return cb && typeof cb === 'function' ? cb(data) : false
    //  setPointStyle(layerName)
  })
  render()
}

//  size?: number[], scale?: number, anchor?: number[], ...option: any
const createImageStyle = (
  src: any,
  options?: {
    size?: number[]
    scale?: number
    anchor?: number[]
    [property: string]: any
  }
) => {
  const { size, scale, anchor } = options || {}
  return window.IndoorThree.createImageStyle({
    src: src,
    anchor: anchor ?? [0.5, 1],
    scale: scale ?? 0.5,
    size: size ?? [33, 33],
    depthTest: false,
    ...option,
  })
}

const onMouseClick = (data: any[]) => {
  console.log('🚀 ~ onMouseClick ~ data:')
}
gisMap3DM.onMouseClick = (e: any) => {
  const geoObjectList: any[] = []
  for (const key in layerObj) {
    const layer = layerObj[key]
    const geoObject: any = gisMap3DM.getGeoObjectByClientXY(layer, e.getX(), e.getY())
    if (geoObject?.object && geoObject.object.gsData) {
      geoObject.object.gsData.layerName = key
    }
    geoObjectList.push(geoObject)
  }
  const gsDatas = geoObjectList.map((i: any) => {
    if (i && i.object) {
      return i.object.gsData
    }
  })
  GISOBJ.onMouseClick(gsDatas.filter((i) => i))
}
const setVisible = (layerName: string, isShow: boolean) => {
  layerObj[layerName].setVisible(isShow)
  render()
}

const AppendAlarmArrayToDeviceLayer = (pointer: any[], floorId: string) => {
  gisMap3DM.AppendAlarmArrayToDeviceLayer(pointer, floorId)
  render()
}
const setcenter = (pointer: { x: number; y: number }) => {
  gisMap3DM.setCenter(pointer)
}
const setTileVisible = (value: boolean) => {
  gisMap3DM.setTileVisible(value)
}
export const GISOBJ = {
  getIndoorMap: getIndoorMap, // 返回gis实例
  getDom, // 返回gis dom
  render, // gis 从新渲染
  initDom, // 初始化gis
  setcenter,

  // 鸟瞰图相关
  showNkt, // 显示鸟瞰图
  addBuildTip, //  添加楼栋信息
  onOVBuildSelected, //  楼栋点位点击事件
  onOVBuildAreaSelected, // 楼栋图层点击事件
  AppendInspectionArrayToTargetLayer, // 巡检
  onGridSelected, // 楼层图斑点击事件
  // 楼层图相关
  deviceIconUrlFilter, // 设备点位过滤器
  showFloor, // 显示楼层图
  showFloorPopup, // 添加楼层点位点击详情框
  onDeviceSelected, // 设备点击事件
  clearPopup, //  清楚弹框
  // 自定义图层相关
  createImageStyle, // 创建自定义图层撒点图标
  creatLayer, //  创建图层
  setVisible, // 设置创建图层显隐
  addPointsToLayer, // 添加点位到指定图层
  onMouseClick, // 图层点击事件获取数据
  clearLayer, // 清除图层
  showOVDataVideo,
  AppendAlarmArrayToDeviceLayer, // 巡检点位添加
  setTileVisible, // 控制底图显示隐藏 true false
}
