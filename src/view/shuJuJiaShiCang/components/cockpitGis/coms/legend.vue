<template>
  <div class="legend w-full grid grid-cols-1 gap-[10px]">
    <div class="flex items-center" v-for="(n, index) in options" :key="index">
      <img :src="n.imgUrl" class="w-25px h-25px mr-[10px]" alt="" />
      <div class="text-[#fff] text-[14px]">{{ n.label }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// import * as echarts from 'echarts'
import { ref, onMounted, computed, watch, reactive } from 'vue'
import { useRouter, useRoute, RouteRecordRaw } from 'vue-router'
import { Calendar, Search, CloseBold } from '@element-plus/icons-vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
const userInfo = useUserInfo()
interface ListItem {
  imgUrl: string
  label: string
}

const value = ref<string>('')
const loading = ref(false)
const activeTabs = ref('')

const props = defineProps({
  options: {
    type: Object,
    default: () => {
      return []
    },
  },
})
</script>

<style scoped lang="scss"></style>
