<template>
  <div class="leftPopup">
    <div class="part-one">
      <ChartTitle title="安全指数分布" />
      <accessUnit :dataList="accessType" :avgScore="avgScore" />
    </div>
    <div class="part-two">
      <ChartTitle title="安全指数排名" />
      <organizingPersonnel
        v-loading="loading"
        element-loading-text="加载中..."
        element-loading-background="rgba(29, 53, 100, 0.8)"
        :dataList="rankingList"
        :tabList="tabList"
        @change="change"
      />
    </div>
    <div class="part-three">
      <ChartTitle title="风险因子TOP5" />
      <regulationsRules :dataList="top5List" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import accessUnit from './components/accessUnit.vue'
import regulationsRules from './components/regulationsRules.vue'
import organizingPersonnel from './components/organizingPersonnel.vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
const loading = ref(false)
const userInfo = useUserInfo() as any
//安全指数分布
const accessType = ref<{ name: string; num: number; key: string }[]>([
  { name: '优秀', num: 0, key: 'excellent' },
  { name: '良好', num: 0, key: 'good' },
  { name: '及格', num: 0, key: 'pass' },
  { name: '危险', num: 0, key: 'danger' },
])
// 安全指数排名
const tabList = ref<{ name: string; num: number; key: string }[]>([
  { name: '低风险', num: 0, key: 'excellent' },
  { name: '中风险', num: 0, key: 'good' },
  { name: '高风险', num: 0, key: 'pass' },
  { name: '危险', num: 0, key: 'danger' },
])
const rankingList = ref<{ name: string; num: number; total: number }[]>([])
const top5List = ref<{ name: string; num: number }[]>([{ name: '', num: 0 }])
const avgScore = ref<number>(0)
const orgParams = {
  orgCode: userInfo.value.unitId,
  modelType: 'unit_base_url' as const,
}
const change = (e) => {
  querySuperviseRiskSort(e)
  querySuperviseRiskNum(e)
}

// 安全指数num
const querRiskNum = () => {
  $API
    .post({
      url: '/security/riskNum',
      params: { ...orgParams },
    })
    .then((res: any) => {
      accessType.value.forEach((i) => {
        i.num = res.data[i.key] || 0
      })
    })
    .catch((error) => {
      console.error(error)
    })
}
//
const queryLatestScore = () => {
  $API
    .post({
      url: '/security/queryLatestScore',
      params: { ...orgParams },
    })
    .then((res: any) => {
      avgScore.value = res.data.avgScore || 0
      // top5List.value = res.data
    })
    .catch((error) => {
      console.error(error)
    })
}

// 查询风险因子排行

const queryRiskIndicatorsRank = () => {
  $API
    .post({
      url: '/security/queryRiskIndicatorsRank',
      params: { ...orgParams },
    })
    .then((res: any) => {
      top5List.value =
        res.data.records.map((e) => ({
          name: e.indicatorName,
          num: e.riskNum,
        })) || []
    })
    .catch((error) => {
      console.error(error)
    })
}

// 安全指数排名

const querySuperviseRiskSort = (e) => {
  $API
    .post({
      url: e === '1' ? '/security/getNewstScoreListByCondition' : '/security/querySuperviseRiskSort',
      params: { ...orgParams, pageNo: 1, pageSize: 5 },
    })
    .then((res: any) => {
      rankingList.value = res.data.map((e) => ({
        name: e.superviseName || e.unitName,
        num: e.avgScore || e.socre,
        total: 100,
      }))
    })
    .catch((error) => {
      console.error(error)
    })
}
// 安全指数数量
const querySuperviseRiskNum = (e) => {
  loading.value = true
  $API
    .post({
      url: e === '1' ? '/security/statisticsUnitCount' : '/security/querySuperviseRiskNum',
      params: { ...orgParams, pageNo: 1, pageSize: 5 },
    })
    .then((res: any) => {
      tabList.value = [
        { name: '优秀', num: res.data.excellent, key: 'excellent' },
        { name: '良好', num: res.data.good, key: 'good' },
        { name: '及格', num: res.data.pass, key: 'pass' },
        { name: '危险', num: res.data.danger, key: 'danger' },
      ]
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}

onMounted(() => {
  console.log(userInfo.value.unitOrgType, 987)
  querySuperviseRiskSort(userInfo.value.unitOrgType)
  querySuperviseRiskNum(userInfo.value.unitOrgType)
  queryRiskIndicatorsRank()
  querRiskNum()
  queryLatestScore()
})
</script>
<style lang="scss" scoped>
.leftPopup {
  width: 468.096px;
  height: 100%;
  display: grid;
  grid-template-rows: 30% 40% 30%;
}
</style>
