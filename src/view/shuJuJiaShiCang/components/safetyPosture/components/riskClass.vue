<template>
  <div class="riskClass">
    <div class="button-tab">
      <div class="button-tab-item" :class="tab == '0' ? 'button-tab-item-select' : ''" @click="change('0')">
        过去7天
      </div>
      <div class="button-tab-item" :class="tab == '1' ? 'button-tab-item-select' : ''" @click="change('1')">
        过去30天
      </div>
      <div class="button-tab-item" :class="tab == '2' ? 'button-tab-item-select' : ''" @click="change('2')">
        过去6个月
      </div>
    </div>
    <div class="box">
      <div class="box-echarts" ref="echartsRefSituation"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { onMounted, ref, defineEmits, watch } from 'vue'
import { isFontSizeRem } from '~/common/utils'
const props = defineProps({
  dataList: {
    type: Object,
    default: () => ({}),
  },
})
const emit = defineEmits(['change'])
const tab = ref('0')
const change = (e) => {
  tab.value = e
  emit('change', e)
}
const echartsRefSituation = ref()
const initEcharts = async (e) => {
  let data = e.map((item) => item.num)
  let indicatorList = e.map((item) => {
    return {
      name: item.name,
      max: item.total,
    }
  })
  let myChart = echarts.init(echartsRefSituation.value)
  let option = {
    backgroundColor: 'transparent',
    color: ['#3572ad'],
    tooltip: {
      show: false, // 弹层数据去掉
    },
    radar: {
      radius: ['0%', '60%'],
      center: ['50%', '50%'], // 外圆的位置
      // axisName: {
      //   show: true,
      // },
      name: {
        textStyle: {
          color: '#fff',
          fontSize: isFontSizeRem(14),
          fontWeight: 400,
          fontFamily: 'PingFangSC-Regular,PingFang SC',
        },
        formatter: (event) => {
          let item = e.find((item) => item.name == event)
          let percent = ((item.num * 100) / item.total).toFixed(2)
          // return `${event}${percent}%`
          if (event == '消控室值班完成率') {
            let name1 = event.slice(0, 5)
            let name2 = event.slice(5)
            return '' + name1 + '\n' + '\n' + '' + name2 + percent + '%'
          }
          if (event == '隐患整改完成率') {
            let name1 = event.slice(0, 7)
            let name2 = event.slice(7)
            return '' + name1 + '\n' + '\n' + '' + name2 + percent + '%'
          } else {
            return `${event}${percent}%`
          }
        },
      },
      // TODO:
      indicator: indicatorList,
      splitArea: {
        // 坐标轴在 grid 区域中的分隔区域，默认不显示。
        show: true,
        areaStyle: {
          // 分隔区域的样式设置。
          color: ['#1c2330'], // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
        },
      },
      axisLine: {
        // 指向外圈文本的分隔线样式
        lineStyle: {
          color: 'rgba(255,255,255,0.2)',
        },
      },
      splitLine: {
        lineStyle: {
          type: 'solid',
          color: '#5178c7', // 分隔线颜色
          width: 1, // 分隔线线宽
        },
      },
    },
    series: [
      {
        type: 'radar',
        data: [
          {
            // TODO:
            value: data,
            name: 'a',
            areaStyle: {
              normal: {
                color: 'rgba(76,155,254,0.5)',
              },
            },
          },
        ],
      },
    ],
  }
  myChart.setOption(option)
}
onMounted(() => {
  initEcharts(props.dataList)
})

watch(
  () => props.dataList,
  () => {
    initEcharts(props.dataList)
  },
  {
    immediate: true,
    deep: true,
  }
)
</script>

<style lang="scss" scoped>
.riskClass {
  height: calc(100% - 46.08px);
  padding: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  .button-tab {
    position: absolute;
    right: 19.2px;
    top: -38.4px;
    display: flex;
    .button-tab-item {
      width: 76.8px;
      height: 38.4px;
      background: url(@/assets/image/shujujiashicang/safetyPostureModules/tab.png) no-repeat center;
      background-size: 100% 100%;
      color: #50a1ff;
      font-size: 11.52px;
      text-align: center;
      line-height: 38.4px;
      cursor: pointer;
    }
    .button-tab-item-select {
      background: url(@/assets/image/shujujiashicang/safetyPostureModules/tab-select.png) no-repeat center;
      background-size: 100% 100%;
      color: rgb(245, 245, 245);
    }
  }
  .box {
    width: 100%;
    height: 100%;
    position: relative;
    color: rgb(235, 235, 235);
    font-size: 15.36px;
    .box-echarts {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
