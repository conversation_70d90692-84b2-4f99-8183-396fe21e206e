<template>
  <div class="rules-unite">
    <div class="rules-unite-item" v-for="(item, index) in props.dataList" :key="index">
      <div class="rules-unite-item-image">
        <img v-if="index == 0" :src="risk04" alt="" style="width: 23.04px" />
        <img v-if="index == 1" :src="risk03" alt="" style="width: 23.04px" />
        <img v-if="index == 2" :src="risk02" alt="" style="width: 23.04px" />
        <img v-if="index == 3" :src="risk01" alt="" style="width: 23.04px" />
        <img v-if="index == 4" :src="risk01" alt="" style="width: 23.04px" />
        <div class="rules-unite-item-image-number">{{ index + 1 }}</div>
      </div>
      <div class="rules-unite-item-context">
        <myTooltip :str="item.name || '-'" />
      </div>
      <div class="rules-unite-item-number">{{ item.num }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import risk01 from '@/assets/image/shujujiashicang/safetyPostureModules/risk01.png'
import risk02 from '@/assets/image/shujujiashicang/safetyPostureModules/risk02.png'
import risk03 from '@/assets/image/shujujiashicang/safetyPostureModules/risk03.png'
import risk04 from '@/assets/image/shujujiashicang/safetyPostureModules/risk04.png'
const props = defineProps({
  dataList: {
    type: Object,
    default: () => ({}),
  },
})
defineOptions({ name: 'regulationsRules' })
</script>

<style lang="scss" scoped>
.rules-unite {
  width: 100%;
  height: calc(100% - 46.08px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 19.2px 0px 19.2px 19.2px;
  color: rgb(235, 235, 235);
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  .rules-unite-item {
    width: 100%;
    height: 15%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 15.36px;
    .rules-unite-item-image {
      width: 28.8px;
      height: 28.8px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      .rules-unite-item-image-number {
        position: absolute;
        left: 9.6px;
        top: 0px;
        font-size: 15.36px;
        color: rgb(235, 235, 235);
      }
    }
    .rules-unite-item-context {
      width: 307.2px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .rules-unite-item-number {
      width: 57.6px;
      height: 100%;
      display: flex;
      align-items: center;
    }
  }
}
</style>
