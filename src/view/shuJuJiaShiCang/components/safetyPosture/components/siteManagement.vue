<template>
  <div class="riskClass">
    <div class="button-tab">
      <div class="button-tab-item button-tab-item-select">设施完好率</div>
    </div>
    <div class="riskClass-echarts" ref="echartsRef"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { isFontSizeRem } from '~/common/utils'
const props = defineProps({
  dataList: {
    type: Object,
    default: () => ({}),
  },
})
const m1 = 6
const m2 = m1 * 2
const echartsRef = ref()
const initEcharts = async (e) => {
  let myChart = echarts.init(echartsRef.value)
  const CubeLeft = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0,
    },
    buildPath: function (ctx, shape) {
      // console.log('xAxisPoint[0]',xAxisPoint[0])
      const xAxisPoint = shape.xAxisPoint
      const c0 = [shape.x, shape.y]
      const c1 = [shape.x - m1, shape.y - m1]
      const c2 = [xAxisPoint[0] - m1, xAxisPoint[1] - m1]
      const c3 = [xAxisPoint[0], xAxisPoint[1]]
      ctx.moveTo(c0[0], c0[1]).lineTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).closePath()
    },
  })
  const CubeRight = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0,
    },
    buildPath: function (ctx, shape) {
      const xAxisPoint = shape.xAxisPoint
      const c1 = [shape.x, shape.y]
      const c2 = [xAxisPoint[0], xAxisPoint[1]]
      const c3 = [xAxisPoint[0] + m2, xAxisPoint[1] - m1]
      const c4 = [shape.x + m2, shape.y - m1]
      ctx.moveTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).lineTo(c4[0], c4[1]).closePath()
    },
  })
  const CubeTop = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0,
    },
    buildPath: function (ctx, shape) {
      const c1 = [shape.x, shape.y]
      const c2 = [shape.x + m2, shape.y - m1]
      const c3 = [shape.x + m1, shape.y - m2]
      const c4 = [shape.x - m1, shape.y - m1]
      ctx.moveTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).lineTo(c4[0], c4[1]).closePath()
    },
  })
  echarts.graphic.registerShape('CubeLeft', CubeLeft)
  echarts.graphic.registerShape('CubeRight', CubeRight)
  echarts.graphic.registerShape('CubeTop', CubeTop)
  const MAX = e.map((item) => item.total)
  const VALUE = e.map((item) => item.num)
  const name = e.map((item) => item.name)
  console.log('e', e)
  let option = {
    backgroundColor: 'transparent',
    grid: {
      left: '3%',
      right: '3%',
      bottom: '20%',
      top: '30%',
    },
    xAxis: {
      type: 'category',
      data: name,
      axisLine: {
        show: false,
      },
      offset: 0,
      axisTick: {
        show: false,
      },
      axisLabel: {
        formatter: function (value, index) {
          if (value.length < 5) {
            return value
          } else if (value.length < 10) {
            let name1 = value.slice(0, 5)
            let name2 = value.slice(5)
            return name1 + '\n' + name2
          } else if (value.length < 15) {
            let name1 = value.slice(0, 5)
            let name2 = value.slice(5, 10)
            let name3 = value.slice(10)
            return name1 + '\n' + name2 + '\n' + name3
          }
        },
        interval: 0,
        color: 'rgba(235, 235, 235, 1)',
        fontSize: isFontSizeRem(12),
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      // boundaryGap: ['20%', '20%']
    },
    series: [
      {
        type: 'custom',
        renderItem: function (params, api) {
          const location = api.coord([api.value(0), api.value(1)])
          return {
            type: 'group',
            children: [
              {
                type: 'CubeLeft',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: 'rgba(47,102,192,.27)',
                },
              },
              {
                type: 'CubeRight',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: 'rgba(59,128,226,.27)',
                },
              },
              {
                type: 'CubeTop',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: 'rgba(72,156,221,.27)',
                },
              },
            ],
          }
        },
        data: MAX,
      },
      {
        type: 'custom',
        renderItem: (params, api) => {
          const location = api.coord([api.value(0), api.value(1)])
          return {
            type: 'group',
            children: [
              {
                type: 'CubeLeft',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#3B80E2',
                    },
                    {
                      offset: 1,
                      color: '#49BEE5',
                    },
                  ]),
                },
              },
              {
                type: 'CubeRight',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#3B80E2',
                    },
                    {
                      offset: 1,
                      color: '#49BEE5',
                    },
                  ]),
                },
              },
              {
                type: 'CubeTop',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#3B80E2',
                    },
                    {
                      offset: 1,
                      color: '#49BEE5',
                    },
                  ]),
                },
              },
            ],
          }
        },
        data: VALUE,
      },
      {
        type: 'bar',
        label: {
          normal: {
            show: true,
            position: 'top',
            formatter: (event) => {
              console.log('event', event)
              let item = e.find((item) => item.name == event.name)
              let percent = ((item.num * 100) / item.total).toFixed(2)
              return percent + '%'
              // console.log('e',e)
              // switch (e.name) {
              //   case '10kV线路':
              //     return VALUE[0]
              //   case '公用配变':
              //     return VALUE[1]
              //   case '35kV主变':
              //     return VALUE[2]
              //   case '水':
              //
              // }
            },
            fontSize: isFontSizeRem(14),
            color: '#fff',
            offset: [4, -25],
          },
        },
        itemStyle: {
          color: 'transparent',
        },
        data: MAX,
      },
    ],
  }
  myChart.setOption(option)
}

watch(
  () => props.dataList,
  (newVal) => {
    console.log(newVal, 1)
    initEcharts(newVal)
  },
  {
    immediate: true,
    deep: true,
  }
)
</script>

<style lang="scss" scoped>
.riskClass {
  height: calc(100% - 46.08px);
  position: relative;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  .button-tab {
    position: absolute;
    right: 19.2px;
    top: -38.4px;
    display: flex;
    .button-tab-item {
      width: 76.8px;
      height: 38.4px;
      background: url(@/assets/image/shujujiashicang/safetyPostureModules/tab.png) no-repeat center;
      background-size: 100% 100%;
      color: #50a1ff;
      font-size: 11.52px;
      text-align: center;
      line-height: 38.4px;
      cursor: pointer;
    }
    .button-tab-item-select {
      background: url(@/assets/image/shujujiashicang/safetyPostureModules/tab-select.png) no-repeat center;
      background-size: 100% 100%;
      color: rgb(245, 245, 245);
    }
  }
  .riskClass-echarts {
    height: 100%;
  }
}
</style>
