<template>
  <div class="access-unite">
    <div class="access-unite-echarts" ref="echartsRef"></div>
    <div class="access-unite-list">
      <div class="access-unite-list-item" v-for="(item, index) in props.dataList" :key="index">
        <div class="access-unite-list-item-name">
          <div
            :class="['access-unite-list-item-name-circle', circleClass(index)]"
            :style="{ backgroundColor: circleColor(index) }"
          ></div>
          <div class="access-unite-list-item-name-text">{{ item.name }}</div>
        </div>
        <div class="access-unite-list-item-number">{{ item.num }}家</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, PropType } from 'vue'
import * as echarts from 'echarts'
import { isFontSizeRem } from '~/common/utils'
const props = defineProps({
  dataList: {
    type: Array as PropType<DataItem[]>,
    default: () => [],
  },
  avgScore: {
    type: Number,
    default: 0,
  },
})

interface DataItem {
  name: string
  num: number
  // 如果dataList中有total属性，请在这里添加
  // total?: number
}
const echartsRef = ref()

const circleClass = (index: number) => {
  return `circle-${index}`
}

const circleColor = (index: number) => {
  const colors = ['#09be83', '#0da8de', '#ff933b', '#f74940']
  return colors[index] || '#000'
}
const initEcharts = async (e) => {
  let data = e.map((item, index) => {
    if (index == 0) {
      return {
        value: item.num,
        name: item.name,
        itemStyle: {
          normal: {
            //颜色渐变
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#E58B44' },
              { offset: 1, color: 'rgba(229,139,68,0)' },
            ]),
          },
        },
      }
    } else if (index === 1) {
      return {
        value: item.num,
        name: item.name,
        itemStyle: {
          normal: {
            //颜色渐变
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#06A3F4' },
              { offset: 1, color: 'rgba(6,163,244,0)' },
            ]),
          },
        },
      }
    } else if (index === 2) {
      return {
        value: item.num,
        name: item.name,
        itemStyle: {
          normal: {
            //颜色渐变
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#29AF62' },
              { offset: 1, color: 'rgba(41,175,98,0)' },
            ]),
          },
        },
      }
    } else if (index === 3) {
      return {
        value: item.num,
        name: item.name,
        itemStyle: {
          normal: {
            //颜色渐变
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#FF3939' },
              { offset: 1, color: 'rgba(255,57,57,0)' },
            ]),
          },
        },
      }
    }
  })

  let level = ''
  if (Number(props.avgScore) >= 90) {
    level = '优秀'
  } else if (Number(props.avgScore) >= 80) {
    level = '良好'
  } else if (Number(props.avgScore) >= 60) {
    level = '及格'
  } else {
    level = '危险'
  }
  let myChart = echarts.init(echartsRef.value)
  let option = {
    backgroundColor: 'transparent',
    title: [
      {
        text: '平均分',
        textStyle: {
          color: '#ebebeb',
          fontSize: isFontSizeRem(14),
          align: 'center',
        },
        x: 'center',
        y: '27%',
      },
      {
        text: props.avgScore,
        textStyle: {
          color: '#00FDFF',
          fontSize: isFontSizeRem(18),
          align: 'center',
        },
        x: 'center',
        y: '40%',
      },
      {
        text: level,
        textStyle: {
          color: '#fff',
          fontSize: isFontSizeRem(15),
          align: 'center',
        },
        x: 'center',
        y: '60%',
        backgroundColor:
          level === '优秀'
            ? '#09be83'
            : level === '良好'
              ? '#0da8de'
              : level === '及格'
                ? '#ff933b'
                : level === '危险'
                  ? '#f74940'
                  : '#ebebeb',
        padding: [5, 10, 5, 10],
        borderRadius: [20, 20, 20, 20],
      },
    ],
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        // name: '总考生数量',
        type: 'pie',
        itemStyle: {
          normal: {
            borderWidth: 1,
            borderColor: 'transparent',
          },
        },
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        radius: ['65%', '100%'],
        hoverAnimation: false,
        color: ['#c487ee', '#deb140', '#49dff0', '#034079', '#6f81da', '#00ffb4'],
        data: data,
      },
    ],
  }
  myChart.setOption(option)
}
onMounted(() => {
  initEcharts(props.dataList)
})

watch(
  () => props.avgScore,
  (newVal) => {
    initEcharts(props.dataList)
  },
  {
    immediate: true,
  }
)
</script>

<style lang="scss" scoped>
.access-unite {
  padding: 10px 19.968px;
  height: calc(100% - 46.08px);
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  .access-unite-echarts {
    width: 170px;
    height: 170px;
  }
  .access-unite-list {
    width: 192px;
    height: 153.6px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-size: 15.36px;
    .access-unite-list-item {
      width: 100%;
      height: 20%;
      display: flex;
      justify-content: space-between;
      background-color: rgba(64, 153, 249, 0.2);
      align-items: center;
      padding: 0px 19.2px;
      position: relative;
      .access-unite-list-item-name {
        display: flex;
        align-items: center;
        color: #ebebeb;
        .access-unite-list-item-name-circle {
          width: 15.36px;
          height: 15.36px;
          margin-right: 19.2px;
          border-radius: 50%;
        }
      }
      .access-unite-list-item-number {
        color: #2bd9ed;
      }
    }
  }
}
</style>
