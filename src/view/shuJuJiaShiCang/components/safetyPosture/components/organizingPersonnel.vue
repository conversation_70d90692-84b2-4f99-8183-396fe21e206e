<template>
  <div class="riskClass">
    <div class="button-tab">
      <div
        class="button-tab-item"
        v-if="userInfo.unitOrgType !== '1'"
        :class="tab == '2' ? 'button-tab-item-select' : ''"
        @click="change('2')"
      >
        直管公司
      </div>
      <div class="button-tab-item" :class="tab == '1' ? 'button-tab-item-select' : ''" @click="change('1')">
        基层公司
      </div>
    </div>
    <div class="riskClass-top">
      <div class="riskClass-top-item" :class="getItemClass(index)" v-for="(item, index) in props.tabList" :key="index">
        <div class="riskClass-top-item-top">{{ item.name }}</div>
        <div class="riskClass-top-item-bottom">{{ item.num }}家</div>
      </div>
    </div>
    <div class="riskClass-main">
      <div class="riskClass-main-item" v-for="(item, index) in props.dataList" :key="index">
        <div class="riskClass-main-item-text">
          <span style="margin-right: 9.6px">{{ index + 1 }}</span>
          {{ item.name }}
        </div>
        <div class="riskClass-main-item-box">
          <div class="riskClass-main-item-box-son">
            <div
              class="riskClass-main-item-box-son-strip"
              :style="{ width: (item.num / item.total) * 100 + '%' }"
            ></div>
          </div>
          <div class="riskClass-main-item-box-son-percent">{{ item.num }}分</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useUserInfo } from '~/store'
const userInfo = useUserInfo() as any
interface TabItem {
  name: string
  num: number
}

interface DataItem {
  name: string
  num: number
  total: number
}
const props = defineProps({
  tabList: {
    type: Array as () => TabItem[],
    default: () => [],
  },
  dataList: {
    type: Array as () => DataItem[],
    default: () => [],
  },
})
const emit = defineEmits(['change'])
const tab = ref(userInfo.value.unitOrgType === '1' ? '1' : '2')
const change = (e) => {
  tab.value = e
  emit('change', e)
}

const getItemClass = (index: number) => {
  const classes = ['bgc-04', 'bgc-03', 'bgc-02']
  return classes[index % classes.length] || ''
}
</script>

<style lang="scss" scoped>
.riskClass {
  width: 100%;
  height: calc(100% - 46.08px);
  box-sizing: border-box;
  padding: 19.2px;
  position: relative;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  .button-tab {
    position: absolute;
    right: 19.2px;
    top: -38.4px;
    display: flex;
    .button-tab-item {
      width: 76.8px;
      height: 38.4px;
      background: url(@/assets/image/shujujiashicang/safetyPostureModules/tab.png) no-repeat center;
      background-size: 100% 100%;
      color: #50a1ff;
      font-size: 11.52px;
      text-align: center;
      line-height: 38.4px;
      cursor: pointer;
    }
    .button-tab-item-select {
      background: url(@/assets/image/shujujiashicang/safetyPostureModules/tab-select.png) no-repeat center;
      background-size: 100% 100%;
      color: rgb(245, 245, 245);
    }
  }
  .riskClass-top {
    display: flex;
    justify-content: space-between;
    .riskClass-top-item {
      width: 22%;
      height: 57.6px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      box-sizing: border-box;
      padding: 7.68px;
      color: rgb(235, 235, 235);
      font-size: 15.36px;
      background: url('@/assets/image/shujujiashicang/safetyPostureModules/bgc-04.png') no-repeat;
      background-size: 100% 100%;
    }
    .bgc-02 {
      background: url('@/assets/image/shujujiashicang/safetyPostureModules/bgc-03.png') no-repeat;
      background-size: 100% 100%;
    }
    .bgc-03 {
      background: url('@/assets/image/shujujiashicang/safetyPostureModules/bgc-02.png') no-repeat;
      background-size: 100% 100%;
    }
    .bgc-04 {
      background: url('@/assets/image/shujujiashicang/safetyPostureModules/bgc-01.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  .riskClass-main {
    height: calc(100% - 57.6px);
    margin-top: 19.2px;
    overflow: auto;
    .riskClass-main-item {
      width: 100%;
      height: 57.6px;
      margin-bottom: 5.76px;
      .riskClass-main-item-text {
        font-size: 15.36px;
        color: rgb(235, 235, 235);
        line-height: 28.8px;
        width: 100%;
        height: 50%;
      }
      .riskClass-main-item-box {
        width: 100%;
        height: 50%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .riskClass-main-item-box-son {
          width: calc(100% - 57.6px);
          height: 13.44px;
          background-color: rgba(63, 130, 255, 0.1);
          border-radius: 19.2px;
          .riskClass-main-item-box-son-strip {
            width: 50%;
            height: 100%;
            background: linear-gradient(90deg, #3f82ff 0%, #45f5e1 100%);
            border-radius: 19.2px;
          }
        }
        .riskClass-main-item-box-son-percent {
          width: 42.24px;
          height: 19.2px;
          color: rgb(235, 235, 235);
          font-size: 15.36px;
          text-align: left;
          line-height: 19.2px;
        }
      }
    }
  }
}

div::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  /**/
}
div::-webkit-scrollbar-track {
  background: rgba(63, 130, 255, 0.24);
  border-radius: 2px;
}
div::-webkit-scrollbar-thumb {
  background: #4674d2;
  border-radius: 10px;
}
div::-webkit-scrollbar-thumb:hover {
  background: #3f82ff;
}
div::-webkit-scrollbar-corner {
  background: #179a16;
}
</style>
