<template>
  <div class="riskClass">
    <div class="button-tab">
      <div
        v-for="tabItem in tabItems"
        :key="tabItem.value"
        :class="['button-tab-item', { 'button-tab-item-select': tab === tabItem.value }]"
        @click="change(tabItem.value)"
      >
        {{ tabItem.label }}
      </div>
    </div>
    <div v-for="(item, index) in list" :key="index" class="riskClass-item">
      <div class="riskClass-item-left">{{ item.name }}</div>
      <div class="riskClass-item-middle">
        <div class="riskClass-item-middle-text">已完成任务数：{{ item.num }}</div>
        <div class="riskClass-item-middle-percent">
          <div
            :class="['riskClass-item-middle-percent-percentage', getPercentageClass(index)]"
            :style="{ width: getPercentageWidth(item) }"
          ></div>
        </div>
      </div>
      <div class="riskClass-item-right">
        <div class="riskClass-item-right-top">{{ getRightTopLabel(index) }}</div>
        <div class="riskClass-item-right-bottom">{{ item.rate }}%</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, watch, PropType } from 'vue'

const props = defineProps({
  dataList: {
    type: Array as PropType<RiskItem[]>,
    default: () => [],
  },
})

type RiskItem = {
  name: string
  num: number
  total: number | string
  key: string
  rate?: number | string
}

type TabItem = {
  value: string
  label: string
}

const emit = defineEmits(['change'])
const list = ref(props.dataList)
const tab = ref('0')
const tabItems: TabItem[] = [
  { value: '0', label: '过去7天' },
  { value: '1', label: '过去30天' },
  { value: '2', label: '过去6个月' },
]

const change = (e: string) => {
  tab.value = e
  emit('change', e)
}

const getPercentageWidth = (item: RiskItem) => {
  return ((item.num * 100) / Number(item.total)).toFixed(0) + '%'
}

const getPercentageValue = (item: RiskItem) => {
  return item.num ? ((Number(item.num) * 100) / Number(item.total)).toFixed(2) : '0'
}

const getPercentageClass = (index: number) => {
  const classes = ['bgc-01', 'bgc-02', 'bgc-03']
  return classes[index] || ''
}

const getRightTopLabel = (index: number) => {
  const labels = ['任务规范率', '打卡规范率', '规范率']
  return labels[index] || ''
}

watch(
  () => props.dataList,
  () => {
    list.value = props.dataList
  },
  {
    immediate: true,
    deep: true,
  }
)
</script>

<style lang="scss" scoped>
.riskClass {
  height: calc(100% - 46.08px);
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  box-sizing: border-box;
  padding: 9.6px;
  color: rgb(235, 235, 235);
  font-size: 15.36px;
  position: relative;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  .button-tab {
    position: absolute;
    right: 19.2px;
    top: -38.4px;
    display: flex;
    .button-tab-item {
      width: 76.8px;
      height: 38.4px;
      background: url(@/assets/image/shujujiashicang/safetyPostureModules/tab.png) no-repeat center;
      background-size: 100% 100%;
      color: #50a1ff;
      font-size: 11.52px;
      text-align: center;
      line-height: 38.4px;
      cursor: pointer;
    }
    .button-tab-item-select {
      background: url(@/assets/image/shujujiashicang/safetyPostureModules/tab-select.png) no-repeat center;
      background-size: 100% 100%;
      color: rgb(245, 245, 245);
    }
  }
  .riskClass-item {
    width: 100%;
    height: 18%;
    background: url(@/assets/image/shujujiashicang/safetyPostureModules/background.png) no-repeat center;
    background-size: 100% 100%;
    display: flex;
    font-size: 15.36px;
    position: relative;
    &::before {
      content: '';
      display: block;
      width: 1.92px;
      height: 19.2px;
      background-color: #eab10e;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 0px;
    }
    .riskClass-item-left {
      width: 30%;
      height: 100%;
      box-sizing: border-box;
      border-right: 1px solid #15356f;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .riskClass-item-middle {
      width: 46%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .riskClass-item-middle-text {
        color: rgb(235, 235, 235);
        margin-bottom: 7.68px;
      }
      .riskClass-item-middle-percent {
        width: 80%;
        height: 13.44px;
        background-color: rgba(63, 130, 255, 0.1);
        border-radius: 19.2px;
        .riskClass-item-middle-percent-percentage {
          width: 50%;
          height: 100%;
          background: linear-gradient(90deg, #3f82ff 0%, #45f5e1 100%);
          border-radius: 19.2px;
        }
        .bgc-01 {
          background: linear-gradient(90deg, #179c90 0%, #37ec96 100%);
        }
        .bgc-02 {
          background: linear-gradient(90deg, #d09108 0%, #f5cd45 100%);
        }
        .bgc-03 {
          background: linear-gradient(90deg, #861cea 0%, #b129ff 100%);
        }
      }
    }
    .riskClass-item-right {
      width: 24%;
      height: 100%;
      box-sizing: border-box;
      border-left: 1px solid #15356f;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: rgb(235, 235, 235);
    }
  }
}
</style>
