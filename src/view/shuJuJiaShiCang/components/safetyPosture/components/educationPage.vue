<template>
  <div class="educationPagg">
    <div class="left">
      <div class="complateRate">
        <InstrumentPanel />
      </div>
    </div>
    <div class="right">
      <div class="right-top">
        <div v-for="item in educationInfo.list" :key="item.typeName" class="right-top-item">
          <div class="right-top-item-title">{{ item.typeName }}</div>
          <div class="right-top-item-content">{{ item.num }}</div>
        </div>
      </div>
      <div class="right-bottom">
        <div v-for="item in educationInfo.imgList" :key="item.typeName" class="exam-list">
          <div class="right-bottom-content">
            <img :src="item.imgUrl" alt="" />
            <div class="state"></div>
          </div>
          <div class="right-bottom-title">{{ item.typeName }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import InstrumentPanel from '../../instrumentPanel.vue'
const props = defineProps({
  educationInfo: {
    type: Object,
    default: () => ({}),
  },
})
</script>

<style lang="scss" scoped>
.educationPagg {
  height: calc(100% - 2.4vw);
  display: grid;
  grid-template-columns: 6.77vw 1fr;
  background: url(@/assets/image/shujujiashicang/safetyManage/item-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #fff;
  .left {
    width: 6.77vw;
    padding: 0 0.26vw;
    display: flex;
    align-items: center;
    .complateRate {
      width: 6.25vw;
      height: 6.25vw;
    }
  }
  .right {
    padding: 0.52vw 0 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-rows: repeat(2, 1fr);
    .right-top {
      display: flex;
      justify-content: space-around;
      .right-top-item {
        font-size: 0.73vw;
      }
      .right-top-item-content {
        width: 5.47vw;
        height: 4.06vw;
        background: url(@/assets/image/shujujiashicang/safetyManage/task-bg.png) no-repeat center/cover;
        font-weight: bold;
        font-size: 1.25vw;
        color: #ffffff;
        text-align: center;
      }
      .right-top-item-title {
        text-align: center;
        font-size: 0.73vw;
      }
    }
    .right-bottom {
      font-size: 0.63vw;
      display: flex;
      justify-content: space-around;
      .exam-list {
        // background-color: #fff;
        width: 5.41vw;
        height: 4.69vw;
        display: flex;
        flex-direction: column;
        align-items: center;

        .right-bottom-content {
          flex: 1;
          width: 100%;
          background: url('../../../../../assets/image/shujujiashicang/safetyManage/temp.png') no-repeat center/cover;
        }
        .right-bottom-title {
          margin: 0.26vw 0;
          width: 100%;
          // 溢出...表示
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
      }
    }
  }
}
</style>
