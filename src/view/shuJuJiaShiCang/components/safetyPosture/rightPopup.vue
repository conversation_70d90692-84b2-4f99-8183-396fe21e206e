<template>
  <div class="leftPopup h-full">
    <div class="part-one h-[calc(33%-7px)]">
      <ChartTitle title="工作执行态势" />
      <riskClass :dataList="accessType" @change="change" />
    </div>
    <div class="part-two h-[calc(33%-7px)]">
      <ChartTitle title="工作执行质量态势" />
      <hiddenDanger
        v-loading="loading"
        element-loading-text="加载中..."
        element-loading-background="rgba(29, 53, 100, 0.8)"
        :dataList="stateList"
        @change="changeType"
      />
    </div>
    <div class="part-three h-[calc(33%-7px)]">
      <ChartTitle title="设施运行态势" />
      <siteManagement
        v-loading="loadingList"
        element-loading-text="加载中..."
        element-loading-background="rgba(29, 53, 100, 0.8)"
        :dataList="list"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import riskClass from './components/riskClass.vue'
import hiddenDanger from './components/hiddenDanger.vue'
import siteManagement from './components/siteManagement.vue'

interface Item {
  name: string
  num: number
  total: number | string
  key: string
  rate?: number | string
}
const loadingList = ref(false)
const userInfo = useUserInfo()
//工作执行态势
const accessType = ref<Item[]>([
  { name: '火警处置完成率', num: 0, total: 100, key: 'alarmDisposeRate' },
  { name: '隐患整改完成率', num: 0, total: 100, key: 'hazardDisposeRate' },
  { name: '安全检查完成率', num: 0, total: 100, key: 'completePlanTaskRate' },
  { name: '消控室值班完成率', num: 0, total: 100, key: 'fireControlRoomRate' },
])
const loading = ref(false)
const orgParams = {
  orgCode: userInfo.value.unitId,
  modelType: 'unit_base_url' as const,
}

//工作执行质量态势
const stateList = ref<Item[]>([])

//工作执行质量态势
const list = ref([]) as any

const change = (e: number): void => {
  querySuperviseRiskSort(e)
}
const changeType = (e: number): void => {
  querySuperviseRiskSortQuality(e)
}
// 工作执行态势

const querySuperviseRiskSort = (timeRange: number) => {
  $API
    .post({
      url: '/security/queryWorkExecutionPosture',
      params: { ...orgParams, timeRange },
    })
    .then((res: any) => {
      accessType.value.forEach((e) => {
        e.num = Number(res.data[e.key])
      })
    })
    .catch((error) => {
      console.error(error)
    })
}

// 工作执行质量态势
const querySuperviseRiskSortQuality = (timeRange: number) => {
  loading.value = true
  $API
    .post({
      url: '/security/queryWorkExecutionPosture',
      params: { ...orgParams, timeRange },
    })
    .then((res: any) => {
      stateList.value = [
        { name: '安全检查', num: 0, total: 'allHazardNum', key: 'hazardDisposeNum', rate: 'hazardDisposeRate' },
        {
          name: '消控室值班',
          num: 0,
          total: 'fireControlTotal',
          key: 'fireCompletedNum',
          rate: 'standardRate',
        },
        { name: '火警处置', num: 0, total: 'allAlarmNum', key: 'alarmDisposeNum', rate: 'regularRate' },
      ]
      stateList.value.forEach((e: any) => {
        e.num = Number(res.data[e.key])
        e.rate = Number(res.data[e.rate])
        e.total = Number(res.data[e.total])
      })
      loading.value = false
    })
    .catch((error) => {
      loading.value = false
      console.error(error)
    })
}
// 设施运行态势
const getIntactRate = () => {
  loadingList.value = true
  $API
    .post({
      url: '/security/getIntactRate',
      params: {
        orgCode: userInfo.value.unitId,
        modelType: 'unit_base_url',
      },
    })
    .then((res: any) => {
      list.value = res.data.analysisList.map((e) => ({
        name: e.name,
        num: Number(e.intactRate),
        total: 100,
      }))
      loadingList.value = false
    })
    .catch((error) => {
      loadingList.value = false
      console.error(error)
    })
}
onMounted(() => {
  querySuperviseRiskSort(0)
  getIntactRate()
  querySuperviseRiskSortQuality(0)
})
</script>
