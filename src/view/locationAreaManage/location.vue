<template>
  <div class="location-box">
    <iframe :src="url" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import config from '~/config'
const url = ref(config.locationAreaUrl + window.location.hash)
defineOptions({ name: 'locationIndex' })
</script>

<style scoped lang="scss">
.location-box {
  width: 100%;
  height: 100%;

  iframe {
    width: 100%;
    height: 100%;
  }
}
</style>
