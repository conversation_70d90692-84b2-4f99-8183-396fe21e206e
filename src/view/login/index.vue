<template>
  <div :class="$style['page-login']">
    <div :class="$style['header']">
      <div :class="$style['page-login-title']">
        <!--        <img class="w-[60px] h-[60px]" src="./assets/header-logo.png" alt="header-logo" />-->
      </div>
    </div>
    <div :class="$style['page-login-wrap']">
      <div :class="$style['login-form']">
        <login />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import login from './comp/login.vue'

defineOptions({ name: 'loginIndex' })
</script>

<style module lang="scss">
.page-login {
  background: url('./assets/login.png');
  height: 100vh;
  background-size: cover;
}

.header {
  position: absolute;
  width: 100%;
  height: 77px;
  top: 0;
  background-image: url('./assets/header.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.page-login-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
}

.page-login-title {
  position: absolute;
  top: 9px;
  left: 36px;
  display: flex;
}

.login-form {
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
}
</style>
