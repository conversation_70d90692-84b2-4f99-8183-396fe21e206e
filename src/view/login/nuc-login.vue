<template>
  <div :class="$style['page-login']">
    <div :class="$style['header']">
      <div :class="$style['page-login-title']"></div>
    </div>
    <div :class="$style['page-login-wrap']">
      <div :class="$style['login-form']">
        <div class="login-box">
          <div v-if="islogin">
            <div class="title">
              <div class="welcome-login">欢迎登录</div>
            </div>
            <div
              class="login-form h-380px w-375px m-auto"
              v-loading="loading"
              element-loading-text="自动登录中..."
              element-loading-background="rgba(0, 0, 0, 0)"
            ></div>
          </div>
          <div v-else>
            <div class="w-login-opt">
              <el-form :model="form" class="h-[100px]" :rules="rules" ref="formRef">
                <el-form-item label="" class="w-full" prop="unitId">
                  <el-select
                    v-model="form.unitId"
                    placeholder="请选择单位"
                    size="large"
                    style="width: 100%"
                    clearable
                    class="input-border-style"
                  >
                    <el-option v-for="item in options" :key="item.unitId" :label="item.unitName" :value="item.unitId" />
                  </el-select>
                </el-form-item>
              </el-form>
              <div class="flex justify-center items-center">
                <el-button type="primary" class="w-150px" @click="unitSubmit">确定</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import $API from '@/common/api'
import { useUserInfo } from '~/store'
import router from '~/router'
import { useRoute } from 'vue-router'
import type { FormRules } from 'element-plus'

const islogin = ref<boolean>(true)
const loading = ref(true)

const ui = useUserInfo()
let formRef = ref()
const route = useRoute()
const rules = reactive<FormRules<any>>({
  unitId: [{ required: true, message: '请选择单位', trigger: ['change', 'blur'] }],
})
const form = ref({
  unitId: '',
})
const options = ref<any[]>([])

function submit() {
  loading.value = true
  $API
    .get({
      url: '/atomic-upms-service/power/login/v1/getTokenLoginInfoByZssx',
    })
    .then(async (res: any) => {
      loading.value = false
      if (res && res.code == '200') {
        const d = res?.data
        ui.value = d
        ui.value.systemName = d?.systemName || '延长石油'
        localStorage.setItem('@@web_userInfo', JSON.stringify(ui.value))
        sessionStorage.setItem('@@web_userInfo', JSON.stringify({ userInfo: { ...ui.value } }))
        getAuthList()
      } else {
        ElMessage.error(res?.message || '获取用户信息失败')
      }
    })
}

function getAuthList() {
  loading.value = true
  $API
    .post({
      url: `ehs-clnt-platform-service/login/getUserAuthUit`,
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        options.value = res.data
        if (res.data.length <= 1) {
          islogin.value = true
          $API
            .post({
              url: 'train-server/login/getUnitDetailInfoWithToken',
              params: {
                orgCode: options.value[0].unitId,
                client: 'platform',
              },
            })
            .then((data: any) => {
              if (data && data.code === 'success') {
                ui.value = {} as any
                const d = data.data
                d.resourceVoList = data?.data?.resourceVoList.map((item) => {
                  return {
                    ...item,
                    children: item.childrens,
                  }
                })
                ui.value = d
                ui.value.resourceList = d?.resourceVoList
                ui.value.systemName = d?.systemName || '延长石油'
                localStorage.setItem('@@web_userInfo', JSON.stringify(ui.value))
                sessionStorage.setItem('@@web_userInfo', JSON.stringify({ userInfo: { ...ui.value } }))
                router.push('/staging')
              }
            })
        } else {
          islogin.value = false
        }
      }
    })
    .finally(() => {
      loading.value = false
    })
}

async function unitSubmit() {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (!valid) return
    loading.value = true
    $API
      .post({
        url: 'train-server/login/getUnitDetailInfoWithToken',
        params: {
          orgCode: form.value.unitId,
          client: 'platform',
        },
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          const d = res.data
          d.resourceVoList = res?.data?.resourceVoList.map((item) => {
            return {
              ...item,
              children: item.childrens,
            }
          })
          ui.value = d
          ui.value.resourceList = d?.resourceVoList
          ui.value.systemName = d?.systemName || '延长石油'
          localStorage.setItem('@@web_userInfo', JSON.stringify(ui.value))
          sessionStorage.setItem('@@web_userInfo', JSON.stringify({ userInfo: { ...ui.value } }))
          router.push('/staging')
        }
      })
      .finally(() => {
        loading.value = false
      })
  })
}

onMounted(() => {
  submit()
})
</script>

<script lang="ts">
export default {}
</script>

<style module lang="scss">
.page-login {
  background: url('./assets/login.png');
  height: 100vh;
  background-size: cover;
}

.header {
  position: absolute;
  width: 100%;
  height: 77px;
  top: 0;
  background-image: url('./assets/header.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.page-login-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
}

.page-login-title {
  position: absolute;
  top: 9px;
  left: 36px;
  display: flex;
}

.login-form {
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
}
</style>
<style lang="scss">
.login-box {
  width: 581px;
  height: 531px;
  padding: 44px;
  background-image: url('./assets/login-form.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 8px;
  color: #333;
  right: 10%;

  .captchaImg {
    width: 100px;
    height: 50px;
    margin-left: 30px;
    cursor: pointer;
  }

  .login-form {
    // margin-top: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .w-login-opt {
    height: 409px;
    width: 440px !important;
    margin: 30px auto;
    //background-color: chocolate;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .el-select__wrapper {
    background: rgba(0, 55, 97, 0.41);
    border-radius: 2px;
    border: 1px solid #119cca;
    box-shadow: 0 2px 4px rgba(17, 156, 202, 0.3);
    font-weight: 400;
    font-size: 16px;
    color: #4998c8;
    opacity: 0.76;
  }

  .title {
    background-image: url('./assets/login-title.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;

    .welcome-login {
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: MyCustomFont, sans-serif;
      font-weight: 400;
      font-size: 36px;
      color: #d3dce5;
      background: linear-gradient(0deg, #00aeff 0%, #ffffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .title-left {
    margin-bottom: 2px;
    font-family:
      Microsoft YaHei,
      serif;
    font-weight: 400;
    font-size: 16px;
    color: #e6efff;
    opacity: 0.76;
  }

  .el-input {
    margin-bottom: 10px;

    .el-input__inner {
      height: 48px;
      font-weight: 400;
      font-size: 16px;
      color: #4998c8;
      opacity: 0.76;
    }

    // 改变input框背景颜色
    :deep(.el-input__inner) {
      background-color: #4998c8 !important;
      border: 1px solid #1296db;
    }

    .el-input__wrapper {
      background: rgba(0, 55, 97, 0.41);
      border-radius: 2px;
      border: 1px solid #119cca;
      box-shadow: 0 2px 4px rgba(17, 156, 202, 0.3);
      font-weight: 400;
      font-size: 16px;
      color: #4998c8;
      opacity: 0.76;
    }
  }

  .el-button {
    width: 100%;
    height: 48px;
    background: linear-gradient(0deg, rgba(2, 81, 137, 0.4), rgba(0, 178, 199, 0.4));
    border-radius: 2px;

    > span {
      font-size: 20px;
    }
  }

  input {
    color: 4998c8;
  }

  input:-webkit-autofill {
    -webkit-text-fill-color: #fff;
    transition: background-color 50000s ease-in-out 0s;
    caret-color: #4998c8;
    color: #4998c8;
  }

  /* 修改 Placeholder 颜色为灰色 */
  input::placeholder {
    color: #4998c8;
  }

  input::-webkit-input-placeholder {
    /* WebKit, Blink, Edge */
    color: #4998c8;
  }

  input:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #4998c8;
  }

  input::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #4998c8;
  }

  input:-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: #4998c8;
  }
}

.custom-checkbox {
  .el-checkbox__inner {
    background: rgba(0, 55, 97, 0.5);
    border-radius: 2px;
    border: 1px solid #119cca;

    :after {
      border-color: var(#075068);
      transform: rotate(45deg) scaleY(1);
    }
  }

  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #075068;
    border-color: var(--el-checkbox-checked-input-border-color);
  }
}
</style>
