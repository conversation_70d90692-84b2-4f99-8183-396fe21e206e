import type { TypeStorage } from './types'
import { isNil, map } from './_utils'
import CryptoJS from 'crypto-js'
import CRYPTOJS_ENC_UTF8 from 'crypto-js/enc-utf8'

export const LocalStorage: TypeStorage = {
  has: (key: string) => {
    const item = window.localStorage.getItem(key)
    return !isNil(item)
  },
  json: <T>() => window.localStorage as unknown as T,
  deleteAll: () => {
    map(window.localStorage, LocalStorage.delete)
  },
  get: <T>(key: string) => {
    const str = window.localStorage.getItem(key)
    try {
      return JSON.parse(str as never) as T
    } catch (error) {
      return str
    }
  },
  delete: (key) => {
    window.localStorage.removeItem(key)
  },
  set: (key, object) => {
    window.localStorage.setItem(key, JSON.stringify(object))
  },
}

/**
 * AES解密
 * @param cipherText
 * @param secretKey
 */
export function decryptAes(cipherText: string, secretKey: string) {
  const bytes = CryptoJS.AES.decrypt(cipherText, secretKey)
  return bytes.toString(CRYPTOJS_ENC_UTF8)
}

/**
 * AES加密
 * @param originalText
 * @param secretKey
 */
export function encryptAes(originalText: string, secretKey: string) {
  return CryptoJS.AES.encrypt(originalText, secretKey).toString();
}
