<template>
  <div class="login-box relative">
    <div v-if="islogin" class="h-full">
      <div class="title">
        <div class="welcome-login">欢迎登录</div>
      </div>
      <div class="login-form">
        <div class="w-375px">
          <!-- 登录方式切换 -->
          <div>
            <el-tabs v-model="loginMethod" class="login-tabs">
              <el-tab-pane label="账号密码登录" name="password">
                <div class="title-left">账号</div>
                <el-input class="input-border-style" v-model="loginName" @keyup.enter="submit" placeholder="请输入账号">
                  <template #prefix>
                    <img :src="sjhIcon" class="input-icon" alt="手机号图标" />
                  </template>
                </el-input>

                <div class="title-left">密码</div>
                <el-input
                  v-model="password"
                  @keyup.enter="submit"
                  type="password"
                  show-password
                  autocomplete="new-password"
                  placeholder="请输入密码"
                >
                  <template #prefix>
                    <img :src="maIcon" class="input-icon" alt="密码图标" />
                  </template>
                </el-input>

                <div class="title-left">验证码</div>
                <div style="display: flex">
                  <el-input v-model="captcha" @keyup.enter="submit" placeholder="请输入图形验证码">
                    <template #prefix>
                      <img :src="YzmIcon" class="input-icon" alt="验证码图标" />
                    </template>
                  </el-input>
                  <img :src="captchaImg" alt="" class="captchaImg" @click="refreshCaptcha" />
                </div>

                <!-- 记住登录状态 -->
                <div class="h-[30px]">
                  <el-checkbox class="custom-checkbox" v-model="isRememberInfo">
                    <span class="text-[#E6EFFF]">记住登录状态</span>
                  </el-checkbox>
                </div>
              </el-tab-pane>

              <el-tab-pane label="手机号登录" name="sms">
                <div class="title-left">手机号</div>
                <el-input v-model="phone" @keyup.enter="submit" placeholder="请输入手机号">
                  <template #prefix>
                    <img :src="sjhIcon" class="input-icon" alt="手机号图标" />
                  </template>
                </el-input>

                <div class="title-left">验证码</div>
                <div style="display: flex">
                  <el-input v-model="captcha" @keyup.enter="submit" placeholder="请输入图形验证码">
                    <template #prefix>
                      <img :src="YzmIcon" class="input-icon" alt="验证码图标" />
                    </template>
                  </el-input>
                  <img :src="captchaImg" alt="" class="captchaImg" @click="refreshCaptcha" />
                </div>
                <div class="title-left">短信验证码</div>
                <div class="flex">
                  <el-input v-model="smsCode" @keyup.enter="submit" placeholder="请输入短信验证码">
                    <template #prefix>
                      <img :src="smsIcon" class="input-icon" alt="短信图标" />
                    </template>
                  </el-input>
                  <el-button class="sms-code-btn" :disabled="smsCodeTimer > 0" @click="getSmsCode" size="small">
                    {{ smsCodeTimer > 0 ? `${smsCodeTimer}s后重新获取` : '发送验证码' }}
                  </el-button>
                </div>
                <div class="h-[30px]"></div>
              </el-tab-pane>
            </el-tabs>
          </div>
          <!-- 登录按钮 -->
          <div>
            <el-button :loading="loading" class="login-button" type="primary" @click="submit"> 登录 </el-button>
          </div>

          <!-- 服务协议和隐私协议 -->
          <div class="agreement-section flex items-center justify-center">
            <el-checkbox v-model="agreeToTerms" class="custom-checkbox" />
            <div class="flex items-center ml-[10px]">
              <span class="text-[#E6EFFF] pointer-events-none">我已阅读并同意</span>
              <a href="https://agjp.tanzervas.com/aqsc/v1/html/wych/yhxy.html" target="_blank" class="agreement-link"
                >服务协议</a
              >
              <span class="text-[#E6EFFF] pointer-events-none">和</span>
              <a href="https://agjp.tanzervas.com/aqsc/v1/html/wych/ysxy.html" target="_blank" class="agreement-link"
                >隐私协议</a
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <div class="w-login-opt">
        <el-form :model="form" class="h-[100px]" :rules="rules" ref="formRef">
          <el-form-item label="" class="w-full" prop="unitId">
            <el-select
              v-model="form.unitId"
              placeholder="请选择单位"
              size="large"
              style="width: 100%"
              clearable
              class="input-border-style"
            >
              <el-option v-for="item in options" :key="item.unitId" :label="item.unitName" :value="item.unitId" />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="flex justify-center items-center">
          <el-button type="primary" class="w-150px" @click="unitSubmit">确定</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, reactive, watchEffect } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UserFilled, Lock } from '@element-plus/icons-vue'
import { hex_md5 } from '@/common/md5'
import $API from '@/common/api'
import { useUserInfo } from '~/store'
import router from '~/router'
import { LocalStorage, decryptAes, encryptAes } from './local-storage'
import type { FormRules } from 'element-plus'
import YzmIcon from '../assets/yzm.png'
import sjhIcon from '../assets/sjh.png'
import maIcon from '../assets/ma.png'
import smsIcon from '../assets/sms.png'
import { nextTick } from 'process'
// 账号缓存信息
interface LoginCacheType {
  n?: string // 账号
  p?: string // 密码
  // r?: string // 验证码
}

const islogin = ref<boolean>(true)
const secretKey = 'XYZDZHA'
const loginCacheKey = 'SXZD_FSMS_LOGIN_REM'
const loginCacheRes: LoginCacheType = getLoginCache()
const loginName = ref(loginCacheRes.n || null)
const password = ref(loginCacheRes.p || null)
const captcha = ref<string | null>('')
const loading = ref(false)
const ui = useUserInfo()
const isRememberInfo = ref(!!loginCacheRes.n) // 有缓存默认勾选
let formRef = ref()
const captchaImg = ref<string>('')
const phone = ref('')
const smsCode = ref('')
const smsCodeTimer = ref(0) // 短信验证码倒计时
const loginMethod = ref('password')
const agreeToTerms = ref(false)
const captchaKey = ref('') // 验证码校验码

// 获取短信验证码
const getSmsCode = () => {
  if (!phone.value) {
    ElMessage.error('请输入手机号')
    return
  }
  if (!captcha.value) {
    ElMessage.error('请输入图形验证码')
    return false
  }

  $API
    .post({
      url: 'ehs-clnt-platform-service/login/sendSmsForLogin',
      params: {
        phone: phone.value,
        sysCode: 'platform',
        client: 'WEB',
        captcha: captcha.value,
        captchaKey: captchaKey.value,
      },
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        ElMessage.success('短信验证码已发送')
        smsCodeTimer.value = 60
        const interval = setInterval(() => {
          if (smsCodeTimer.value > 0) {
            smsCodeTimer.value--
          } else {
            clearInterval(interval)
          }
        }, 1000)
      } else {
        getCaptcha()
        // ElMessage.error(res.message || '发送失败')
      }
    })
}

const rules = reactive<FormRules<any>>({
  unitId: [{ required: true, message: '请选择单位', trigger: ['change', 'blur'] }],
})
const form = ref({
  unitId: '',
})

// 获取验证码图片
function getCaptcha() {
  $API
    .post({
      url: `ehs-clnt-platform-service/login/captcha`,
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        captchaImg.value = res.data.base64Pic
        captchaKey.value = res.data.captchaKey
      }
    })
}

// 点击重新获取验证码
function refreshCaptcha() {
  getCaptcha()
}

function submit() {
  loading.value = true
  let loginMode = loginMethod.value === 'password' ? '1' : '2'
  let params: any = {
    client: 'WEB',
    captcha: captcha.value,
    sysCode: 'platform',
    loginMode: loginMode,
    captchaKey: captchaKey.value,
  }

  if (loginMode === '1') {
    // 密码登录
    if (!loginName.value) {
      loading.value = false
      ElMessage.error('请填写账号')
      return
    }
    if (!password.value) {
      loading.value = false
      ElMessage.error('请填写密码')
      return
    }
    if (!captcha.value) {
      loading.value = false
      ElMessage.error('请填写图形验证码')
      return
    }
    params.loginName = loginName.value
    params.password = hex_md5(password.value + 'true')
  } else {
    // 短信登录
    if (!phone.value) {
      loading.value = false
      ElMessage.error('请填写手机号')
      return
    }
    if (!captcha.value) {
      loading.value = false
      ElMessage.error('请填写图形验证码')
      return
    }
    if (!smsCode.value) {
      loading.value = false
      ElMessage.error('请填写短信验证码')
      return
    }
    params.loginName = phone.value
    params.smsCode = smsCode.value
  }
  if (!agreeToTerms.value) {
    ElMessage.error('请先阅读并同意服务协议和隐私协议')
    loading.value = false
    return
  }
  $API
    .post({
      url: 'ehs-clnt-platform-service/login/login',
      params: params,
    })
    .then(async (res: any) => {
      loading.value = false
      if (res && res.code == '200') {
        if (loginMode === '1' && isRememberInfo.value) {
          const text = JSON.stringify({
            n: loginName.value,
            p: password.value,
          })
          LocalStorage.set(loginCacheKey, encryptAes(text, secretKey))
        } else {
          LocalStorage.delete(loginCacheKey)
        }
        const d = res?.data
        ui.value = d
        ui.value.systemName = d?.systemName || '延长石油'
        localStorage.setItem('@@web_userInfo', JSON.stringify(ui.value))
        sessionStorage.setItem('@@web_userInfo', JSON.stringify({ userInfo: { ...ui.value } }))
        getAuthList()
      } else {
        getCaptcha()
      }
    })
}

let options = ref<any[]>([])

function getAuthList() {
  $API
    .post({
      url: `ehs-clnt-platform-service/login/getUserAuthUit`,
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        // 只有一个单位 直接跳过选择 直接进入
        options.value = res.data
        if (res.data.length <= 1) {
          islogin.value = true
          $API
            .post({
              url: 'ehs-clnt-platform-service/login/getUnitDetailInfoWithToken',
              params: {
                orgCode: options.value[0].unitId,
                client: 'platform',
              },
            })
            .then((data: any) => {
              console.log('data', data)
              if (data && data.code === 'success') {
                ui.value = {}
                const d = data.data
                d.resourceVoList = data?.data?.resourceVoList.map((item) => {
                  return {
                    ...item,
                    children: item.childrens,
                  }
                })
                ui.value = d
                ui.value.resourceList = d?.resourceVoList
                ui.value.systemName = d?.systemName || '延长石油'
                localStorage.setItem('@@web_userInfo', JSON.stringify(ui.value))
                sessionStorage.setItem('@@web_userInfo', JSON.stringify({ userInfo: { ...ui.value } }))
                router.push('/staging')
                // setTimeout(() => {
                //   window.location.reload()
                // }, 300)
                // localStorage.setItem('isreload', '1')
              }
            })
        } else {
          islogin.value = false
        }
      }
    })
}

async function unitSubmit() {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (!valid) return
    $API
      .post({
        url: 'ehs-clnt-platform-service/login/getUnitDetailInfoWithToken',
        params: {
          orgCode: form.value.unitId,
          client: 'platform',
        },
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          const d = res.data
          d.resourceVoList = res?.data?.resourceVoList.map((item) => {
            return {
              ...item,
              children: item.childrens,
            }
          })
          ui.value = d
          ui.value.resourceList = d?.resourceVoList
          ui.value.systemName = d?.systemName || '延长石油'
          // localStorage.setItem('@@web_userInfo', JSON.stringify(ui.value))
          localStorage.setItem('@@web_userInfo', JSON.stringify(ui.value))
          sessionStorage.setItem('@@web_userInfo', JSON.stringify({ userInfo: { ...ui.value } }))
          router.push('/staging')
          // setTimeout(() => {
          //   window.location.reload()
          // }, 300)
          // localStorage.setItem('isreload', '1')
        }
      })
    // console.log('发送请求',formInline.value)
  })
}

function getLoginCache() {
  let ret = {}
  const cipherText = LocalStorage.get<string>(loginCacheKey)
  if (cipherText) {
    const originalText = decryptAes(cipherText, secretKey)
    try {
      ret = JSON.parse(originalText)
    } catch (e) {
      ret = {}
    }
  }
  return ret
}

watch(
  () => isRememberInfo.value,
  (val) => {
    if (!val) {
    }
  },
  { immediate: true }
)
// 监听 loginMethod 的变化，执行相应的操作
watchEffect(() => {
  console.log(`当前登录方式: ${loginMethod.value}`)
  captcha.value = ''
  getCaptcha()
  loginName.value = ''
  password.value = ''
  phone.value = ''
  smsCode.value = ''
})

onMounted(() => {
  // getCaptcha()
  const loginCacheRes: LoginCacheType = getLoginCache()
  loginName.value = loginCacheRes.n || ''
  password.value = loginCacheRes.p || ''
})
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss">
.login-box {
  width: 601px;
  height: 660px;
  padding: 44px;
  background-image: url('../assets/login-form.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 8px;
  color: #333;
  // right: 10%;

  .captchaImg {
    width: 100px;
    height: 50px;
    margin-left: 30px;
    cursor: pointer;
  }

  .login-form {
    height: 419px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 45px;
  }

  .w-login-opt {
    height: 409px;
    width: 440px !important;
    margin: 30px auto;
    //background-color: chocolate;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .el-select__wrapper {
    background: rgba(0, 55, 97, 0.41);
    border-radius: 2px;
    border: 1px solid #119cca;
    box-shadow: 0 2px 4px rgba(17, 156, 202, 0.3);
    font-weight: 400;
    font-size: 16px;
    color: #4998c8;
    opacity: 0.76;
  }

  .title {
    background-image: url('../assets/login-title.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;

    .welcome-login {
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: MyCustomFont, sans-serif;
      font-weight: 400;
      font-size: 36px;
      color: #d3dce5;
      background: linear-gradient(0deg, #00aeff 0%, #ffffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .title-left {
    margin-bottom: 2px;
    font-family:
      Microsoft YaHei,
      serif;
    font-weight: 400;
    font-size: 16px;
    color: #e6efff;
    opacity: 0.76;
  }

  .el-input {
    margin-bottom: 10px;

    .el-input__inner {
      height: 48px;
      font-weight: 400;
      font-size: 16px;
      color: #4998c8;
      opacity: 0.76;
    }

    // 改变input框背景颜色
    :deep(.el-input__inner) {
      background-color: #4998c8 !important;
      border: 1px solid #1296db;
    }

    .el-input__wrapper {
      background: rgba(0, 55, 97, 0.41);
      border-radius: 2px;
      border: 1px solid #119cca;
      box-shadow: 0 2px 4px rgba(17, 156, 202, 0.3);
      font-weight: 400;
      font-size: 16px;
      color: #4998c8;
      opacity: 0.76;
    }
  }

  .el-button {
    width: 100%;
    height: 48px;
    background: linear-gradient(0deg, rgba(2, 81, 137, 0.4), rgba(0, 178, 199, 0.4));
    border-radius: 2px;
  }

  .login-button {
    width: 375px;
    font-size: 20px;
    position: relative;
    top: 22px;
  }

  input {
    color: 4998c8;
  }

  input:-webkit-autofill {
    -webkit-text-fill-color: #fff;
    transition: background-color 50000s ease-in-out 0s;
    caret-color: #4998c8;
    color: #4998c8;
  }

  /* 修改 Placeholder 颜色为灰色 */
  input::placeholder {
    color: #4998c8;
  }

  input::-webkit-input-placeholder {
    /* WebKit, Blink, Edge */
    color: #4998c8;
  }

  input:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #4998c8;
  }

  input::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #4998c8;
  }

  input:-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: #4998c8;
  }

  .input-icon {
    width: 16px;
    height: 16px;
  }
}

.custom-checkbox {
  .el-checkbox__inner {
    background: rgba(0, 55, 97, 0.5);
    border-radius: 2px;
    border: 1px solid #119cca;

    :after {
      border-color: var(#075068);
      transform: rotate(45deg) scaleY(1);
    }
  }

  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #075068;
    border-color: var(--el-checkbox-checked-input-border-color);
  }
}
</style>
<style lang="scss" scoped>
/* 登录框宽度 */
.w-375px {
  width: 375px;
  margin: 0 auto;
}

/* 登录方式切换 */
.login-tabs {
  :deep(.el-tabs__header) {
    margin: 0 0 26px;
  }

  :deep(.el-tabs__nav-wrap) {
    &::after {
      // 修改底部分隔线
      height: 1px !important;
      background-color: rgba(229, 229, 229, 0.3) !important;
    }
  }

  :deep(.el-tabs__nav) {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  :deep(.el-tabs__item) {
    flex: 1;
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    font-family:
      Microsoft YaHei,
      serif;
    font-weight: 400;

    &.is-active {
      color: #ffffff !important;
      opacity: 1 !important;
    }

    &:hover {
      color: #ffffff !important;
      opacity: 1 !important;
    }
  }
}

/* 短信验证码按钮 */
.sms-code-btn {
  width: 140px !important;
  margin-top: 2px;
  margin-left: 10px;
  font-family:
    Microsoft YaHei,
    serif;
  font-weight: 400;
  color: #e6efff;
  opacity: 0.76;
  font-size: 14px;
  background: rgba(0, 55, 97, 0.41) !important;
  border: 1px solid #119cca !important;
  box-shadow: 0 2px 4px rgba(17, 156, 202, 0.3);
}

/* 协议相关样式 */
.agreement-section {
  margin-top: 40px;
  text-align: center;
}

.agreement-link {
  color: #00b2c7;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

:deep(.agreement-dialog) {
  background: rgba(0, 55, 97, 0.95);
  border: 1px solid #119cca;

  .el-message-box__content {
    color: #e6efff;
  }
}
</style>
