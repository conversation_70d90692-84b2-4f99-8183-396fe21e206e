<template>
  <div class="view">
    <TitleBar title="监测点分布" />
    <div class="monitor-point">
      <div class="point-pic"></div>
      <div class="point-info">
        <div class="title">监测点总数</div>
        <div>
          <el-progress percentage="80" color="(90deg, #0A2C55 0%, #30AFEF 100%)" stroke-linecap="square">
            <el-button text class="text">200</el-button>
          </el-progress>
        </div>
      </div>
    </div>
    <div class="line-part w-full h-full" ref="chartRef"></div>
  </div>
</template>
<script setup lang="ts">
import TitleBar from '@/view/brainBigScreen/component/TitleBar.vue'
import * as echarts from 'echarts'
import { markRaw, onMounted, ref, watch } from 'vue'

defineOptions({ name: 'TopChart' })

const props = defineProps({
  chartData: {
    type: Object,
    default: () => {
      return {
        xAxisData: ['内部位移', '表面位移', '雨量监测', '水位监测', '视频监测'],
        seriesData: ['91', '82', '63', '82', '74'],
      }
    },
  },
  graphicColor: {
    type: Array,
    default: () => {
      return [
        ['#4BE9EA', '#1A4AD0'],
        ['#2A1770', '#AC5C8D', '#F38C38'],
      ]
    },
  },
})

const chartRef = ref<HTMLDivElement | null>(null)

const comChart = ref<any>(null)

function initChart() {
  const graphicColor: any[] = props.graphicColor || [[]]
  if (comChart.value) destroyEcharts()
  comChart.value = markRaw(echarts.init(chartRef.value as HTMLElement))
  const option = {
    grid: {
      top: 20,
      left: 88,
      right: 10,
      bottom: 88,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'none',
      },
      backgroundColor: 'RGBA(13, 29, 51, 0.8)',
      textStyle: {
        color: '#fff',
      },
      formatter: function (params: any) {
        return params[0].name + ': ' + params[0].value
      },
    },
    xAxis: {
      data: props.chartData.xAxisData,
      type: 'category',
      axisLine: {
        show: false,
        lineStyle: {
          color: 'rgba(93, 122, 166, 1)',
          shadowColor: 'rgba(255,255,255,1)',
        },
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        margin: 20,
        fontSize: 14,
      },
    },
    yAxis: {
      show: true,
      axisLine: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'solied',
          color: 'rgba(93, 122, 166, 1)',
        },
      },
      axisLabel: {
        fontSize: 14,
      },
    },
    series: [
      {
        name: 'hill',
        type: 'pictorialBar',
        barCategoryGap: '5%',
        // symbol: 'path://M0,10 L10,10 L5,0 L0,10 z',
        symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
        itemStyle: {
          opacity: 1,
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: graphicColor[0][0],
              },
              {
                offset: 1,
                color: graphicColor[0][1],
              },
            ],
            false
          ),
        },
        emphasis: {
          itemStyle: {
            opacity: 1,
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: graphicColor[1][0],
                },
                {
                  offset: 1,
                  color: graphicColor[1][1],
                },
                {
                  offset: 1,
                  color: graphicColor[1][2],
                },
              ],
              false
            ),
          },
        },
        data: props.chartData.seriesData,
        z: 10,
      },
      {
        name: 'glyph',
        type: 'pictorialBar',
        barGap: '-100%',
        symbolPosition: 'end',
        symbolSize: 50,
        symbolOffset: [0, '-120%'],
        data: props.chartData.seriesData.map((item: number) => {
          return {
            value: item,
            symbolSize: [0, 0],
          }
        }),
      },
    ],
  }
  comChart.value?.setOption(option)
}

function destroyEcharts() {
  if (comChart.value) {
    comChart.value.dispose()
    comChart.value = null
  }
}

onMounted(() => {
  watch(
    () => props.chartData,
    async () => {
      destroyEcharts()
      setTimeout(() => {
        initChart()
      }, 500)
    },
    { immediate: true, deep: true }
  )
})
</script>

<style scoped lang="scss">
.view {
  height: 280px;
  margin-top: 20px;

  .monitor-point {
    display: flex;
    margin-left: 40px;
    width: 100%;

    .point-pic {
      width: 68px;
      height: 68px;
      background: url('@/assets/image/monitorWarn/monitor-point.png') no-repeat;
      background-size: 100% 100%;
    }

    .point-info {
      width: 100%;
      flex: 1;
      margin-right: 30px;
    }

    .title {
      margin-top: 8px;
      font-family: AlibabaPuHuiTi;
      font-weight: 400;
      font-size: 18px;
      color: #ffffff;
      line-height: 30px;
    }

    .text {
      font-family: D-DIN-PRO;
      font-weight: bold;
      font-size: 26px;
      color: #ffffff;
      line-height: 30px;
    }
  }
}

:deep(.el-progress-bar__outer),
:deep(.el-progress-bar__inner) {
  border-radius: inherit;
}
</style>
