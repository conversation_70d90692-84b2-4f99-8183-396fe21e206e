<template>
  <div class="_navs_bottom">
    <div class="_navs" v-for="(item, index) in monitorList" :key="index">
      <img class="_sygl" :src="item.src" />
      <div class="_num">{{ item.num }}</div>
      <div class="_name">{{ item.name }}</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import mineBg from '@/assets/image/monitorWarn/mine-bg.png'
import monitorArea from '@/assets/image/monitorWarn/monitor-area.png'
import monitorPoint from '@/assets/image/monitorWarn/monitor-point2.png'

const monitorList = [
  { name: '矿山', src: mineBg, num: '1200' },
  { name: '监测面积', src: monitorArea, num: '12000' },
  { name: '监测点', src: monitorPoint, num: '120' },
]
</script>

<style scoped lang="scss">
._navs_bottom {
  font-size: 14px;
  position: absolute;
  left: 51%;
  top: 14%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  color: #fff;

  ._navs {
    position: relative;
    text-align: center;
    margin-right: 18px;
    cursor: pointer;

    ._sygl {
      // width: 80px;
      // height: 80px;
    }

    ._num {
      position: absolute;
      top: 29px;
      left: 106px;
      font-family: D-DIN-PRO;
      font-weight: bold;
      font-size: 32px;
      color: #4ccaff;
      line-height: 26px;
      background: linear-gradient(0deg, #ffffff 0%, #44bfff 53.759765625%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    ._name {
      position: absolute;
      top: 58px;
      left: 111px;
      font-family: AlibabaPuHuiTi;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 26px;
    }
  }
}
</style>
