<template>
  <div class="view">
    <TitleBar title="监测报警排名" />
    <div ref="horizontalColumnChart" class="line-part w-full h-full"></div>
    <!-- <div style="height: 180px">
      <ComPaiMing :graphic-color="['rgba(43, 97, 223, 1)', 'rgba(39, 150, 253, 1)']" />
    </div> -->
  </div>
</template>

<script setup>
import TitleBar from '@/view/brainBigScreen/component/TitleBar.vue'
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { sleep } from '@/common/utils'
import top1 from '@/assets/image/monitorWarn/sort-bj.png'
import top2 from '@/assets/image/monitorWarn/sort-bj2.png'
// import top2 from '../assets/top2.png';
// import top3 from '../assets/top3.png';
// import topOther from '../assets/top-other.png';

defineComponent({ name: 'HorizontalColumnChart' })

const props = defineProps({
  echartsData: {
    type: Array,
    default: () => {
      return [
        {
          name: '研发设备122',
          value: 93,
        },
        {
          name: '研发设备123',
          value: 79,
        },
        {
          name: '研发设备124',
          value: 18,
        },
        {
          name: '研发设备125',
          value: 16,
        },
        {
          name: '研发设备126',
          value: 0,
        },
        {
          name: '研发设备127',
          value: 0,
        },
      ]
    },
  },
  graphicColor: {
    type: Array,
    default: () => {
      return ['rgba(0, 98, 199, 1)', 'rgba(0, 233, 248, 1)']
    },
  },
  barWidth: {
    type: String,
    default: '10',
  },
  yAxisLabel: {
    type: Object,
    default: () => {
      return { grid: { left: '-20' }, axisLabel: { width: 220, margin: 100 } }
    },
  },
})

const imgList = [top1, top2]

const isEmpty = ref(false)
const horizontalColumnChart = ref()
const myChart = ref(null)
// const observer = ref < ResizeObserver > ();
const timer = ref(null)

function initEcharts(data) {
  if (myChart.value) destroyEcharts()
  const barWidth = props.barWidth
  const dataValue = data.map((item) => item.value)
  const max = Math.max(...dataValue)
  const maxData = dataValue.map(() => max)
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(horizontalColumnChart.value))
  const option = {
    grid: {
      top: '0',
      left: props.yAxisLabel.grid.left - 20,
      right: '-8',
      bottom: '0',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      backgroundColor: 'rgba(0,150,236,0.6)',
      textStyle: {
        color: '#fff',
      },
      formatter: function (params) {
        return (
          params[0].name +
          '<br/>' +
          "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:9px;background-color:rgba(36,207,233,0.9)'></span>" +
          params[0].seriesName +
          Number(params[0].value.toFixed(4)).toLocaleString() +
          '%<br/>'
        )
      },
    },
    xAxis: {
      type: 'value',
      minInterval: 1,
      max: 100,
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
    },
    yAxis: [
      {
        type: 'category',
        triggerEvent: true,
        inverse: true,
        axisLabel: {
          show: true,
          color: '#fff',
          align: 'left',
          width: props.yAxisLabel.axisLabel.width,
          overflow: 'truncate',
          ellipsis: '...',
          margin: props.yAxisLabel.axisLabel.margin,
          formatter: function (value, index) {
            return ['{lg|' + (index + 1) + '}' + '{title|' + value + '} '].join('\n')
          },
          rich: {
            lg: {
              backgroundColor: {
                image: top1,
              },
              color: '#fff',
              width: 13,
              height: 18,
              padding: [0, 0, 2, 5],
            },
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        data: data.map((item) => item.name),
      },
      {
        type: 'category',
        inverse: true,
        axisTick: 'none',
        axisLine: 'none',
        show: false,
        axisLabel: {
          color: '#333',
          fontSize: '16',
        },
        data: dataValue,
      },
    ],
    series: [
      {
        name: '',
        type: 'bar',
        zlevel: 1,
        itemStyle: {
          normal: {
            barBorderRadius: 30,
            color: (params) => {
              if (params.dataIndex < 3) {
                return new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: 'rgba(76, 42, 5, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(250, 173, 40, 1)',
                  },
                ])
              } else {
                return new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: 'rgba(10, 44, 85, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(48, 175, 239, 1)',
                  },
                ])
              }
            },
          },
        },
        label: {
          normal: {
            show: true,
            color: '#fff',
            fontSize: 16,
            position: 'right',
            formatter: '{c}%',
          },
        },
        barWidth,
        data: dataValue,
      },
      {
        name: '背景',
        type: 'bar',
        barWidth,
        barGap: '-100%',
        data: maxData,
        itemStyle: {
          color: 'rgba(106, 118, 136, 0.3)',
          // borderRadius: 8,
        },
      },
      // {
      //   name: '背景2',
      //   type: 'bar',
      //   barWidth: '16',
      //   barGap: '-130%',
      //   data: maxData,
      //   itemStyle: {
      //     color: 'rgba(47, 64, 79, 0.4)',
      //     // borderRadius: 8,
      //   },
      // },
    ],
  }
  myChart.value.setOption(option, true)
  // let i = -1;
  // timer.value = setInterval(() => {
  //   i = i === dataValue.length ? 0 : i + 1;
  //   myChart.value.dispatchAction({
  //     type: "showTip",
  //     seriesIndex: 0, // 显示第几个series
  //     dataIndex: i, // 显示第几个数据
  //   });
  // }, 2000);
  window.addEventListener('resize', () => {
    if (myChart.value) myChart.value.resize()
  })
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }

  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val) => {
      isEmpty.value = !val.length
      await sleep(500)
      if (!isEmpty.value && horizontalColumnChart.value) initEcharts(val)
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
  // 在组件卸载时停止监听，避免内存泄漏
  // observer.value?.disconnect();
})
</script>

<style scoped lang="scss">
.view {
  height: 310px;
  // margin-top: 20px;
  padding-bottom: 20px;
}
</style>
