<template>
  <div class="view">
    <TitleBar title="年代分布" />
    <div ref="chart" class="line-part w-full h-full"></div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import TitleBar from '@/view/brainBigScreen/component/TitleBar.vue'

const chart = ref(null)

onMounted(() => {
  const myChart = echarts.init(chart.value)

  let yList = [92, 83, 88, 30, 63]
  let xData = ['1960s', '1970s', '1980s', '1990s', '2000s']

  let barWidth = 800 / 20
  let colors = []
  for (let i = 0; i < 5; i++) {
    colors.push({
      type: 'linear',
      x: 0,
      x2: 1,
      y: 0,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: 'rgba(10, 99, 194, 1)', // 最左边
        },
        {
          offset: 0.5,
          color: '#86eef1', // 左边的右边 颜色
        },
        {
          offset: 0.5,
          color: 'rgba(21, 146, 214, 1)', // 右边的左边 颜色
        },
        {
          offset: 1,
          color: 'rgba(21, 146, 214, 1)',
        },
      ],
    })
  }
  const option = {
    //提示框
    tooltip: {
      trigger: 'axis',
      formatter: '{b} : {c}',
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
      },
    },
    /**区域位置*/
    grid: {
      left: '15%',
      right: 0,
      top: '5%',
      bottom: '25%',
    },
    //X轴
    xAxis: {
      data: xData,
      type: 'category',
      axisLine: {
        show: false,
        lineStyle: {
          color: 'rgba(93, 122, 166, 1)',
          shadowColor: 'rgba(255,255,255,1)',
        },
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        margin: 20,
        fontSize: 14,
      },
    },
    yAxis: {
      show: true,
      axisLine: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'solied',
          color: 'rgba(93, 122, 166, 1)',
        },
      },
      axisLabel: {
        // color: '#FFFFFF',
        // margin: 30,
        fontSize: 14,
      },
    },
    series: [
      {
        type: 'bar',
        barWidth: barWidth,
        itemStyle: {
          color: function (params) {
            return colors[params.dataIndex % 7]
          },
        },
        label: {
          show: false,
          position: [barWidth / 2, -(barWidth + 20)],
          color: '#ffffff',
          fontSize: 14,
          fontStyle: 'bold',
          align: 'center',
        },
        data: yList,
      },
      {
        z: 2,
        type: 'pictorialBar',
        data: yList,
        symbol: 'diamond',
        symbolOffset: [0, '50%'],
        symbolSize: [barWidth, barWidth * 0.5],
        itemStyle: {
          color: function (params) {
            return colors[params.dataIndex % 7]
          },
        },
      },
      {
        z: 3,
        type: 'pictorialBar',
        symbolPosition: 'end',
        data: yList,
        symbol: 'diamond',
        symbolOffset: [0, '-50%'],
        symbolSize: [barWidth, barWidth * 0.5],
        itemStyle: {
          borderWidth: 5,
          color: '#091434',
          borderColor: 'rgba(21, 146, 214, 1)',
        },
      },
    ],
  }

  myChart.setOption(option)
})
</script>

<style scoped lang="scss">
.view {
  height: 310px;
  margin-top: 20px;
}
</style>
