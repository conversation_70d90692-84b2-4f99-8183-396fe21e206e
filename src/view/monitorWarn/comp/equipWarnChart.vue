<template>
  <div class="view">
    <TitleBar title="设备报警趋势" :show-select="true" />
    <div ref="chart" class="line-part w-full h-full"></div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import TitleBar from '@/view/brainBigScreen/component/TitleBar.vue'

const chart = ref(null)

onMounted(() => {
  const myChart = echarts.init(chart.value)

  const option = {
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          // backgroundColor: '#6a7985'
        },
      },
    },
    legend: {
      left: 'right',
      textStyle: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 400,
      },
    },
    grid: {
      left: '8%',
      right: '2%',
      bottom: '25%',
      top: '10%',
      containLabel: true,
      backgroundColor: 'transparent',
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: ['1月', '2月', '3月', '4月', '5月', '6月'],
        axisLine: {
          onZero: false, // 关键设置，确保x轴线在y轴的最小值处，即使是最小值为负数
          show: true, // 隐藏Y轴轴线
          lineStyle: {
            color: 'rgba(21, 93, 174, 1)',
            width: 1,
            type: 'dashed',
            dashOffset: 0,
            opacity: 0.2,
          },
        },
        axisLabel: {
          show: true, // 隐藏刻度标签
          color: 'rgba(174, 195, 220, 1)',
          // interval: 1,
          align: 'middle', // 水平居中
          verticalAlign: 'top', // 垂直居中
          // margin: 18,
        },
        axisTick: {
          show: false, // 隐藏Y轴刻度线
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        // nameGap: 25,
        splitLine: {
          show: true,
          lineStyle: {
            type: 'solied',
            color: 'rgba(93, 122, 166, 1)',
          },
        },
      },
    ],
    series: [
      {
        name: '一级',
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 2,
          color: 'rgb(0,159,244)',
        },
        showSymbol: false,
        // symbolSize: 1,
        legendHoverLink: true,
        areaStyle: {
          opacity: 0.8,
          origin: 'start',
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(0, 221, 255,0.6)',
            },
            {
              offset: 1,
              color: 'rgba(35, 162, 255, 0.1)',
            },
          ]),
        },
        data: [80, 52, 81, 34, 51, 69, 15],
      },
      {
        name: '二级',
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 2,
          color: 'rgba(253, 125, 11, 1)',
        },
        showSymbol: false,
        // symbolSize: 1,
        legendHoverLink: true,
        areaStyle: {
          opacity: 0.8,
          origin: 'start',
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(253, 125, 11, 0.8)',
            },
            {
              offset: 1,
              color: 'rgba(253, 125, 11, 0.1)',
            },
          ]),
        },
        emphasis: {
          // focus: 'series'
        },
        data: [10, 72, 51, 54, 69, 59, 80],
      },
      {
        name: '三级',
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 2,
          color: 'rgba(255, 195, 16, 1)',
        },
        showSymbol: false,
        // symbolSize: 1,
        legendHoverLink: true,
        areaStyle: {
          opacity: 0.8,
          origin: 'start',
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(255, 195, 16, 0.8)',
            },
            {
              offset: 1,
              color: 'rgba(255, 195, 16, 0.1)',
            },
          ]),
        },
        emphasis: {
          // focus: 'series'
        },
        data: [50, 52, 66, 29, 48, 19, 47],
      },
      {
        name: '四级',
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 2,
          color: 'rgba(255, 0, 0, 1)',
        },
        showSymbol: false,
        // symbolSize: 1,
        legendHoverLink: true,
        areaStyle: {
          opacity: 0.8,
          origin: 'start',
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(255, 0, 0, 0.8)',
            },
            {
              offset: 1,
              color: 'rgba(255, 0, 0, 0.1)',
            },
          ]),
        },
        data: [69, 32, 41, 14, 56, 19, 25],
      },
    ],
  }

  myChart.setOption(option)
})
</script>

<style scoped lang="scss">
.view {
  height: 310px;
  // margin-top: 20px;
}
</style>
