<template>
  <div class="view">
    <TitleBar title="报警等级分布" />
    <div class="warn-total">
      <div class="warn-left">
        <div class="warn-top">
          <div class="warn-circle"></div>
          <span class="warn-title">设备超限报警累计情况</span>
        </div>
        <div class="warn-bot">
          <div class="warn-bot-pic"></div>
          <div style="margin-top: 8px">
            <div class="warn-bot-num">200</div>
            <div class="warn-bot-text">累计报警次数</div>
          </div>
        </div>
      </div>
      <div class="warn-right">
        <buttonBar />
      </div>
    </div>
    <div class="line-part w-full h-full" ref="pieChart"></div>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
import TitleBar from '@/view/brainBigScreen/component/TitleBar.vue'
import buttonBar from './buttonBar.vue'

defineComponent({ name: 'pie3DChart' })

const pieChart = ref()
const myChart = ref<any>(null)

function getParametricEquation(startRatio: any, endRatio: any, isSelected: any, isHovered: any, k: any, height: any) {
  // 计算
  let midRatio = (startRatio + endRatio) / 2

  let startRadian = startRatio * Math.PI * 2
  let endRadian = endRatio * Math.PI * 2
  let midRadian = midRatio * Math.PI * 2

  // 如果只有一个扇形，则不实现选中效果。
  if (startRatio === 0 && endRatio === 1) {
    isSelected = false
  }

  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = typeof k !== 'undefined' ? k : 1 / 3

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
  let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0

  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  let hoverRate = isHovered ? 1.05 : 1

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },

    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },

    x: function (u: any, v: any) {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
    },

    y: function (u: any, v: any) {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
    },

    z: function (u: any, v: any) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u)
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u)
      }
      return Math.sin(v) > 0 ? 1 * height : -1
    },
  }
}

// 生成模拟 3D 饼图的配置项
function getPie3D(pieData: any, internalDiameterRatio: any) {
  let series = [] as any[]
  let sumValue = 0
  let startValue = 0
  let endValue = 0
  let legendData: any = []
  let k =
    typeof internalDiameterRatio !== 'undefined' ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 3

  // 为每一个饼图数据，生成一个 series-surface 配置
  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value

    let seriesItem: Record<string, any> = {
      name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
      type: 'surface',
      parametric: true,
      wireframe: {
        show: false,
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: k,
      },
    }

    if (typeof pieData[i].itemStyle != 'undefined') {
      let itemStyle = {
        color: undefined,
        opacity: undefined,
      }

      typeof pieData[i].itemStyle.color != 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null
      typeof pieData[i].itemStyle.opacity != 'undefined' ? (itemStyle.opacity = pieData[i].itemStyle.opacity) : null

      seriesItem.itemStyle = itemStyle
    }
    series.push(seriesItem)
  }

  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
  // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
  for (let i = 0; i < series.length; i++) {
    endValue = startValue + series[i].pieData.value
    console.log(series[i])
    series[i].pieData.startRatio = startValue / sumValue
    series[i].pieData.endRatio = endValue / sumValue
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      series[i].pieData.value
    )

    startValue = endValue

    legendData.push(series[i].name)
  }

  // // 补充一个透明的圆环，用于支撑高亮功能的近似实现。
  series.push({
    name: '',
    type: 'surface',
    parametric: true,
    wireframe: {
      show: false,
    },
    itemStyle: {
      opacity: 0,
      color: '#22395A',
    },
    parametricEquation: {
      u: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20,
      },
      v: {
        min: 0,
        max: Math.PI,
        step: Math.PI / 20,
      },
      x: function (u: any, v: any) {
        return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2
      },
      y: function (u: any, v: any) {
        return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2
      },
      z: function (u: any, v: any) {
        return Math.cos(v) > 0 ? -0.5 : -5
      },
    },
  })

  // // 补充一个透明的圆环，用于支撑高亮功能的近似实现。
  series.push({
    name: '',
    type: 'surface',
    parametric: true,
    wireframe: {
      show: false,
    },
    itemStyle: {
      opacity: 1,
      color: '#22395A',
    },
    parametricEquation: {
      u: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20,
      },
      v: {
        min: 0,
        max: Math.PI,
        step: Math.PI / 20,
      },
      x: function (u: any, v: any) {
        return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2
      },
      y: function (u: any, v: any) {
        return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2
      },
      z: function (u: any, v: any) {
        return Math.cos(v) > 0 ? -5 : -7
      },
    },
  })
  // series.push({
  //   name: 'mouseoutSeries',
  //   type: 'surface',
  //   parametric: true,
  //   wireframe: {
  //     show: false,
  //   },
  //   itemStyle: {
  //     opacity: 1,
  //     color: '#173047',
  //   },

  //   parametricEquation: {
  //     u: {
  //       min: 0,
  //       max: Math.PI * 2,
  //       step: Math.PI / 20,
  //     },
  //     v: {
  //       min: 0,
  //       max: Math.PI,
  //       step: Math.PI / 20,
  //     },
  //     x: function (u: any, v: any) {
  //       return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2.2
  //     },
  //     y: function (u: any, v: any) {
  //       return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2.2
  //     },
  //     z: function (u: any, v: any) {
  //       return Math.cos(v) > 0 ? -7 : -7
  //     },
  //   },
  // })
  return series
}

function initEcharts() {
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(pieChart.value))
  const optionsData = [
    {
      name: '一级报警',
      value: 86,
      itemStyle: {
        //   opacity: 0.5,
        color: 'rgba(89, 106, 212, 1)',
      },
    },

    {
      name: '二级预警',
      value: 26,
      itemStyle: {
        //   opacity: 0.5,
        color: 'rgba(253, 125, 11, 1)',
      },
    },
    {
      name: '三级预警',
      value: 23,
      itemStyle: {
        //   opacity: 0.5,
        color: 'rgba(255, 195, 16, 1)',
      },
    },
    {
      name: '四级预警',
      value: 31,
      itemStyle: {
        //   opacity: 0.5,
        color: 'rgba(255, 0, 0, 1)',
      },
    },
  ]

  let series = getPie3D(optionsData, 0.8) as any

  series.push({
    name: 'pie2d',
    type: 'pie',
    label: {
      show: false,
      opacity: 1,
      fontSize: 13,
      lineHeight: 20,
      textStyle: {
        fontSize: 22,
      },
    },
    labelLine: {
      show: false,
      length: 60,
      length2: 60,
    },
    startAngle: -60, //起始角度，支持范围[0, 360]。
    clockwise: false, //饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式
    radius: ['50%', '0%'],

    center: ['10%', '10%'],
    data: optionsData,
    itemStyle: {
      opacity: 0,
    },
  })

  // 传入数据生成 option
  const option = {
    subtext: '',
    legend: {
      left: 'left',
      // data: ['一级报警', '二级报警', '三级报警', '四级报警', ''],
      orient: 'vertical',
      top: '25%',
      textStyle: {
        color: '#fff',
        fontSize: 16,
      },
    },
    animation: true,
    tooltip: {
      formatter: (params: any) => {
        if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
          return `${params.seriesName}<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>${option.series[params.seriesIndex].pieData.value + '次'}`
        }
      },
      textStyle: {
        fontSize: 15,
      },
    },
    title: {
      x: '0',
      top: '20',
      textStyle: {
        color: '#fff',
        fontSize: 22,
      },
    },
    // backgroundColor: '#333',
    labelLine: {
      show: false,
      lineStyle: {
        color: '#7BC0CB',
      },
    },
    label: {
      show: false,
      position: 'outside',
      formatter: '{b} \n{c} {d}%',
    },
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 0.5,
      left: 80,
      top: -30,
      environment: '',

      viewControl: {
        //3d效果可以放大、旋转等，请自己去查看官方配置
        alpha: 30, //角度(这个很重要 调节角度的)
        distance: 200, //调整视角到主体的距离，类似调整zoom(这是整体大小)
        rotateSensitivity: 1, //设置为0无法旋转
        zoomSensitivity: 0, //设置为0无法缩放
        panSensitivity: 0, //设置为0无法平移
        autoRotate: false, //自动旋转
      },
    },

    series: series,
  }

  myChart.value.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart.value) myChart.value.resize()
  })
}

onMounted(() => {
  initEcharts()
})
</script>

<style scoped lang="scss">
.view {
  height: 310px;

  .warn-total {
    display: flex;
    justify-content: space-between;
    margin-left: 35px;
    align-items: baseline;

    .warn-left {
      .warn-circle {
        display: inline-block;
        width: 10px;
        height: 10px;
        background: #5cc7ff;
        border-radius: 50%;
        margin-right: 5px;
      }

      .warn-title {
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        line-height: 30px;
      }

      .warn-bot {
        display: flex;

        .warn-bot-pic {
          width: 68px;
          height: 68px;
          background: url('@/assets/image/monitorWarn/warn-pic.png') no-repeat;
          background-size: 100% 100%;
          margin-right: 5px;
        }

        .warn-bot-num {
          font-family: D-DIN-PRO;
          font-weight: bold;
          font-size: 26px;
          color: #ffffff;
          line-height: 30px;
        }

        .warn-bot-text {
          font-family: AlibabaPuHuiTi;
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          line-height: 30px;
        }
      }
    }

    .warn-right {
      padding-right: 51px;
      flex: 1;
    }
  }

  .line-part {
    margin-left: 35px;
    margin-top: -61px;
  }
}
</style>
