<template>
  <div class="view">
    <TitleBar title="监测总览" />
    <div ref="chart" class="line-part w-full h-full">
      <div class="title title1">露天矿</div>
      <div class="title title2">尾矿库</div>
      <div class="title title3">排土场</div>
      <div class="title title4 num">44</div>
      <div class="title title5 num">3</div>
      <div class="title title6 num">3</div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import TitleBar from '@/view/brainBigScreen/component/TitleBar.vue'

const chart = ref(null)

onMounted(() => {
  // const myChart = echarts.init(chart.value)

  let yList = [92, 83, 88, 30, 63]
  let xData = ['1960s', '1970s', '1980s', '1990s', '2000s']

  let barWidth = 800 / 20
  let colors = []
  for (let i = 0; i < 5; i++) {
    colors.push({
      type: 'linear',
      x: 0,
      x2: 1,
      y: 0,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: 'rgba(10, 99, 194, 1)', // 最左边
        },
        {
          offset: 0.5,
          color: '#86eef1', // 左边的右边 颜色
        },
        {
          offset: 0.5,
          color: 'rgba(21, 146, 214, 1)', // 右边的左边 颜色
        },
        {
          offset: 1,
          color: 'rgba(21, 146, 214, 1)',
        },
      ],
    })
  }
  const option = {
    //提示框
    tooltip: {
      trigger: 'axis',
      formatter: '{b} : {c}',
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
      },
    },
    /**区域位置*/
    grid: {
      left: '12%',
      right: '2%',
      top: '5%',
      // bottom: '15%',
    },
    //X轴
    xAxis: {
      data: xData,
      type: 'category',
      axisLine: {
        show: false,
        lineStyle: {
          color: 'rgba(93, 122, 166, 1)',
          shadowColor: 'rgba(255,255,255,1)',
        },
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        margin: 20,
        fontSize: 14,
      },
    },
    yAxis: {
      show: true,
      axisLine: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'solied',
          color: 'rgba(93, 122, 166, 1)',
        },
      },
      axisLabel: {
        // color: '#FFFFFF',
        // margin: 30,
        fontSize: 14,
      },
    },
    series: [
      {
        type: 'bar',
        barWidth: barWidth,
        itemStyle: {
          color: function (params) {
            return colors[params.dataIndex % 7]
          },
        },
        label: {
          show: false,
          position: [barWidth / 2, -(barWidth + 20)],
          color: '#ffffff',
          fontSize: 14,
          fontStyle: 'bold',
          align: 'center',
        },
        data: yList,
      },
      {
        z: 2,
        type: 'pictorialBar',
        data: yList,
        symbol: 'diamond',
        symbolOffset: [0, '50%'],
        symbolSize: [barWidth, barWidth * 0.5],
        itemStyle: {
          color: function (params) {
            return colors[params.dataIndex % 7]
          },
        },
      },
      {
        z: 3,
        type: 'pictorialBar',
        symbolPosition: 'end',
        data: yList,
        symbol: 'diamond',
        symbolOffset: [0, '-50%'],
        symbolSize: [barWidth, barWidth * 0.5],
        itemStyle: {
          borderWidth: 5,
          color: '#091434',
          borderColor: 'rgba(21, 146, 214, 1)',
        },
      },
    ],
  }

  // myChart.setOption(option)
})
</script>

<style scoped lang="scss">
.view {
  // height: 200px;
  margin-bottom: 20px;

  .line-part {
    margin-left: 30px;
    height: 200px;
    background: url('@/assets/image/monitorWarn/monitor-over.png') no-repeat;
    background-size: 93% 100%;
    position: relative;

    .title {
      font-weight: 400;
      font-size: 18px;
      color: #fff9f8;
      position: absolute;
      line-height: 40px;
    }

    .title1 {
      top: 7%;
      left: 41%;
    }

    .title2 {
      top: 39%;
      left: 41%;
    }

    .title3 {
      top: 74%;
      left: 41%;
    }

    .title4 {
      top: 7%;
      right: 17%;
    }

    .title5 {
      top: 39%;
      right: 21%;
    }

    .title6 {
      top: 74%;
      right: 21%;
    }

    .num {
      font-family: D-DIN-PRO;
      font-weight: bold;
      font-size: 32px;
      color: #ffffff;
      line-height: 40px;
      background: linear-gradient(0deg, #ffffff 0%, #44bfff 53.759765625%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
</style>
