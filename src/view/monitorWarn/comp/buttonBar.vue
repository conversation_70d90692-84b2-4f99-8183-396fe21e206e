<template>
  <div>
    <div class="_yinhuan">
      <div class="_tab">
        <div v-for="(item, index) in tab" :key="index" @click="clickTab(index)">
          <div :class="tIndex === index ? '_s' : '_c'">{{ item }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  showTab: {
    type: Boolean,
    default: false,
  },
})

const tab = ref(['今年度', '历史累计'])
const tIndex = ref(0)

const clickTab = (i: number) => {
  tIndex.value = i
}
</script>

<style scoped lang="scss">
._yinhuan {
  position: relative;
  color: #fff;

  ._title {
    font-family: sans-serif;
    font-size: 24px;
    text-shadow: 0 2px 8px rgba(41, 47, 58, 0.05);
    background: linear-gradient(180deg, #bbe7fe, #e9f8ff 60%, #f8fdff);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: absolute;
    top: -6px;
    left: 9%;
  }

  ._xczytj {
    width: 100%;
    height: 53px;
  }

  ._tab {
    display: flex;
    align-items: center;
    position: absolute;
    right: 2px;
    top: 1px;

    div {
      position: relative;
      font-size: 14px;
      width: 74px;
      height: 32px;
      color: #5c758d;
      cursor: pointer;

      span {
        position: absolute;
        top: 5px;
        left: 9px;
      }

      ._s {
        text-align: center;
        align-items: center;
        color: #fff;
        background: url('@/assets/image/bigScreen/_fx_bg_active.png') no-repeat;
        background-size: 100% 100%;
        cursor: pointer;
        line-height: 28px;
      }

      ._c {
        text-align: center;
        align-items: center;
        // color: #fff;
        background: url('@/assets/image/bigScreen/_fx_bg.png') no-repeat;
        background-size: 100% 100%;
        cursor: pointer;
        line-height: 28px;
      }
    }
  }
}
</style>
