<template>
  <div id="screen" class="screen">
    <div class="bodys">
      <HomeGis />
      <HeaderScreen />
      <div class="_screen_content">
        <div class="_screen_left">
          <MonitorViewChart />
          <YearListChart />
          <MonitorPointChart />
        </div>
        <div class="_screen_right">
          <WarnLevelChart />
          <EquipWarnChart />
          <MonitorSortChart />
        </div>
      </div>
      <MonitorList />
      <!-- <BottomNavs /> -->
    </div>
  </div>
</template>
<script setup lang="ts">
import MonitorViewChart from './comp/monitorViewChart.vue'
import YearListChart from './comp/yearListChart.vue'
import MonitorPointChart from './comp/monitorPointChart.vue'
import WarnLevelChart from './comp/warnLevelChart.vue'
import EquipWarnChart from './comp/equipWarnChart.vue'
import MonitorSortChart from './comp/monitorSortChart.vue'
import MonitorList from './comp/monitorList.vue'
// import BottomNavs from '@/components/bigScreen/BottomNavs.vue'
import HeaderScreen from '@/view/brainBigScreen/component/HeaderScreen.vue'
import HomeGis from '@/components/HomeGis/HomeGis.vue'
import { onMounted } from 'vue'

//数据大屏自适应函数
const handleScreenAuto = (): void => {
  const designDraftWidth = 1920 //设计稿的宽度
  const designDraftHeight = 1080 //设计稿的高度
  //根据屏幕的变化适配的比例
  const scale =
    document.documentElement.clientWidth / document.documentElement.clientHeight < designDraftWidth / designDraftHeight
      ? document.documentElement.clientWidth / designDraftWidth
      : document.documentElement.clientHeight / designDraftHeight
  //缩放比例
  ;(document.querySelector('#screen') as any).style.transform = `scale(${scale}) translate(-50%)`
}

onMounted(() => {
  //初始化自适应 -刚显示的时候就开始适配一次
  handleScreenAuto()
  //绑定自适应函数 -防止浏览器栏变化后不再适配
  window.onresize = () => handleScreenAuto()
})

defineOptions({ name: 'MonitorWarn' })
</script>

<style scoped lang="scss">
.screen {
  display: inline-block;
  width: 1920px; //设计稿的宽度
  height: 1080px; //设计稿的高度
  transform-origin: 0 0;
  position: absolute;
  left: 50%;
  background: url('@/components/HomeGis/assets/bg.png') no-repeat #030910;
  // background-size: 100% 100%;
  overflow: hidden !important;
  letter-spacing: 1px;

  .bodys {
    ._screen_content {
      display: flex;
      justify-content: space-between;

      ._screen_left {
        width: 27%;
        // display: flex;
      }

      ._screen_right {
        width: 27%;
        padding-right: 20px;
        // height: 800px;
      }
    }
  }
}
</style>
