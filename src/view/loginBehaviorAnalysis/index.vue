<template>
  <div class="w-full h-full flex">
    <el-card
      class="mr-[20px] py-[13px]"
      :class="collapsed ? 'px-17px' : ''"
      :style="{ width: collapsed ? '300px' : '0' }"
      v-show="collapsed"
    >
      <train-tree :collapsed="collapsed" @serach="handleSearch" :extra-params="{ unitStatus: '1' }" />
    </el-card>
    <div class="main flex-1 w-0 relative p-[20px] flex flex-col h-full" ref="captureRef">
      <Filter class="mb-[16px]" @search="handleSearch" @export="handleExport" />
      <Dashboard />
      <div class="flex flex-1 h-0 gap-[16px]">
        <div class="flex-1 w-0 flex flex-col gap-[16px]">
          <!--系统访问统计-->
          <div class="flex-1 h-0">
            <SystemVisitStatistics />
          </div>
          <!--业务系统访问量排行-->
          <div class="flex-1 h-0">
            <SystemVisitRanking />
          </div>
        </div>
        <!--活跃单位排行榜-->
        <div class="flex-1 w-0">
          <ActiveUnitRanking />
        </div>
      </div>
      <img src="@/assets/image/tree-switch.png" class="tree-switch" alt="" @click="collapsed = !collapsed" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import TrainTree from '@/components/tree/index.vue'
import Filter from './comps/filter.vue'
import Dashboard from './comps/dashboard.vue'
import SystemVisitStatistics from './comps/systemVisitStatistics.vue'
import SystemVisitRanking from './comps/systemVisitRanking.vue'
import ActiveUnitRanking from './comps/activeUnitRanking.vue'
import { commonQuery } from '~/view/loginBehaviorAnalysis/service'
import { toPng } from 'html-to-image'

const collapsed = ref(true)
const captureRef = ref<null | HTMLElement>(null)

function handleSearch(data: any) {
  commonQuery.value.orgCode = data.orgCode
}

// 导出为图片
function handleExport() {
  if (!captureRef.value) return
  toPng(captureRef.value, {
    filter: (node) => {
      // 过滤掉你想隐藏的元素
      return !(node.id === 'hideBtn')
    },
  })
    .then((dataUrl) => {
      const link = document.createElement('a')
      link.href = dataUrl
      link.download = `登录行为分析-${Date.now()}.png`
      link.click()
    })
    .catch((error) => {
      console.error('导出失败', error)
    })
}

defineOptions({ name: 'LoginBehaviorAnalysis' })
</script>

<style scoped lang="scss">
.main {
  background-color: #eef7ff;
  border-radius: 4px;
}

:deep(.el-card.is-always-shadow) {
  box-shadow: none !important;
  border: none;
  background: #eef6ff;

  .el-card__body {
    @apply h-full;
    padding: 0;
  }
}

.tree-switch {
  @apply absolute left-0 top-1/2 cursor-pointer w-[36px] h-[36px];
  transform: translate(-50%, -50%);
}
</style>
