<template>
  <div class="filter">
    <div
      class="time-btn"
      :class="{ active: commonQuery.timeType === item.key }"
      v-for="item in dateShortcut"
      :key="item.key"
      @click="changeDateRange(item)"
    >
      {{ item.text }}
    </div>
    <div class="ml-[20px] w-[460px] flex items-center text-[14px]">
      <span>自定义时间：</span>
      <el-date-picker
        class="flex-1"
        v-model="dateRange"
        type="datetimerange"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        range-separator="至"
        value-format="YYYY-MM-DD HH:mm:ss"
        :clearable="false"
        :editable="false"
        :default-time="defaultTime"
        @change="handleChange"
      />
    </div>
    <el-button class="ml-auto" type="primary" @click="handleExport" id="hideBtn">导出</el-button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getMonthRange, getQuarterRange, getTodayRange, getWeekRange } from '~/common/dateTimeRange'
import { commonQuery } from '../service'

const $emits = defineEmits(['search', 'export'])

const dateShortcut = ref([
  {
    key: 1,
    text: '今天',
    value: getTodayRange(),
  },
  {
    key: 2,
    text: '本周',
    value: getWeekRange(),
  },
  {
    key: 3,
    text: '本月',
    value: getMonthRange(),
  },
  {
    key: 4,
    text: '本季度',
    value: getQuarterRange(),
  },
])

const dateRange = ref([])
const defaultTime: [Date, Date] = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]

function changeDateRange(data: any) {
  commonQuery.value.timeType = data.key
  commonQuery.value.startTime = data.value[0]
  commonQuery.value.endTime = data.value[1]
  dateRange.value = []
}

function handleChange() {
  commonQuery.value.timeType = null
  commonQuery.value.startTime = dateRange.value ? dateRange.value[0] : ''
  commonQuery.value.endTime = dateRange.value ? dateRange.value[1] : ''
}

function handleSearch() {
  $emits('search')
}

// 导出
function handleExport() {
  $emits('export')
}

defineOptions({ name: 'LoginBehaviorAnalysisFilter' })
</script>

<style scoped lang="scss">
.filter {
  background: #fff;
  padding: 16px;
  border-radius: 6px;
  display: flex;
  align-items: center;

  .time-btn {
    @apply cursor-pointer;
    background: #f9fafb;
    padding: 8px 16px;
    margin-right: 16px;
    border-radius: 6px;
    font-size: 12px;
    &.active {
      background: #eff6ff;
      color: #4186f6;
    }
  }
}
</style>
