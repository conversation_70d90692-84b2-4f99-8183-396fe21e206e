<template>
  <div class="system-visit-ranking">
    <div class="header">
      <ComTitleA title="业务系统访问量排行">
        <template #left-extra>
          <el-tooltip content="在选定的周期范围内，展示出各业务系统访问量的排行Top10" placement="top-start">
            <el-icon class="ml-[4px]"><InfoFilled /></el-icon>
          </el-tooltip>
        </template>
      </ComTitleA>
    </div>
    <div class="chart">
      <BarChart :echartsData="echartsData" :graphicColor="graphicColor" :extra="extra" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import BarChart from '@/components/charts/barChart.vue'
import { commonQuery } from '~/view/loginBehaviorAnalysis/service'
import { getYwxtfwph } from '~/view/loginBehaviorAnalysis/fetchData'
import { InfoFilled } from '@element-plus/icons-vue'
import ComTitleA from '~/components/HeadTitle/comTitleA.vue'

// 柱状图数据
const echartsData = ref<Record<string, any>>({
  label: [],
  data: [{ name: '', value: [] }],
})

const graphicColor = [['#5FB4FF', '#1598FF']]

const extra = {
  grid: { bottom: '8' },
  barGap: 0,
  barWidth: 32,
  xAxis: {
    rotate: 45, // 倾斜角度（正数顺时针，负数逆时针）
    interval: 0, // 强制显示所有标签
    formatter: function (value: string) {
      // 每4个字符换行，可根据需要自定义
      return value.length > 4 ? value.slice(0, 4) + '\n' + value.slice(4) : value;
    },
  },
}

function getData() {
  const params = {
    ...commonQuery.value,
  }
  getYwxtfwph(params).then((res) => {
    // 判断是否都为0
    const allZero = res.data.yData1.every((num: number) => num === 0);
    if (allZero) {
      echartsData.value.data = []
    } else {
      echartsData.value.label = res.data.xData
      echartsData.value.data[0] = {
        name: '',
        value: res.data.yData1
      }
    }
  })
}

watch(
  () => commonQuery.value,
  (val) => {
    if (!val.orgCode) return;
    if (val) getData()
  },
  {
    deep: true,
    immediate: true,
  }
)

defineOptions({ name: 'SystemVisitRanking' })
</script>

<style lang="scss" scoped>
.system-visit-ranking {
  @apply w-full h-full bg-white flex flex-col;
  border-radius: 6px;
  overflow: hidden;
}
.header {
  @apply flex items-center;
  height: 40px;
  padding: 0 16px;
}
.chart {
  @apply w-full flex-1 h-0;
}
</style>
