<template>
  <div class="active-unit-ranking">
    <div class="header">
      <ComTitleA title="活跃单位排行榜">
        <template #left-extra>
          <el-tooltip
            content="在选定的周期范围内，以活跃人数和访问次数来统计单位的活跃度的排行Top10"
            placement="top-start"
          >
            <el-icon class="ml-[4px]"><InfoFilled /></el-icon>
          </el-tooltip>
        </template>
      </ComTitleA>
      <div class="ml-auto cursor-pointer" @click="changeSort">
        <img v-if="sortType === 1" :src="IconAsc" alt="" />
        <img v-else :src="IconDesc" alt="" />
      </div>
    </div>
    <div class="chart">
      <StackedHorizontalBar :echartsData="echartsData" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import StackedHorizontalBar from '@/components/charts/stackedHorizontalBar.vue'
import { getHydwphb } from '~/view/loginBehaviorAnalysis/fetchData'
import { commonQuery } from '~/view/loginBehaviorAnalysis/service'
import IconAsc from '../assets/icon-asc.svg'
import IconDesc from '../assets/icon-desc.svg'
import { InfoFilled } from '@element-plus/icons-vue'
import ComTitleA from '~/components/HeadTitle/comTitleA.vue'

const sortType = ref(1) // 排序类型 1-top10 2-bottom10

const echartsData = ref({
  label: [],
  data: [
    { name: '访问人数', value: [] },
    { name: '访问次数', value: [] },
  ],
})

function changeSort() {
  sortType.value = sortType.value === 1 ? 2 : 1
  getData()
}

function getData() {
  const params = {
    sortType: sortType.value, // 排序类型 1-top10 2-bottom10
    ...commonQuery.value,
  }
  getHydwphb(params).then((res) => {
    // 判断是否都为0
    const allZero =
      res.data.yData1.every((num: number) => num === 0) && res.data.yData2.every((num: number) => num === 0)
    if (allZero) {
      echartsData.value.data = []
    } else {
      echartsData.value.label = res.data.xData
      echartsData.value.data[0] = {
        name: '访问人数',
        value: res.data.yData1,
      }
      echartsData.value.data[1] = {
        name: '访问次数',
        value: res.data.yData2,
      }
    }
  })
}

watch(
  () => commonQuery.value,
  (val) => {
    if (!val.orgCode) return
    if (val) getData()
  },
  {
    deep: true,
    immediate: true,
  }
)

defineOptions({ name: 'ActiveUnitRanking' })
</script>

<style lang="scss" scoped>
.active-unit-ranking {
  @apply w-full h-full bg-white flex flex-col;
  border-radius: 6px;
  overflow: hidden;
}
.header {
  @apply flex items-center;
  height: 40px;
  padding: 0 16px;
}
.chart {
  @apply w-full flex-1 h-0;
}
</style>
