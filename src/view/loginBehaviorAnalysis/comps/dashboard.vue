<template>
  <div class="dashboard">
    <div class="dashboard-item">
      <div class="label">总登录人数</div>
      <div class="value">{{ totalInfo.zdlrs }}</div>
      <div class="contrast">
        <span :class="getClass(totalInfo.zdlrs_last_prcent)">{{ Math.abs(totalInfo.zdlrs_last_prcent || 0) }}%</span>较上周期
      </div>
    </div>
    <div class="dashboard-item">
      <div class="label">今日活跃用户</div>
      <div class="value">{{ totalInfo.jrhyyhs }}</div>
      <div class="contrast">
        <span :class="getClass(totalInfo.jrhyyhs_last_prcent)">{{ Math.abs(totalInfo.jrhyyhs_last_prcent || 0) }}%</span>较上周期
      </div>
    </div>
    <div class="dashboard-item">
      <div class="label">登录频次</div>
      <div class="value">{{ totalInfo.dlpc }}次/人</div>
      <div class="contrast">
        <span :class="getClass(totalInfo.dlpc_last_prcent)">{{ Math.abs(totalInfo.dlpc_last_prcent || 0) }}%</span>较上周期
      </div>
    </div>
    <div class="dashboard-item">
      <div class="label">
        周期内流失人数
        <el-tooltip content="流失人数：30天内未登录的用户统计" placement="top-start">
          <el-icon><InfoFilled /></el-icon>
        </el-tooltip>
      </div>
      <div class="value">{{ totalInfo.zqnxsrs }}</div>
      <div>流失用户总数：{{ totalInfo.lsyhzs }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { getTotal } from '../fetchData'
import { commonQuery } from '../service'
import { InfoFilled } from '@element-plus/icons-vue'

const totalInfo = ref<any>({})

function getClass(val: number) {
  if (val === undefined || val === null) {
    return 'up'
  }
  return val >= 0 ? 'up' : 'down'
}

function getData() {
  const params = {
    ...commonQuery.value,
  }
  getTotal(params).then((res) => {
    totalInfo.value = res.data
  })
}

watch(
  () => commonQuery.value,
  (val) => {
    if (!val.orgCode) return;
    if (val) getData()
  },
  {
    deep: true,
    immediate: true,
  }
)

defineOptions({ name: 'DashboardComp' })
</script>

<style scoped lang="scss">
.dashboard {
  @apply w-full flex gap-[24px] mb-[16px];
  .dashboard-item {
    @apply bg-white flex-1;
    padding: 16px;
    font-size: 14px;
    border-radius: 6px;
    .label {
      margin-bottom: 8px;
    }
    .value {
      @apply text-[16px] font-bold;
      margin-bottom: 8px;
    }
    .contrast {
      color: #9da4b0;
      .up {
        position: relative;
        color: #36ca6d;
        margin-right: 8px;
        padding-left: 10px;
        &::before {
          content: '↑';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .down {
        position: relative;
        color: #ef4444;
        margin-right: 8px;
        padding-left: 10px;
        &::before {
          content: '↓';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }
}
</style>
