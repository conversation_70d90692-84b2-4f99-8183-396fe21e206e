<template>
  <div class="system-visit-statistics">
    <div class="header">
      <ComTitleA title="系统访问统计">
        <template #left-extra>
          <el-tooltip content="通过选择不同的日期单位，多元化展示系统的访问统计" placement="top-start">
            <el-icon class="ml-[4px]"><InfoFilled /></el-icon>
          </el-tooltip>
        </template>
      </ComTitleA>
      <div class="ml-auto flex">
        <div
          class="time-btn"
          :class="{ active: groupType === item.key }"
          v-for="item in dateShortcut"
          :key="item.key"
          @click="changeDateRange(item)"
        >
          {{ item.text }}
        </div>
      </div>
    </div>
    <div class="chart">
      <LineEcharts
        :echartsData="echartsData"
        :extra="{ smooth: true, yAxis: {}, legend: {} }"
        :color="['#2F6CE4', '#1AB5E8']"
        :graphicColor="[
          ['rgba(47,108,228,0.15)', 'rgba(47,108,228,0)'],
          ['rgba(26,181,232,0.15)', 'rgba(26,181,232,0)'],
        ]"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import LineEcharts from '@/components/charts/lineChart.vue'
import ComTitleA from '~/components/HeadTitle/comTitleA.vue'
import { getXtfwtj } from '~/view/loginBehaviorAnalysis/fetchData'
import { commonQuery } from '~/view/loginBehaviorAnalysis/service'
import { InfoFilled } from '@element-plus/icons-vue'

const dateShortcut = ref([
  { key: '1', text: '日' },
  { key: '3', text: '月' },
  { key: '4', text: '季度' },
  { key: '5', text: '年' },
])
const groupType = ref('1') // 分组类型 1-日 2-周 3-月 4-季度 5-年

const echartsData = ref<Record<string, any>>({
  label: [],
  data: [
    {
      name: '活跃人数',
      value: [],
    },
    {
      name: '访问次数',
      value: [],
    },
  ],
})

function changeDateRange(data: any) {
  groupType.value = data.key
  getData()
}

function getData() {
  const params = {
    groupType: groupType.value, // 分组类型 1-日 2-周 3-月 4-季度 5-年
    ...commonQuery.value,
  }
  getXtfwtj(params).then((res) => {
    // 判断是否都为0
    const allZero = res.data.yData1.every((num: number) => num === 0) && res.data.yData2.every((num: number) => num === 0);
    if (allZero) {
      echartsData.value.data = []
    } else {
      echartsData.value.label = res.data.xData
      echartsData.value.data[0] = {
        name: '活跃人数',
        value: res.data.yData1
      }
      echartsData.value.data[1] = {
        name: '访问次数',
        value: res.data.yData2
      }
    }
  })
}

watch(
  () => commonQuery.value,
  (val) => {
    if (!val.orgCode) return;
    if (val) getData()
  },
  {
    deep: true,
    immediate: true,
  }
)

defineOptions({ name: 'SystemVisitStatistics' })
</script>

<style lang="scss" scoped>
.system-visit-statistics {
  @apply w-full h-full bg-white flex flex-col;
  border-radius: 6px;
  overflow: hidden;
}
.header {
  @apply flex items-center;
  height: 40px;
  padding: 0 16px;

  .time-btn {
    @apply cursor-pointer;
    background: #f9fafb;
    padding: 6px 10px;
    margin-right: 8px;
    border-radius: 6px;
    font-size: 12px;
    &:last-child {
      margin-right: 0;
    }
    &.active {
      background: #eff6ff;
      color: #4186f6;
    }
  }
}
.chart {
  @apply w-full flex-1 h-0;
}
</style>
