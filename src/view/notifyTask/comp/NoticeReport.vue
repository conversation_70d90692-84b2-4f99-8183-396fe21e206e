<template>
  <div class="h-full todo-task w-full" v-loading="loading">
    <el-card class="todo-right">
      <el-scrollbar>
        <div v-if="noticeList.length">
          <div v-for="(item, index) in noticeList" :key="index" class="content">
            <div class="img"></div>
            <div class="flex-1">
              <div class="flex justify-between items-center mb-12px">
                <div class="item messageTitle mr-20px flex-1" @click="goDetail(item)">
                  {{ item.messageTitle }}
                </div>
                <div class="time w-240px">发布时间：{{ item.pushTime }}</div>
                <div class="operate">
                  <el-dropdown trigger="hover" @command="handleCommand">
                    <span class="el-dropdown-link">
                      <svg-icon name="moreOperate" :size="20" class="mr-[10px]"></svg-icon>
                    </span>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <!-- <el-dropdown-item :command="'edit-' + index">编辑</el-dropdown-item> -->
                        <el-dropdown-item :command="'delete-' + index">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
              <div class="text" v-html="item.messageContent"></div>
              <div class="read">
                <div class="flex read-status">
                  <div class="mr-[30px]">
                    已读：<span class="status">{{ item.readed }}</span> 人
                  </div>
                  <div>
                    未读：<span class="status-unread">{{ item.unRead }}</span>
                    人
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="not">
          <div>
            <el-image :src="defaultPng"> </el-image>
            <div class="not-description">暂无安全新闻</div>
          </div>
        </div>
      </el-scrollbar>
      <div class="btn-page" v-if="total">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          v-model:current-page="curPage"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </el-card>
  </div>

  <!-- 删除弹框 -->
  <el-dialog v-model="deleteVisible" width="488" height="290" @close="deleteClose">
    <template #header>
      <div class="flex items-center">
        <div class="img"></div>
        <div class="header ml-10px">删除公告</div>
      </div>
    </template>
    <div class="pl-[30px] pr-[30px] !mt-[20px] delete"></div>
    <div class="text-center text-info">确定要删除吗？</div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteClose">取消</el-button>
        <el-button type="primary" @click="deleteSubmit" :loading="deleteLoading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import $API from '@/common/api'
import { useRouter } from 'vue-router'
import defaultPng from '../../staging/assets/default.png'
import { ElMessage } from 'element-plus'

const router = useRouter()
const noticeList = ref<any>([])
const total = ref<number>(0)
const curPage = ref<number>(1)
const pageSize = ref<number>(10)
const pageNo = ref<number>(1)
const deleteId = ref<string>('')
const deleteVisible = ref<boolean>(false)
const loading = ref<boolean>(false)
const deleteLoading = ref<boolean>(false)
function handleSizeChange(val: number) {
  pageSize.value = val
  getEventList()
}
function handleCurrentChange(val: number) {
  pageNo.value = val
  getEventList()
}

// 获取公告列表
function getEventList() {
  loading.value = true
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/myMessageList`,
      params: {
        messageClass: '2',
        pageNo: pageNo.value,
        pageSize: pageSize.value,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        noticeList.value = res.data.rows
        total.value = res.data.total
        pageNo.value = res.data.pageNo
        pageSize.value = res.data.pageSize
        loading.value = false
      }
    })
    .finally(() => {
      loading.value = false
    })
}

function goDetail(item: any) {
  router.push({
    name: 'noticeDetail',
    query: {
      id: item.id,
      messageId: item.messageId,
    },
  })
}

function handleCommand(command: string) {
  console.log('command -----> 🚀', command)
  const [action, itemIndex] = command.split('-')
  if (action === 'edit') {
    // 获取试题详情
    router.push({
      name: 'issueReport',
      query: {
        id: noticeList.value[itemIndex].id,
        messageId: noticeList.value[itemIndex].messageId,
      },
    })
  } else if (action === 'delete') {
    // 拿到试卷id
    deleteId.value = noticeList.value[itemIndex].id
    deleteVisible.value = true
  }
}

// 删除确认
function deleteSubmit() {
  deleteLoading.value = true
  $API
    .get({
      url: 'ehs-clnt-platform-service/workbench/msg/delMyMessage',
      params: {
        noticeId: deleteId.value,
      },
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        deleteLoading.value = false
        ElMessage.success('操作成功!')
        deleteVisible.value = false
        getEventList()
      }
    })
    .finally(() => {
      deleteLoading.value = false
    })
}

// 关闭删除弹框
function deleteClose() {
  deleteVisible.value = false
}

onMounted(() => {
  getEventList()
})
</script>

<style scoped lang="scss">
.todo-task {
  display: flex;
  background-color: #eef7ff;
  border-radius: 0 0 0 10px;

  .todo-right {
    flex: 1;
    position: relative;
    height: calc(100vh - 170px);
    border: none;
    background-color: #eef7ff;

    // height: 100%;
    :deep(.el-card__body) {
      height: 94%;
      position: relative;
    }

    .content {
      margin-bottom: 20px;
      background: #ffffff;
      border-radius: 10px 10px 10px 10px;
      border: 1px solid #dfe6f2;
      padding: 10px;
      display: flex;
      align-items: center;

      .img {
        width: 36px;
        height: 36px;
        margin-right: 10px;
        background: url('@/assets/image/notice-list.png') no-repeat;
        background-size: 100% 100%;
      }

      .messageTitle {
        width: 800px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .item {
        cursor: pointer;
        font-weight: 600;
        font-size: 14px;
        color: #222222;
        line-height: 20px;
      }

      .time {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 14px;
      }

      .text {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        margin-bottom: 10px;
      }

      .read {
        font-weight: 400;
        font-size: 16px;
        color: #5c5e66;
        line-height: 20px;
        display: flex;

        .read-status {
          font-weight: 400;
          font-size: 14px;
          color: rgba(51, 51, 51, 1);
        }

        .status {
          // padding: 1px 10px;
          border-radius: 4px;
          color: rgba(0, 181, 120, 1);
        }

        .status-unread {
          border-radius: 4px;
          color: rgba(231, 22, 22, 1);
        }
      }
    }

    // .btn-page {
    //   position: absolute;
    //   bottom: 10px;
    //   right: 20px;
    // }

    .not {
      display: flex;
      align-items: center;
      justify-content: center;
      height: calc(100% - 48px);
      position: absolute;
      left: 50%;
      transform: translateX(-50%);

      .not-description {
        font-weight: 400;
        font-size: 14px;
        color: #969799;
        line-height: 22px;
        text-align: center;
      }
    }

    .btn-page {
      width: 100%;
      margin-top: 18px;
      padding-right: 20px;
      text-align: right;
      display: flex;
      justify-content: flex-end;
    }
  }
}

.img {
  width: 18px;
  height: 12px;
  background: url('@/assets/image/drawer_bg.png') no-repeat;
  background-size: 100% 100%;
}

.delete {
  width: 72px;
  height: 72px;
  background: #d9dde8;
  margin: 0 auto;
  background: url('@/assets/image/exam-delete.png') no-repeat center;
  background-size: 100% 100%;
}
</style>
