<template>
  <div class="h-full todo-task">
    <el-card class="todo-left" v-loading="loading">
      <div v-if="noticeTabs.length">
        <div
          v-for="(item, index) in noticeTabs"
          :class="['list', { t_active: current === item.type }]"
          @click="tabChange(item)"
          :key="index"
        >
          {{ item.name }}({{ item.num }})
        </div>
      </div>
      <div v-else class="not">
        <div>
          <el-image :src="defaultPng"> </el-image>
          <div class="not-description">暂无事件监测</div>
        </div>
      </div>
    </el-card>
    <el-card class="todo-right">
      <el-scrollbar>
        <div v-if="eventList.length">
          <div v-for="(item, index) in eventList" :key="index" class="content">
            <div class="img"></div>
            <div class="flex-1">
              <div class="info">
                <div class="flex">
                  <div class="title">{{ item.messageTitle || item.unitName }}</div>
                  <!--                  <div class="status" v-if="item.isRead === 1">已读</div>-->
                  <!--                  <div class="status status-unread" v-else>待处理</div>-->
                  <!--                  <div class="status status-unread" v-else>未读</div>-->
                </div>
                <div class="time">接收时间：{{ item.pushTime || item.iotReceiveTime || '' }}</div>
              </div>
              <div class="text">
                {{ getContentText(item) }}
                <!--                {{ item.messageContent }}<span class="text-content">{{ item.strongMsgContent }}</span> ，请控制安全风险-->
              </div>
            </div>
          </div>
        </div>
        <div v-else class="not">
          <div>
            <el-image :src="defaultPng"> </el-image>
            <div class="not-description">暂无事件监测</div>
          </div>
        </div>
      </el-scrollbar>
      <div class="btn-page" v-if="total">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          v-model:current-page="curPage"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import $API from '@/common/api'
import { Clock } from '@element-plus/icons-vue'
import defaultPng from '../../staging/assets/default.png'
import { useUserInfo } from '~/store'

const current = ref('1')
const noticeTabs = ref<any>([])
const eventList = ref<any>([])
const total = ref(0)
const curPage = ref(1)
const pageSize = ref(10)
const pageNo = ref(1)
const loading = ref<boolean>(false)
const ui: any = useUserInfo()
const active = ref<string>('1')

// function changeTab(item: any) {
//   current.value = item.messageType
//   getEventList()
// }

// 获取左侧分类
// function getLeftTree() {
//   loading.value = true
//   return new Promise((resolve, reject) => {
//     $API
//       .get({
//         url: `ehs-clnt-platform-service/workbench/msg/queryMessageGroupMessageType`,
//         params: {
//           messageCategory: '2',
//           messageClass: '2',
//         },
//       })
//       .then((res: any) => {
//         if (res && res.code == 'success') {
//           loading.value = false
//           noticeTabs.value = res.data
//           current.value = res.data[0].messageType
//           resolve(res.data)
//         }
//       })
//       .catch((err) => {
//         reject(err)
//       })
//   })
// }
// 获取消息列表
// function getEventList() {
//   $API
//     .get({
//       url: `ehs-clnt-platform-service/workbench/msg/queryMessagePageList`,
//       params: {
//         messageCategory: '2',
//         messageClass: '2',
//         pageNo: pageNo.value,
//         pageSize: pageSize.value,
//         messageType: current.value,
//       },
//     })
//     .then((res: any) => {
//       if (res && res.code == 'success') {
//         eventList.value = res.data.rows
//         total.value = res.data.total
//         pageNo.value = res.data.pageNo
//         pageSize.value = res.data.pageSize
//       }
//     })
// }

const getContentText = (val) => {
  //
  if (val.messageContent || val.strongMsgContent)
    return val.messageContent + val.strongMsgContent + '，请控制安全风险。'
  return (
    val.buildingName +
    val.floorName +
    val.deviceAddress +
    val.deviceTypeName +
    '上报了' +
    val.description +
    '请及时前往查看, 请控制安全风险!'
  )
}

const tabChange = (val) => {
  current.value = val.type
  total.value = val.num
  getEventInfo(val.type)
}
const getEventInfo = async (type = 1) => {
  const res = await $API.post({
    url: `ehs-clnt-internetMonitor-service/monitor/getRealEventPage`,
    params: {
      eventType: type,
      orderFields: 'iotReceiveTime:desc',
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      orgCode: ui.value.unitId,
    },
  })
  eventList.value = res.data.rows
}

const getEventListDiv = async () => {
  const list: any = [
    {
      bg: 'fire',
      name: '火警',
      typeName: '火警',
      unit: '起',
      count: 0,
      type: 1,
      flag: 'alarmNum',
      isActiveTab: true,
      class: 'cursor-pointer',
      num: 0,
    },
    {
      bg: 'warning',
      name: '预警',
      unit: '起',
      count: 0,
      type: 2,
      flag: 'warningNum',
      isActiveTab: false,
      class: 'cursor-pointer',
      num: 0,
    },
    {
      bg: 'fault',
      name: '故障',
      unit: '处',
      count: 0,
      type: 3,
      flag: 'faultNum',
      isActiveTab: false,
      class: 'cursor-pointer',
      num: 0,
    },
    {
      bg: 'hidden-danger',
      name: '隐患',
      unit: '处',
      count: 0,
      type: 4,
      flag: 'hazardNum',
      isActiveTab: false,
      class: 'cursor-pointer',
      num: 0,
    },
  ]
  const params = {
    orgCode: ui.value.unitId,
  }
  const res = await $API.post({
    url: `ehs-clnt-internetMonitor-service/unit/monitor/getRealtimeAlarm`,
    params: {
      ...params,
    },
  })
  list.forEach((i) => {
    i.num = res.data[i.flag]
  })
  let _arr = list.filter((i) => i.num > 0)
  if (_arr.length > 0) {
    tabChange(_arr[0])
  }
  noticeTabs.value = list
}

function handleSizeChange(val: number) {
  pageSize.value = val
  getEventInfo(current.value)
}
function handleCurrentChange(val: number) {
  pageNo.value = val
  getEventInfo(current.value)
}

onMounted(async () => {
  // await getLeftTree()
  // getEventList()
  await getEventListDiv()
})
</script>

<style scoped lang="scss">
.todo-task {
  display: flex;
  background-color: #eef7ff;
  border-radius: 0 0 0 10px;

  .todo-left {
    width: 240px;
    height: calc(100vh - 170px);
    background: #ffffff;
    box-shadow:
      24px 6px 50px 0px rgba(0, 0, 0, 0.02),
      35px 0px 70px 0px rgba(86, 128, 248, 0.05),
      14px 0px 25px 0px rgba(86, 128, 248, 0.03);
    border-radius: 0 0 0 10px;
    border-right-color: #ffffff;
    border-top: none;

    .list {
      height: 54px;
      line-height: 54px;
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      margin-bottom: 12px;
      text-align: center;
      cursor: pointer;

      &:hover {
        color: #333333;
        background: rgba(0, 0, 0, 0.04);
        border-radius: 6px 6px 6px 6px;
      }
    }

    .t_active {
      color: rgba(82, 123, 254, 1);
      background: rgba(82, 124, 255, 0.15);
      border-radius: 6px 6px 6px 6px;
    }
  }

  .todo-right {
    flex: 1;
    position: relative;
    height: calc(100vh - 170px);
    border: none;
    background-color: #eef7ff;

    // height: 100%;
    :deep(.el-card__body) {
      height: 94%;
      position: relative;
    }

    .content {
      margin-bottom: 20px;
      background: #ffffff;
      border-radius: 10px 10px 10px 10px;
      border: 1px solid #dfe6f2;
      padding: 10px;
      display: flex;
      align-items: center;

      .img {
        width: 36px;
        height: 36px;
        margin-right: 10px;
        background: url('@/assets/image/event-list.png') no-repeat;
        background-size: 100% 100%;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;

        .status {
          margin-left: 20px;
          padding: 1px 10px;
          color: #fff;
          height: 20px;
          line-height: 20px;
          background: #00b578;
          border-radius: 3px 3px 3px 3px;
        }

        .status-unread {
          background: #fa5151;
        }

        .title {
          font-weight: 600;
          color: #222222;
          line-height: 20px;
          text-align: left;
          margin-bottom: 10px;
        }

        .time {
          font-weight: 400;
          color: #666666;
          line-height: 14px;
          text-align: right;
        }
      }

      .text {
        font-weight: 400;
        font-size: 14px;
        color: #222222;
        line-height: 20px;

        .text-content {
          color: rgba(82, 124, 255, 1);
        }
      }
    }

    .btn-page {
      width: 100%;
      margin-top: 18px;
      padding-right: 20px;
      text-align: right;
      display: flex;
      justify-content: flex-end;
    }
  }

  .not {
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(100% - 48px);
    position: absolute;
    left: 50%;
    transform: translateX(-50%);

    .not-description {
      font-weight: 400;
      font-size: 14px;
      color: #969799;
      line-height: 22px;
      text-align: center;
    }
  }
}
</style>
