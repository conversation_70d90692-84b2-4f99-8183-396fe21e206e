<template>
  <div class="h-full todo-task">
    <div class="tips">
      <div><span @click="back">通知公告</span>/新闻详情</div>
      <el-button type="primary" @click="back">返回</el-button>
    </div>
    <div class="h-full">
      <el-card class="todo-right" v-loading="loading">
        <el-scrollbar height="100%">
          <div class="item mt-10px">{{ noticeList.noticeName }}</div>
          <div class="time">
            <div class="flex mt-10px">
              <div class="mr-[30px]">
                已读：<span class="status">{{ noticeList.readed }}人</span>
              </div>
              <div>
                未读：<span class="status-unread">{{ noticeList.unread }}人</span>
              </div>
            </div>
            <div class="flex items-center ml-40px mt-10px">
              <el-icon class="mr-10px">
                <Clock />
              </el-icon>
              发布时间：{{ noticeList.createTime }}
            </div>
          </div>
          <div class="text" v-html="noticeList.noticeContent" @click="handleImageClick"></div>
          <el-image-viewer v-if="showViewer" :url-list="[previewUrl]" @close="showViewer = false" />
          <div class="file mt-60px">
            <div>附件:</div>
            <div
              v-for="(item, index) in noticeList.fileAttachmentList"
              :key="index"
              class="cursor-pointer mt-10px mb-10px"
              @click="fileUpdown(item)"
            >
              {{ item.fileName }}
            </div>
          </div>
        </el-scrollbar>
        <!-- v-if="total" -->
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import $API from '@/common/api'
import { useRouter, useRoute } from 'vue-router'
import { Clock } from '@element-plus/icons-vue'
import config from '~/config'
import { ElImageViewer } from 'element-plus'

const noticeList = ref<any>({})
const router = useRouter()
const route = useRoute()
const loading = ref<boolean>(false)
const showViewer = ref(false)
const previewUrl = ref('')

// 获取公告详情
function getDetail(noticeId, messageId) {
  loading.value = true
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/getNoticInfo`,
      params: {
        noticeId,
        messageId,
        messageClass: '2',
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        loading.value = false
        noticeList.value = res.data
      }
    })
}
function back() {
  router.push({ name: 'notifyTask', state: { tab: '2' } })
}

function downloadFileFromStream(binFile, fileName, blobType = 'application/vnd.ms-excel') {
  // 创建 Blob 对象，包含二进制文件流和文件类型
  const blobObj = new Blob([binFile], { type: blobType })
  // 创建下载链接元素
  const downloadLink = document.createElement('a')
  // 处理不同浏览器的 URL 兼容性
  let url: any = window.URL || window.webkitURL
  url = url.createObjectURL(blobObj) // 创建文件 URL
  // 设置下载链接属性
  downloadLink.href = url
  downloadLink.download = fileName
  // 将下载链接添加到文档中并触发点击事件
  document.body.appendChild(downloadLink)
  downloadLink.click()
  // 移除下载链接并释放 URL 对象
  document.body.removeChild(downloadLink)
  window.URL.revokeObjectURL(url)
}
function fileUpdown(item: any) {
  let name = item.path.split('.')[1]
  if (name === 'pdf') {
    window.open(config.downloadFileUrl + item.path)
  } else {
    $API
      .get({
        url: `ehs-clnt-platform-service/workbench/msg/download`,
        params: {
          id: item.id,
        },
        responseType: 'blob',
      })
      .then(async (res: any) => {
        console.log('res', res)
        downloadFileFromStream(res.data, item.fileName)
      })
  }
}

// 处理图片点击
function handleImageClick(e: MouseEvent) {
  const target = e.target as HTMLElement
  console.log('target', target)
  if (target.tagName === 'IMG') {
    previewUrl.value = (target as HTMLImageElement).src
    showViewer.value = true
  }
}

watch(
  () => route.query.id,
  (val) => {
    getDetail(val, route.query.messageId)
  },
  { immediate: true }
)

onMounted(() => {
  // getDetail(route.query.id)
})
</script>

<style scoped lang="scss">
.tips {
  height: 50px;
  cursor: pointer;
  color: #607590;
  font-weight: 400;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.todo-task {
  height: calc(100vh - 155px);
  .todo-right {
    position: relative;
    height: 100%;

    :deep(.el-card__body) {
      height: 100%;
    }

    .item {
      font-weight: 500;
      font-size: 24px;
      color: #061032;
      line-height: 20px;
      margin-bottom: 12px;
      color: rgba(82, 124, 255, 1);
    }

    .text {
      font-weight: 400;
      font-size: 16px;
      color: #061032;
      line-height: 20px;
      text-align: left;
      margin-bottom: 6px;
      margin-top: 40px;
    }

    .time {
      font-weight: 400;
      font-size: 16px;
      color: #5c5e66;
      line-height: 20px;
      display: flex;

      .status {
        // padding: 1px 10px;
        border-radius: 4px;
        color: #719f2f;
      }

      .status-unread {
        border-radius: 4px;
        color: rgba(231, 22, 22, 1);
      }
    }

    .file {
      font-weight: 400;
      font-size: 18px;
      color: #061032;
      line-height: 20px;
      margin-bottom: 12px;
      color: rgba(82, 124, 255, 1);
    }
  }
}
</style>
