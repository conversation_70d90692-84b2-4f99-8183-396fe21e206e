<template>
  <div class="h-full todo-task">
    <el-card class="todo-left" v-loading="loading">
      <el-scrollbar>
        <div v-if="noticeTabs.length">
          <div
            v-for="(item, index) in noticeTabs"
            :class="['list', { t_active: current === item.messageType }]"
            @click="changeTab(item)"
            :key="index"
          >
            {{ item.typeName }}({{ item.messageCount }})
          </div>
        </div>
        <div v-else class="not">
          <div>
            <el-image :src="defaultPng"> </el-image>
            <div class="not-description">暂无待办任务</div>
          </div>
        </div>
      </el-scrollbar>
    </el-card>
    <el-card class="todo-right">
      <el-scrollbar>
        <div v-if="riskLevelList.length">
          <div v-for="(item, index) in riskLevelList" :key="index" class="content" @click="jump(item)">
            <div class="img"></div>
            <div class="flex-1">
              <div class="info">
                <div class="flex">
                  <div class="title">{{ item.messageTitle }}</div>
                  <!-- <div class="status" v-if="item.isRead === 1">已读</div>
                  <div class="status status-unread" v-else>未读</div> -->
                </div>
                <div class="time">接收时间：{{ item.pushTime }}</div>
              </div>
              <div class="text">
                {{ item.messageContent }}<span class="text-content">{{ item.strongMsgContent }}</span>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="not">
          <div>
            <el-image :src="defaultPng"> </el-image>
            <div class="not-description">暂无待办任务</div>
          </div>
        </div>
      </el-scrollbar>
      <div class="btn-page" v-if="total">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          v-model:current-page="curPage"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import $API from '@/common/api'
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'
import { useUserInfo } from '~/store'
import defaultPng from '../../staging/assets/default.png'
const userInfo = useUserInfo()
const current = ref('1')
const noticeTabs = ref<any>([])
const riskLevelList = ref<any>([])
const total = ref(0)
const curPage = ref(1)
const pageSize = ref(10)
const pageNo = ref(1)
const loading = ref<boolean>(false)

function changeTab(item: any) {
  current.value = item.messageType
  getTaskList()
}
function handleSizeChange(val: number) {
  pageSize.value = val
  getTaskList()
}
function handleCurrentChange(val: number) {
  pageNo.value = val
  getTaskList()
}
// 获取左侧分类
function getLeftTree() {
  loading.value = true
  return new Promise((resolve, reject) => {
    $API
      .get({
        url: `ehs-clnt-platform-service/workbench/msg/queryMessageGroupMessageType`,
        params: {
          messageCategory: '1',
          messageClass: '2',
        },
      })
      .then((res: any) => {
        if (res && res.code == 'success') {
          loading.value = false
          noticeTabs.value = res.data
          current.value = res.data[0].messageType
          resolve(res.data)
        }
      })
      .catch((err) => {
        reject(err)
      })
  })
}

// 获取消息列表
function getTaskList() {
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/queryMessagePageList`,
      params: {
        messageCategory: '1',
        messageClass: '2',
        pageNo: pageNo.value,
        pageSize: pageSize.value,
        messageType: current.value,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        riskLevelList.value = res.data.rows
        total.value = res.data.total
        pageNo.value = res.data.pageNo
        pageSize.value = res.data.pageSize
      }
    })
}

// 点击跳转至各子系统
function jump(item) {
  if (item.msgType == '9') {
    return ElMessage.warning('请使用招商随行APP或小程序进行学习')
  }
  if (!item.routePath) {
    return
  }
  if (item.messageTitle === '隐患复查') {
    ElMessage.warning('请前往移动端-隐患排查治理-隐患复查中处理')
    return
  }
  let goUrl
  $API
    .post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: item.sysCode,
        userId: userInfo.value.id,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        if (item.sysCode === 'scene_manage') {
          // 现场管理
          const path = item.routePath.split('#')[1]
          const cleanPath = path.replace(/^\/+/, '')
          goUrl =
            item.routePath.split('#')[0] + '?token=' + res.data.token + '&sysCode=' + item.sysCode + '#/' + cleanPath
        } else if (
          item.sysCode === 'risk_level' ||
          item.sysCode === 'hazard_inves' ||
          item.sysCode === 'safe-operation'
        ) {
          // 风险管控
          if (item.routePath.includes('?')) {
            goUrl = item.routePath + '&token=' + res.data.token + '&sysCode=' + item.sysCode
          } else {
            goUrl = item.routePath + '?token=' + res.data.token + '&sysCode=' + item.sysCode
          }
        } else {
          goUrl = item.routePath + '?token=' + res.data.token + '&sysCode=' + item.sysCode
        }
        window.open(goUrl, '_blank')
      }
    })
}

onMounted(async () => {
  await getLeftTree()
  getTaskList()
})
</script>

<style scoped lang="scss">
.todo-task {
  display: flex;
  background-color: #eef7ff;
  border-radius: 0 0 0 10px;

  .todo-left {
    width: 240px;
    height: calc(100vh - 170px);
    background: #ffffff;
    box-shadow:
      24px 6px 50px 0px rgba(0, 0, 0, 0.02),
      35px 0px 70px 0px rgba(86, 128, 248, 0.05),
      14px 0px 25px 0px rgba(86, 128, 248, 0.03);
    border-radius: 0 0 0 10px;
    border-right-color: #ffffff;
    border-top: none;

    :deep(.el-card__body) {
      height: 100%;
      position: relative;
    }

    .list {
      height: 54px;
      line-height: 54px;
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      margin-bottom: 12px;
      text-align: center;
      cursor: pointer;

      &:hover {
        color: #333333;
        background: rgba(0, 0, 0, 0.04);
        border-radius: 6px 6px 6px 6px;
      }
    }

    .t_active {
      color: rgba(82, 123, 254, 1);
      background: rgba(82, 124, 255, 0.15);
      border-radius: 6px 6px 6px 6px;
    }
  }

  .todo-right {
    flex: 1;
    position: relative;
    height: calc(100vh - 170px);
    border: none;
    background-color: #eef7ff;

    // height: 100%;
    :deep(.el-card__body) {
      height: 94%;
    }

    .content {
      margin-bottom: 20px;
      background: #ffffff;
      border-radius: 10px 10px 10px 10px;
      border: 1px solid #dfe6f2;
      padding: 10px;
      display: flex;
      align-items: center;
      cursor: pointer;

      .img {
        width: 36px;
        height: 36px;
        margin-right: 10px;
        background: url('@/assets/image/task-list.png') no-repeat;
        background-size: 100% 100%;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;

        .status {
          height: 20px;
          margin-left: 20px;
          padding: 1px 10px;
          font-size: 12px;
          color: #fff;
          height: 20px;
          line-height: 20px;
          background: #00b578;
          border-radius: 3px 3px 3px 3px;
          color: #ffffff;
        }

        .status-unread {
          // margin-left: 20px;
          background: #fa5151;
        }

        .title {
          font-weight: 600;
          color: #222222;
          line-height: 20px;
          text-align: left;
          margin-bottom: 10px;
        }

        .time {
          font-weight: 400;
          color: #666666;
          line-height: 14px;
          text-align: right;
        }
      }

      .text {
        font-weight: 400;
        font-size: 14px;
        color: #222222;
        line-height: 20px;

        .text-content {
          color: rgba(82, 124, 255, 1);
        }
      }
    }

    .btn-page {
      width: 100%;
      margin-top: 18px;
      padding-right: 20px;
      text-align: right;
      display: flex;
      justify-content: flex-end;
    }
  }

  .not {
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(100% - 48px);
    position: absolute;
    left: 50%;
    transform: translateX(-50%);

    .not-description {
      font-weight: 400;
      font-size: 14px;
      color: #969799;
      line-height: 22px;
      text-align: center;
    }
  }
}
</style>
