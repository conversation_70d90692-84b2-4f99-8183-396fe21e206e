<template>
  <div class="tips"><span @click="back">通知公告</span>/发布新闻</div>
  <el-card class="edit">
    <el-scrollbar>
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="auto" class="demo-ruleForm">
        <el-form-item label="通知名称" prop="noticeName">
          <el-input v-model="ruleForm.noticeName" placeholder="请输入通知名称" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="选择对象" prop="noticeObjName">
          <el-input v-model="ruleForm.noticeObjName" placeholder="请选择通知对象" @click="showPreson" readonly>
            <template #append>
              <el-button type="primary" plain size="small" @click="showPreson">选择</el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="通知内容" prop="noticeContent" :rules="rulsFn()">
          <WangEditor v-model="ruleForm.noticeContent" />
        </el-form-item>
        <div class="upload_file">
          <el-form-item label="附件">
            <el-upload
              accept=".pdf,.doc,.docx,.png,.jpg,.jpeg"
              class="avatar-uploader"
              :limit="3"
              v-model:file-list="fileList"
              :on-exceed="handleExceed"
              :on-success="handleSuccess"
              :before-upload="beforeUpload"
              name="uploadfile"
              :action="url"
            >
              <div>
                <div class="w_file_btn">+ 点击上传</div>
                <div class="w_file_t">支持上传doc、docx、pdf、png、jpg、jpeg格式，最多支持上传3个附件</div>
              </div>
            </el-upload>
          </el-form-item>
        </div>
      </el-form>
    </el-scrollbar>
    <div class="dialog-footer">
      <el-button @click="cancle">取消</el-button>
      <el-button type="primary" @click="save" :loading="loading">保存</el-button>
    </div>
    <SelectUsers ref="selectUsersRef" @selectedUser="selectedUser"></SelectUsers>
  </el-card>
</template>

<script setup lang="ts">
import SelectUsers from '@/view/components/selectUsers/index.vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { nextTick, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import $API from '~/common/api'
import WangEditor from '~/components/wangeditor.vue'
import config from '~/config'

interface RuleForm {
  noticeId: string
  noticeName: string
  noticeids: string
  noticeObjName: string
  noticeContent: string
}
const router = useRouter()
const route = useRoute()
const ruleFormRef = ref<FormInstance>()
const selectUsersRef = ref()
const url = ref(config.update_file + '/file/uploadfile') // 上传路径
const selectArr = ref<any>([])
// 文件上传列表
const fileList = ref<any>([])
const ruleForm = ref<RuleForm>({
  noticeId: '',
  noticeName: '',
  noticeids: '',
  noticeObjName: '',
  noticeContent: '',
})
const isDeptArr = ref<any[]>([])
const noticeObj = ref<{ noticeObjId: string; noticeObjName: string; isDept: number }[]>([])
const noticeUnit = ref<{ noticeObjId: string; noticeObjName: string; isDept: number }[]>([])
const isUserDeptIdArr = ref<string[]>([])
const loading = ref<boolean>(false)

const getPureTextLength = (content) => {
  // 去掉 HTML 标签，<br> 标签也需要处理
  const pureText = content
    .replace(/<\/?[^>]+(>|$)/g, '')
    .replace(/<br\s*\/?>/g, '\n')
    .trim()
  return pureText.length
}
const validateTitle = (rule, value, callback) => {
  if (value === '<p><br></p>') {
    callback(new Error('请输入通知内容'))
  } else {
    callback()
  }
}
const rulsFn = () => [
  {
    required: true,
    message: '请输入通知内容',
    trigger: ['blur', 'change'],
  },
  { validator: validateTitle, trigger: ['blur', 'change'] },
]
const rules = reactive<FormRules<RuleForm>>({
  noticeName: [
    { required: true, message: '请输入通知名称', trigger: ['blur', 'change'] },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value.length > 100) {
          callback(new Error('通知名称不可超过100字'))
        } else {
          callback()
        }
      },
      trigger: ['blur', 'change'],
    },
  ],
  noticeObjName: [
    {
      required: true,
      message: '请选择对象',
      trigger: ['blur', 'change'],
    },
  ],
  noticeContent: [
    {
      required: true,
      message: '请输入通知内容',
      trigger: ['blur', 'change'],
    },
  ],
})

function initForm() {
  return (ruleForm.value = {
    noticeId: '',
    noticeName: '',
    noticeids: '',
    noticeObjName: '',
    noticeContent: '',
  })
}
function selectedUser(list: any) {
  const uniqueList = list.filter((item, index, self) => index === self.findIndex((t) => t.id === item.id))
  list = uniqueList
  noticeObj.value = []
  noticeUnit.value = []

  // 根据isDept字段区分部门和人员
  list.forEach((item: any) => {
    if (item.isDept == 1) {
      // 部门对象
      noticeUnit.value.push({
        noticeObjId: item.id,
        noticeObjName: item.userName,
        isDept: item.isDept,
      })
    } else {
      // 人员对象
      noticeObj.value.push({
        noticeObjId: item.id,
        noticeObjName: item.userName,
        isDept: item.isDept,
      })
    }
  })

  // 更新表单数据
  ruleForm.value.noticeObjName = list.map((item: any) => item.userName).join(',')
  ruleForm.value.noticeids = list.map((item: any) => item.id).join(',')
  isDeptArr.value = list
}
// 显示人员弹框
function showPreson() {
  selectUsersRef.value.outerVisible = true
  if (ruleForm.value.noticeids && ruleForm.value.noticeObjName) {
    nextTick(() => {
      selectUsersRef.value!.toggleSelection(isDeptArr.value)
      selectUsersRef.value!.toggleSelectDepts(isDeptArr.value)
    })
  }
}
//超出文件
function handleExceed() {
  ElMessage.error('最多上传3个附件！')
}

function beforeUpload(rawFile) {
  const white = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword',
    'image/png',
    'image/jpeg',
    'image/jpg',
  ]
  if (!white.includes(rawFile.type)) {
    ElMessage.error('仅支持doc、docx、pdf、png、jpg、jpeg格式文件！')
    return false
  } else if (rawFile.size / 1024 / 1024 > 10) {
    ElMessage.error('附件最大不能超过10MB!')
    return false
  } else {
    return true
  }
}
// 上传文件
function handleSuccess() {
  ElMessage({
    message: '上传文件成功',
    type: 'success',
  })
}
function back() {
  initForm()
  router.push({ name: 'notifyTask', state: { tab: '2' } })
}

function cancle() {
  initForm()
  router.push({ name: 'notifyTask', state: { tab: '2' } })
}

function save() {
  loading.value = true
  let fileAttachmentList: any = []
  fileList.value.forEach((item: any) => {
    let data = item.response.data
    fileAttachmentList.push({
      fileName: data.fileName,
      id: data.id,
      path: data.filePath,
    })
  })
  let params = {
    id: ruleForm.value.noticeId,
    noticeName: ruleForm.value.noticeName,
    noticeContent: ruleForm.value.noticeContent,
    noticeObj: noticeObj.value,
    noticeUnit: noticeUnit.value,
    fileAttachmentList,
  }
  ruleFormRef.value!.validate((valid: boolean) => {
    if (!valid) {
      loading.value = false
      return
    }
    $API
      .post({
        url: 'ehs-clnt-platform-service/workbench/msg/saveNoticInfo',
        data: params,
      })
      .then((res: any) => {
        if (res && res.code == 'success') {
          loading.value = false
          ElMessage.success('发布成功')
          back()
        } else {
          loading.value = false
        }
      })
      .catch(() => {
        loading.value = false
      })
  })
}
// 编辑更新属性
function updateObj(formObj, data) {
  Object.keys(formObj).forEach((key) => {
    // eslint-disable-next-line no-prototype-builtins
    if (data.hasOwnProperty(key)) {
      formObj[key] = data[key]
    }
  })
}
// 获取公告详情
function getDetail(noticeId, messageId) {
  // loading.value = true
  fileList.value = []
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/getNoticInfo`,
      params: {
        noticeId,
        messageId,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        // loading.value = false
        // ruleForm.value = res.data
        updateObj(ruleForm.value, res.data)
        res.data.fileAttachmentList!.forEach((item) => {
          fileList.value.push({
            name: item.fileName,
            response: {
              data: {
                id: item.id,
                fileName: item.fileName,
                filePath: item.path,
              },
            },
          })
        })
      }
    })
}

watch(
  () => route.query.id,
  (val) => {
    if (val) getDetail(val, route.query.messageId)
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.tips {
  height: 50px;
  cursor: pointer;
  color: #607590;
  font-weight: 400;
  line-height: 50px;
}

.edit {
  height: calc(100vh - 155px);
  padding-top: 20px;

  :deep(.el-card__body) {
    height: 97%;
  }
}

.upload_file {
  margin-top: 35px;

  .w_file_btn {
    width: 136px;
    height: 32px;
    border: 1px solid #527cff;
    text-align: center;
    color: #527cff;
    cursor: pointer;
    border-radius: 4px;
    height: 32px;
  }

  .w_file_t {
    color: #a8abb2;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
}
</style>
