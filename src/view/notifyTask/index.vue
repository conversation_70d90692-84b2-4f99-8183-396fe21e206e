<template>
  <div class="w-full relative">
    <HeadTab :changeTabs="changeTabs" @changeTab="changeTab"></HeadTab>
    <div class="release" v-if="current === 'NoticeReport'" @click="publishReport">发布新闻</div>
    <div class="h-full mt-[-8px]">
      <el-scrollbar>
        <component :is="tabs[current]" ref="tableRef"></component>
      </el-scrollbar>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import HeadTab from '@/components/HeadTab/index.vue'
import ToDoTask from './comp/ToDoTask.vue'
import NoticeReport from './comp/NoticeReport.vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const changeTabs = [
  { label: '待办任务', value: '1' },
  { label: '安全新闻', value: '2' },
]
const tabs = {
  ToDoTask,
  NoticeReport,
}
const current = ref('ToDoTask')

function changeTab(item: any) {
  let num = item.value
  switch (num) {
    case '1':
      current.value = 'ToDoTask'
      break
    case '2':
      current.value = 'NoticeReport'
      break
    default:
      break
  }
}

function publishReport() {
  router.push({ path: '/issueReport' })
}

watch(
  () => window.history.state.tab,
  (val) => {
    // if (val === '3') changeTab({ value: '3' })
    if (val) changeTab({ value: val })
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.release {
  position: absolute;
  top: 10px;
  right: 0;
  width: 100px;
  height: 32px;
  background-color: rgb(10 77 208);
  border-radius: 4px;
  color: #fff;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
}

:deep(.el-card.is-always-shadow) {
  box-shadow: none;
}
</style>
