<template>
  <div class="post-box">
    <iframe :src="url" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import config from '~/config'
import { useRouter } from 'vue-router'

const router = useRouter()
window.addEventListener('message', (event) => {
  if (event.data.target && event.data.target === 'metamask-inpage') {
    return false
  } else {
    const data = JSON.parse(event.data)
    // 验证消息来源
    if (data.name === 'member') {
      console.log('Received message:', data)
      router.push({
        name: 'member',
        state: {
          id: data.id,
          text: data.text,
        },
      })
    }
  }
})
const url = ref(config.jumpUrlOrgManagement + 'organizational')
defineOptions({ name: 'userOrganizational' })
</script>

<style scoped lang="scss">
.post-box {
  width: 100%;
  height: 100%;

  iframe {
    width: 100%;
    height: 100%;
  }
}
</style>
