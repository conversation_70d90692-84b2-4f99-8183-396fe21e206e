interface Window {
  $IDSS_CFG: any
  $mainStore: any
  $_getUrlParams: any

  GISShare: any
  IndoorMap: any
  ol: any
  gisIns: any
  GSMap: any
  CONST_GsSysParams_ACS: any
  CONST_ACS_WMTSLayer_Map: any
  CONST_ACS_WMTSLayer_Satellite: any
  lmsDisplay: any
  GetLMSDataOptionsByAdminCode: any
  BP3DGis: any
  SockJS: any
  Stomp: any
}

declare module '@kalimahapps/vue-icons'

declare module '*.svg'
declare module '*.png'
declare module '*.jpg'
declare module '*.jpeg'
declare module '*.gif'
declare module '*.bmp'
declare module '*.tiff'

declare const THREE: any
declare const CameraControls: any
declare const IndoorThree: any
declare const CONST_GeoData_China: any
declare const CONST_GeoData_China_line: any
declare const CONST_GeoData_Item: any
declare const CONST_GeoData_Item_inline: any
declare const CONST_GeoData_Item_outline: any
declare const GISShare: any

declare const CONST_map_annotation_amap: any
declare const CONST_map_vector_amap: any
declare const SGlobe: any
declare const IndoorGlobe: any
