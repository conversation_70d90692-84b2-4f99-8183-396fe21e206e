export interface PageModel {
  pageNo: number
  pageSize: number
  total: number
}

export interface MenuItem {
  icon?: string
  name?: string
  title: string
  id: string
  children?: MenuItem[]

  showChild?: boolean
  parent?: MenuItem | undefined
}

export interface Resource {
  id: string
  resName: string
  resIcon: string
  resAlias: string
  resUrl: string
  children: Resource[]
}

export interface UserInfo {
  [x: string]: any
  authId: string
  loginName: string
  nickName: string
  unitId: string
  roleIds: string
  roleNames: string
  serviceModelCode: string
  subCenterCode: string
  sysCode: string
  unitAddress: string
  unitName: string
  updateUserId: string
  userName: string
  userSex: string
  userTelphone: string
  userToken: string
  userType: string
  userId: string
  isAerialMap: number
  aerialMapType: number
  floorMapType: number | string
  unitType: string
  bitmap: string
  ownerType: string
  aerialviewImg: string
  photoUrl: string
  hasSupervise: string
  hasDiagram: string
  hasSheild: string
  resourceList: Resource[]
  _userId: string
  _hasMaintenance: boolean
  _isGroup: boolean
  _groupId?: string
  _groupName?: string
}

export interface Message {
  unitType: any
  businessId: string
  createTime: string
  isClear?: number
  isRead?: number
  messageContent: string
  messageId: string
  messageState?: string
  messageTitle: string
  messageType: string
  receiveUserId?: string
  unitId?: string
  icon?: string
  deviceId?: string
  monitorReceiveTime?: string
  eventSource?: string
  itemType?: string
  deviceTypeName?: string
  systemLogo: string
  systemName: string
  disposeId?: string
  remake?: any
}

export interface Socket {
  socket: any
}

export interface SocketOptions {
  topic: string
  headers?: any
}
