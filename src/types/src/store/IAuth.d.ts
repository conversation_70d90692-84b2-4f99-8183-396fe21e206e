import { ILoginRes } from '../ires'
import { StoreDefinition } from 'pinia'

export interface IAuthState {
  /** 用户信息 */
  userInfo: Partial<ILoginRes>
}

export interface IAuthGetter {
  // 是否登录
  isLogin: boolean
  // 是否总队
  isZong: boolean
  // 是否支队
  isZhi: boolean
  // 是否大队
  isDa: boolean
  // 行业
  isIndustry: boolean
  // 不显示消防管辖
  showOrgCode: boolean
  // 是否为消防机构
  isInstitution: boolean
}

export interface IAuthAction {
  login: (data: any) => Promise
  logout: (opt?: any) => Promise
  resetAuthStore: () => void
  setAuthorization: (authorization: string) => void
  setInstitutionCode: (institutionCode: string) => void
  setPageOrgCode: (pageOrgCode: object) => void
  setGuidUserJumpVoOptions: (authorization: any) => void
  setPermissions: (permission: any) => void
}

export type IAuthStore = StoreDefinition<string, IAuthState, IAuthGetter, IAuthAction>
