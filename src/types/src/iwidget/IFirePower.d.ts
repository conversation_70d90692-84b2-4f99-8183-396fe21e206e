interface IDcInfo {
  all: number
  dc: number
}

interface IZbItem {
  jdchmhp: string
  jyzmc: string
  zblx: string
  zbsl: number
}

interface ICar {
  bbh: string
  cjrWybs: string
  cjsj: string
  clqwztlbmc: string
  dpclTywysbm: string
  gxrWybs: string
  gxsj: string
  jqcjTywysbm: string
  xfclCdsj: string
  xfclCllxdm: string
  xfclCllxmc: string
  xfclCssj: string
  xfclDdsj: string
  xfclGdsj: string
  xfclJdchphm: string
  xfclTssj: string
  xfclTywysbm: string
  xfclZtfhsj: string
  xfcldjmc: string
  xfclzzgnmc: string
  xfjyjgTywysbm: string
  xfzbqcflmc: string
  zhzsRj: number
  zzalpmRj: number
  zzblpmRj: number
}

interface IMen {
  bbh: number
  cdcs: number
  cjrWybs: number
  cjsj: number
  dpryTywysbm: number
  gxrWybs: number
  gxsj: number
  jdchphm: number
  jqcjTywysbm: number
  sfDdgb: number
  sfzjPdbz: number
  sjszjgTywysbm: number
  xfjgryTywysbm: number
  xfjyjgTywysbm: number
  xm: number
  gwlb: number
}

export interface IStationItem {
  alpm: number
  blpm: number
  carList: ICar[]
  carNum: number
  dzmc: string
  menList: IMen[]
  personNum: number
  rj: number
  sfzz: string
  cdfzr: string
  cdfzrdhhm: string
  jqcjTywysbm: string
  xfjyjgTywysbm: string
  xfjyjgTywymc: string
  sftb: number
}

export interface IPowerDetailRes {
  carNum: number
  equipNum: number
  hclyl: string
  personum: number
  stationNum: number
  zbMap: IDcInfo
  clMap: IDcInfo
  ryMap: IDcInfo
  station: IDcInfo
  stationList: IStationItem[]
  zbList: IZbItem[]
  dwNum: number
  ztNum: number
  dwList: IDWItem[]
  others: IOthers[]
}

export interface IDWItem {
  dwmc: string
  lxr: string
  lxdh: string | number
}

interface IOthers {
  xfjyjgTywymc: string
  ifzz: boolean
  ry: ICzMans[]
}

interface ICzMans {
  dpryTywysbm: string
  jqcjTywysbm: string
  jqTywysbm: string
  xfclTywysbm: string
  xfjgryTywysbm: string
  xm: string
  dh: string
  sjszjgTywysbm: string
  sfzjPdbz: string
  sfDdgb: number
  cjsj: string
  cjrWybs: string
  gxsj: string
  gxrWybs: string
  bbh: string
  cdryJcxx: string
  sort: string
  xfjyjgTywysbm: string
  jdchphm: string
  cdcs: number
  gwlb: string
}
