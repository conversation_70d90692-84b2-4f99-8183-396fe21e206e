export interface IFireInfoRes {
  bjdh: string // 报警电话
  bkrs: number // 被困人数
  jqBh: string // 编号
  jqDjDm?: string // 警情等级代码
  jqDjDmMc: string // 警情等级
  jqDz: string // 警情地址
  jqLxDm?: string // 警情类型代码
  jqLxMc: string // 警情类型名称
  jqTywysbm?: string // 警情识别码
  jqdw: string // 警情单位
  jqms: string // 警情描述
  jqsj: string // 警情时间
  jqzt: string // 警情状态
  bjsj: string // 报警时间
  jqztmc?: string // 警情状态代码
  lcgd: number // 楼层高度
  rsmj: number // 燃烧面积
  rswz: string // 燃烧物质
  sbzt: string // 申报状态
  ywqk: string // 烟雾情况
  zzjyz: string // 主责救援站
  zhyXm?: string // 指挥员姓名
  zhyDh?: string // 指挥员电话
  zyjq: number // 重要警情
  jqdxJqdxlbdm: string // 警情对象类别代码
  jqdxJqdxlbmc: string // 警情对象类别名称
  jzwjglxdm: string // 建筑物结构类型代码
  jzwjglxmc: string // 建筑物结构类型名称
  jasj: string // 结案时间
}

export interface IFireStatusRes {
  jqztLbmc: string // 警情状态名称
  cjsj: string // 出警时间
}

export interface IGroupMeetingRes {
  bjdh: string // 报警电话
  bkrs: number // 被困人数
  jqdjmc: string // 警情等级名称
  jqdz: string // 警情地址
  jqlxmc: string // 警情类型名称
  jqms: string // 警情描述
  jyz: string // 救援站
  bjsj: string // 报警时间
  lasj: string // 立案时间
  rslc: number // 燃烧楼层
  rsmj: number // 燃烧面积
  rswz: string // 燃烧物质
  tqqk: string // 天气情况
  ywqk: string // 烟雾情况
  lywjdz?: string // 录音文件地址
  dpdz: number
  dpcl: number
  dpry: number
  zbqc: number
  zyjq: number
}

export interface IMeetingListRes {
  message: string // 信息内容
  messageType: number // 信息类型
  messageTypeName: string // 信息类型名称
  sendDeptName: string // 发送机构名称
  sendTime: string // 发送时间
  sendUserName: string // 发送人名称
  sendUserId: string // 发送人id
}

export interface ISendMeeting {
  message: string // 发送信息
  messageType: number // 发送信息类型
  messageTypeName: string // 发送信息类型名称
  warnId: string // 警情id
}
