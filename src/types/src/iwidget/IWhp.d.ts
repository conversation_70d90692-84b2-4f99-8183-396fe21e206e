import { IPageRes } from '@/types/src/common'

export interface IBqItem {
  bqTywysbm: string
  bqmc: string
  bqz: string
  cjrWybs: string
  cjsj: string
  fgs: number
  gxrWybs: string
  gxsj: string
  xzfs: number
  zt: number
}

export interface IWhpItem {
  bhzqy: string
  bm: string
  bzsx: string
  bzxx: string
  cash: string
  cjrWybs: string
  cjsj: string
  dx: string
  emergKnowWhpBqList: IBqItem[]
  fdFc: string
  fjwd: string
  ghsJsc: string
  gxrWybs: string
  gxsj: string
  hjwh: string
  hxfy: string
  jjcs: string
  jjw: string
  ljwd: string
  ljyl: string
  md: string
  mhff: string
  nd: string
  ph: string
  rd: string
  rjx: string
  rsr: string
  rsybzwhx: string
  sd: string
  sjtbsj: string
  wgyxz: string
  whpTywysbm: string
  wxxSm: string
  xdmd: string
  xdzqmd: string
  xlyjcz: string
  xsSfpxs: string
  zdbx: string
  zrwd: string
  zt: number
  zwmc: number
}

export type IPageBqRes = IPageRes<IBqItem>

export type IBqList = IBqItem[]

export type IPageWhpRes = IPageRes<IWhpItem>
