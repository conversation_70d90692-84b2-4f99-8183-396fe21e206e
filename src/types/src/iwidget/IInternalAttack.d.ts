interface IInternalAttackList {
  address: string
  commandCode: string
  communicationChannel: string
  entranceCommander: string
  files: string
  groupCount: number
  inFive: string
  internalAttackId: string
  jqTywysbm: string
  memberCount: string
  safetyPrecautions: string
  status: string
}

interface IInternalAttack {
  internalAttackId: string
  commandCode: string
  communicationChannel: string
  address: string
  safetyPrecautions: string
  entranceCommander: string
  jqTywysbm: string
  files: string
  groupCount: number
  inFive: string
  status: string
  memberCount: string
}

interface IMember {
  member_id: string
  member: string
  heartbeat: string
  airCall: string
  airCallCode: string
  gasConsumption: string
  pressure: string
}

export interface IGroup {
  groupId: string
  internalAttackId: string
  groupNum: string
  commanderId: string
  commander: string
  duration: string
  startTime: string
  endTime: string
  runningTime: string
  status: string
  members: IMember[]
}

export interface IInternalAttackRes {
  internalAttack: IInternalAttack
  group: IGroup[]
}
