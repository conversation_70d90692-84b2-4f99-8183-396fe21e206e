import { IPageRes } from '../common'

export interface IEmergUnitItem {
  dwMc: string
  dzMc: string
  lxrLxdh: string
  lxrXm: string
  xfjyjgMc: string
  xfjyjgTywysbm: string
  xzqyMc: string
  yjlddwTywysbm: string
  yjlddwlbMc: string
  yjlddwlbdm: string
}

export type IGetEmergUnitRes = IPageRes<IEmergUnitItem>

export interface ILqldUnitItem {
  dwMc: string
  dzMc: string
  lqbzdwTywysbm: string
  lqbzdwlbdm: string
  lqbzdwlbdmMc: string
  lxrDhhm: string
  lxrXm: string
  xfjyjgMc: string
  xfjyjgTywysbm: string
  xzqhdm: string
  xzqhdmMc: string
}
export type IGetLqbzRes = IPageRes<ILqldUnitItem>

export interface IZydwItem {
  dwMc: string
  dwcs: string
  dwdz: string
  dwjbMc: string
  dwjbdm: string
  dwlbMc: string
  dwlbdm: string
  dwrs: string
  gldw: string
  lxdh: string
  lxry: string
  xfdzlxMc: string
  xfdzlxdm: string
  xfjyjgMc: string
  xfjyjgTywysbm: string
  xzqyBm: string
  xzqyMc: string
  zydwTywysbm: string
}
export type IGetZydwRes = IPageRes<IZydwItem>

export interface IQtllItem {
  clSl: number
  dwMc: string
  dzMc: string
  gldwDwMc: string
  lxrDhhm: string
  lxrXm: string
  qtjyllTywysbm: string
  qtjylltzlbdm: string
  qtjylltzlbdmMc: string
  rs: number
  xfdzlxdm: string
  xfdzlxdmMc: string
  xfjyjgMc: string
  xfjyjgTywysbm: string
  xzqhdm: string
  xzqhdmMc: string
}
export type IGetQtllRes = IPageRes<IQtllItem>

export interface IMhjyzjItem {
  dhhm: string
  mhjyzjTywysbm: string
  sfdwnbzjPdbz: string
  szdwDwMc: string
  xbdm: string
  xbdmMc: string
  xfjyjgMC: string
  xfjyjgTywysbm: string
  xm: string
  xzqhdm: string
  xzqhdmMc: string
  zjlylbdm: string
  zjlylbdmMc: string
}
export type IGetMhjyzjRes = IPageRes<IMhjyzjItem>
