export interface IDocument {
  bbh: string
  btMc: string
  cjrWybs: string
  cjsj: string
  fjDzwjlxdm: string
  fjDzwjwz: string
  fjMc: string
  fjTywysbm: string
  fkRqsj: string
  fkjgDwmc: string
  fkjgTywysbm: string
  fkrTywysbm: string
  fkrXm: string
  gxrWybs: string
  gxsj: string
  jlrTywysbm: string
  jlrXm: string
  jqTywysbm: string
  jqhcwsTywysbm: string
  jqwszllbdm: string
  jqwszllbmc: string
  sjly: string
  wsnrJyqk: string
  xxlx: string
  isExpand?: boolean
}

type msgType = '106' | '107' | '108' | '109' | '110' | '111'

export interface IDocumentSend {
  jqTywysbm: string
  jqwszllbdm: '101' | '102'
  btMc?: string
  wsnrJyqk?: string
  fjTywysbm?: string
  fjDzwjwz?: string
  fjMc?: string
  fjDzwjlxdm?: msgType
  xxlx: msgType
}

export type IDocumentRes = IDocument[]
