interface IJson {
  type: string
  value: string
  aa: string
}

interface ITimeLine {
  createRoleName: string
  createUserId: string
  createUserName: string
  id: string
  jqTywysbm: string
  json: string
  jsonParse: IJson[]
  recordTime: string
  recordType: string
}

export type ITimeLineRes = ITimeLine[]

interface IRiskReportItem {
  content: string
  type: string
}

export interface IRiskDetailRes {
  analysis: IRiskReportItem[]
  aroundRisk: IRiskReportItem[]
  base: IRiskReportItem[]
  time: string
  title: string
}
