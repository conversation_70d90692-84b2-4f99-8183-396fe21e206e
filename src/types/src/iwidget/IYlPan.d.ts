export interface IGetYlPlanDetailRes {
  cyjgTywysbm: string
  cyjgTywysmc: string
  fqjgTywysbm: string
  fqjgTywysmc: string
  jhCjry: string
  jhCjsj: string
  jhGxry: string
  jhGxsj: string
  wjSpwz: string
  wjTpwz: string
  ylKssj: string
  yldz: string
  ylfaWjwz: string
  yljhJssj: string
  yljhKssj: string
  yljhTywysbm: string
  yljhWjwz: string
  ylmc: string
  ylmd: string
  ylnr: string
  ylzt: string
  fawj: IFile
  jhwj: IFile
}

export interface IFile {
  bbh: string
  cjrWybs: string
  cjsj: string
  fjCflj: string
  fjDx: string
  fjLxdm: string
  fjMc: string
  fjTywysbm: string
  fjWjhz: string
  gxrWybs: string
  gxsj: string
  scbs: number
  ywLx: string
  ywSj: string
  ywTywysbm: string
}

export interface IGetYlFileRes {
  pics: IFile[]
  videos: IFile[]
}
