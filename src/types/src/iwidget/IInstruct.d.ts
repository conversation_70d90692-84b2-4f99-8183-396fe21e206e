import { IPageRes } from '../common'

export interface IInstructItem {
  instructId: string
  instructType: string
  instructTypeName: string
  sendTime: string
  sendUser: string
  receiveUser: string
  instructContext: string
  feedbackNum: number
  arriveDes: string
  arriveStatus: string
  instructGis: string
  jqTywysbm: string
  isExpand: boolean
  jsrXm: string
  jsrTywysbms: string
  rwwcbz: string
  feedback?: IFeedback[]
  instructCompletList: IInstructCompletList[]
}

interface IInstructCompletList {
  zlrwwcTywysbm: string
  zlrwTywysbm: string
  jqTywysbm: string
  bzxx: string
  qrr: string
  qrrTywysbm: string
  qrjgTywysbm: string
  qrjgMc: string
  cjsj: string
  gxsj: string
  wczt: number
}
// export interface IInstructList extends Omit<IPageRes, 'rows'> {
//   rows: IInstructItem[];
// }
export type IInstructList = IPageRes<IInstructItem>

export interface IFeedback {
  feedbackContext: string
  userName: string
  deptName: string
  roleName: string
  createTime: string
}

export interface IInstructObject {
  type: string
  objectId: string
  objectName: string
  arriveStatus: string
}

export interface IInstructDetailRes {
  instruct: IInstructItem
  instructObjectList: {
    car: IInstructObject[]
    soldier: IInstructObject[]
  }
  fireInstructFeedBackVo: IFeedback[]
}
