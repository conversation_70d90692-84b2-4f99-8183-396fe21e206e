import { IObj, emptyable } from '../../index'
import { Dialog<PERSON><PERSON>, <PERSON>rete<PERSON>pi, Message<PERSON>pi, MessageOptions } from 'naive-ui'
import { Subject } from 'rxjs'
import { IMenu } from '../ires/IGetMenusByUserIdRes'

interface _IShareCtx {
  useMndCtx: () => DiscreteApi
  useToastCtx: (messageOptions?: MessageOptions & { theme?: 'dark' | 'light' }) => MessageApi
  useDialogCtx: () => DialogApi
  useBridgeCtx: () => IBridgeCtx
  usePermsCtx: () => IPermsCtx
}

interface IBridgeCtx {
  openMis: () => void
  openGis: () => boolean
  closeWin: (winName: string) => void
  $emit: any
  data$: Subject
  send: (type: string, data: any, to: string, from?: string) => void
}

interface IPermsCtx {
  hasPerms: (perms: string, fallback = true) => boolean
  hasPermsByPermsKey: (perms: string) => boolean
  hasPermsByMeta: (meta: IObj<any>, type: IActionType | string, fallback = false) => boolean
  hasPermsByPath: (path: string, type: IActionType | string, fallback = false) => boolean
  menuList: IMenu[]
  getTopMenuByProp: (val: any, key?: string) => emptyable<IMenu>
}

type IActionType =
  | 'export'
  | 'detail'
  | 'add'
  | 'edit'
  | 'delete'
  | 'batchImport'
  | 'auth'
  | 'resetPwd'
  | 'cancel'
  | 'locked'

export type IShareCtx = () => _IShareCtx
