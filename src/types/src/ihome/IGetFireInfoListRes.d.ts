import { IPageRes } from '../common'

export interface IFireInfoItem {
  jqTywysbm: string
  jqbh: string
  bjTywysbm: string
  mc: string
  ddmc: string
  jqlxdm: string
  jqlxmc: string
  jqflydm: string
  jqflymc: string
  jqdjdm: string
  jqdjmc: string
  jqztLbdm: string
  jqztLbmc: string
  jqztRqsj: string
  jqdxJqdxlbdm: string
  jqdxJqdxlbmc: string
  jqdxTywysbm: string
  jqdxMc: string
  jqdxJyqk: string
  jqJyqk: string
  jqbslbdm: string
  jqbslbmc: string
  jzwTywysbm: string
  lc: number
  dqjd: number
  dqwd: number
  bjdh: string
  bjfslbdm: string
  bjfslbmc: string
  bjsj: string
  lasj: string
  sfbjcg: string
  sfcz: string
  sfqwhz: string
  jasj: string
  jsmlsj: string
  cdsj: string
  dcsj: string
  zdzksj: string
  cssj: string
  hskzsj: string
  jbpmsj: string
  tssj: string
  gdsj: string
  ztfhsj: string
  xczhrTywysbm: string
  ddldTywysbm: string
  zhzTywysbm: string
  xfjyjgTywysbm: string
  xzqyBm: string
  xzqyMc: string
  jzwjglxdm: string
  jzwjglxmc: string
  ywqkdm: string
  ywqkmc: string
  lcgdmc: string
  lcgddm: string
  sfsj: string
  rsmjdm: string
  rsmjmc: string
  bkrymc: string
  bkrydm: string
  zqcs: string
  jqczqkbm: string
  jqczqkmc: string
  zyjqjbbm: string
  zyjqjbmc: string
  rsMj: string
  rsWz: string
  bkRs: string
  qzssRs: string
  qzswRs: string
  qzslRs: string
  jjss: string
  zhyXm: string
  zhyDh: string
  cjsj: string
  cjrWybs: string
  gxsj: string
  gxrWybs: string
  bbh: string
  sbzt: string
}

export type IGetFireInfoListRes = IPageRes<IFireInfoItem>
