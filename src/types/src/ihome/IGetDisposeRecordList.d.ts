export interface IGetDisposeRecordList {
  /**
   * 设备id
   */
  deviceId: string
  /**
   * 设备编码
   */
  deviceNum: string
  /**
   * 处置备注
   */
  disposeDesc: string
  /**
   * 预警id
   */
  disposeId: string
  /**
   * 预警原因
   */
  disposeResult: string
  /**
   * 预警时间
   */
  disposeTime: string
  /**
   * 预警等级
   */
  warningRank: string
  /**
   * 预警值
   */
  warnValue: string
  deviceLocation: string
  [property: string]: any
}
export interface IWarnRecordVo {
  /**
   * 报警类型
   */
  alarmTypeName?: null | string
  /**
   * 主键
   */
  id: string
  /**
   * 最新数据时间
   */
  lastTime?: null | string
  /**
   * 监测项
   */
  monitorItem?: null | string
  /**
   * 预警等级
   */
  warnLevelName?: null | string
  /**
   * 预警值(min)
   */
  warnValue?: null | string
  [property: string]: any
}
export type IGetDisposeRecordListRes = IGetDisposeRecordList[]
export type IWarnRecordVoRes = IWarnRecordVo[]
