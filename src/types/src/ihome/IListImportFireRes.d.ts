import { IPageRes } from '../common'

export interface IImportFireItem {
  bbh: string
  bjTywysbm: string
  bjdh: string
  bjfslbdm: string
  bjfslbmc: string
  bjsj: string
  bkRs: number
  bkrydm: string
  bkrymc: string
  cdsj: string
  cjrWybs: string
  cjsj: string
  cssj: string
  dcsj: string
  ddldTywysbm: string
  ddmc: string
  dqjd: number
  dqwd: number
  gdsj: string
  gxrWybs: string
  gxsj: string
  hskzsj: string
  jasj: string
  jbpmsj: string
  jjss: number
  jqJyqk: string
  jqTywysbm: string
  jqbh: string
  jqbslbdm: string
  jqbslbmc: string
  jqczqkbm: string
  jqczqkmc: string
  jqdjdm: string
  jqdjmc: string
  jqdxJqdxlbdm: string
  jqdxJqdxlbmc: string
  jqdxJyqk: string
  jqdxMc: string
  jqdxTywysbm: string
  jqflydm: string
  jqflymc: string
  jqlxdm: string
  jqlxmc: string
  jqztLbdm: string
  jqztLbmc: string
  jqztRqsj: string
  jsmlsj: string
  jzwTywysbm: string
  jzwjglxdm: string
  jzwjglxmc: string
  lasj: string
  lc: number
  lcgddm: string
  lcgdmc: string
  mc: string
  qzslRs: string
  qzssRs: number
  qzswRs: number
  rsMj: number
  rsWz: string
  rsmjdm: string
  rsmjmc: string
  sbzt: string
  sfbjcg: string
  sfcz: string
  sfqwhz: string
  sfsj: string
  tssj: string
  xczhrTywysbm: string
  xfjyjgTywysbm: string
  xzqyBm: string
  xzqyMc: string
  ywqkdm: string
  ywqkmc: string
  zdzksj: string
  zhyDh: string
  zhyXm: string
  zhzTywysbm: string
  zqcs: string
  ztfhsj: string
  zyjqjbbm: string
  zyjqjbmc: string
  bjsjArr?: string[]
}

export type IListImportFireRes = IPageRes<IImportFireItem>
