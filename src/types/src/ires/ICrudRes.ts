export interface ICrudItem {
  id: string
  isPrimary: number
  resourceIdentify: string
  fieldName: string
  fieldIdentify: string
  fieldValueKey?: string // 树或字典关联的值字段名
  validate: string
  isNull: number
  fieldLength: number
  placeholder: string
  toolType: string
  toolUrl: string
  isForm: number
  isList: number
  isSearch: number
  sort: number
  _groupLabel?: string // 合并isSearch后的组名
  _value?: any // 表单默认值
}

export type ICrudRes = ICrudItem[]
