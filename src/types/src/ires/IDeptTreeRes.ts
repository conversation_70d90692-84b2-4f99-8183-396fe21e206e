export interface IDeptTree {
  address: string
  childDeptList?: IDeptTree[]
  createBy: string
  createTime: string
  delFlag: string | number
  deptCode: string
  deptId: string
  deptName: string
  email: string
  latitude: string | number
  leader: string | number
  longitude: string | number
  orderNum: string
  parentId: string | number
  phone: string
  status: string | number
  type: string | number
  updateBy: string
  updateTime: string
  users: string
  dictLabel: string
  zhId: string
}

export type IDeptTreeRes = IDeptTree[]
