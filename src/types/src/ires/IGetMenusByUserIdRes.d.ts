export interface IMenu {
  belongSystem: string
  childMenu: any[]
  createBy: string
  createTime: string
  icon: string
  isBind: true
  isRefresh: string
  menuId: string
  menuName: string
  menuType: string
  orderNum: string
  parentId: string
  parentName: string
  perms: string
  remark: string
  target: string
  updateBy: string
  updateTime: string
  url: string
  visible: string
}

export type IGetMenusByUserIdRes = IMenu[]
