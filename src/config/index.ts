const isPro = import.meta.env.PROD
const base_prefix = import.meta.env.VITE_BASE_PREFIX
const base_url = import.meta.env.VITE_BASE_URL
const ststic_image_prefix = import.meta.env.VITE_STATIC_IMAGE_PREFIX
// const serviceName = '/aqsc/v1' // 公司环境
const isWYCH_LOCAL = import.meta.env.VITE_WYCH_LOCAL
const base_host = import.meta.env.VITE_BASE_HOST || window.location.origin

const config = {
  isOpenFrosua: false, // 是否开启消息埋点  仅招商云环境需要放开
  isFrosuaPro: isPro ? 'prod' : 'dev',
  isProduction: window.location.host !== 'agjp.tanzervas.com',
  socket_url: 'https://agjp.tanzervas.com/aqsc/ws',
  downloadFileUrl: `${base_prefix}/`, // 下载
  USER_IFNO_NAMESPACE: '@@supervise-web_userInfo',
  base_prefix: base_prefix,
  update_file: `${base_url}/edu-file-server`,
  base_url,
  ststic_image_prefix,
  image_url: '/',
  root_dir: 'train',
  appServer: 'ahzd-beta/file/', // app和web公用服务,用于图片查看
  jumpUrlOrgManagement: `${base_prefix}/atomic-upms/#/`,
  locationAreaUrl: `${base_prefix}/locationArea/`,
  base_host,
  risk_base_server: base_url + '/ehs-clnt-rmc-service',
  //单位一张图gis地址
  unit_base_url: base_url + '/ehs-clnt-internetMonitor-service',
  gis_serve_url: base_prefix + '/api/v3/bw-svc-enterprise-gis-service',
  gisDeviceIcon: base_prefix + '/img1/deviceIcons/_v3.0',
  gisUnitModel: base_prefix + '/img1/indoor',
  gisSkyUrl: base_prefix + '/img1/deviceIcons/z/sky/box/5',
  gisGifUrl: base_prefix + '/img1/deviceIcons/gif/alarm.gif',
  gis_image_url: base_prefix + '/img1/floorImage/',
  gsUrlHeader: base_prefix,
}
export default config
