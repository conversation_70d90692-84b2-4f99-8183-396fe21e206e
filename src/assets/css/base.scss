.block-title {
    color: #527CFF;
    position: relative;
    padding-left: 10px;
    font-size: 20px;
    font-family: 'Alibaba-PuHuiTi-Medium';
    &::before {
        content: '';
        width: 4PX;
        height: 16PX;
        background: #527CFF;
        display: inline-block;
        position: absolute;
        top: 50%;
        left: 0;
        border-radius: 2PX;
        transform: translateY(-50%);
    }
}
.block-title.round{
  padding-left: 0;
    &::before {
        width: 8PX;
        height: 8PX;
        border-radius: 50%;
        position: inherit;
        margin-right: 5px;
    }
}

.gs-collapse {
  border-top: none !important;
  border-bottom: none !important;
  .el-collapse-item__header {
    font-size: 16PX;
    font-weight: 900;
    .el-collapse-item__arrow {
      transform: rotate(90deg);
      &.is-active {
        transform: rotate(-90deg);
      }
    }
  }
  .el-collapse-item__header.is-active {
    border-bottom-color: #ebeef5;
  }
  .el-collapse-item__wrap {
    border-bottom: none;
  }
  .el-collapse-item__content {
    padding: 10px 0;
  }
}

.table-operate {
  .operate-item {
    font-size: 14px;
    color: #527CFF;
    cursor: pointer;
  }
  .operate-item + .operate-item::before {
    content: '|';
    display: inline-block;
    margin: 0 8px;
    color: #ccc;
  }
  .operate-disable-single{
    font-size: 16px;
    pointer-events: none;
    color:#ccc;
  }
  .operate-disable{
    font-size: 16px;
    pointer-events: none;
    color:#ccc;
  }
  .operate-disable::before {
    content: '|';
    display: inline-block;
    margin: 0 8px;
    color: #ccc;
  }
  .operate-disable + .operate-item::before {
    content: '|';
    display: inline-block;
    margin: 0 8px;
    color: #ccc;
  }

  .operate-disable1{
    font-size: 16px;
    pointer-events: none;
    color:#ccc;
  }
  .operate-disable1::before {
    // content: '|';
    display: inline-block;
    margin: 0 8px;
    color: #ccc;
  }
  .operate-disable1 + .operate-item::before {
    content: '|';
    display: inline-block;
    margin: 0 8px;
    color: #ccc;
  }
}

.confirm-btn {
  .el-button {
    width: 80px;
  }
}

* {
    scrollbar-color: var(--el-scrollbar-bg-color) var(--el-bg-color);
  }
  
  ::-webkit-scrollbar {
    width: 6px;
  }
  ::-webkit-scrollbar:horizontal {
    height: 6px;
  }
  ::-webkit-scrollbar-track {
    border-radius: 10px;
  }
  
  ::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color);
    border-radius: 10px;
    transition: all 0.2s ease-in-out;
  
    &:hover {
      cursor: pointer;
      background-color: var(--el-border-color);
    }
  }

  .message_tabs .el-tabs__nav-wrap::after {
    height: 0px !important;
  }

  .el-tabs .el-tabs__item {
    font-size: 18px !important;
  }

  .el-form-item {
    .el-form-item__label {
      font-size: 16px;
    }
  }

  // .el-icon svg {
  //   height: inherit !important;
  //   width: inherit !important;
  // }

  .gs-tabs_card.el-tabs--border-card {
    background: none;
    border: 0;
    box-shadow: none;
    .el-tabs__header {
      background: none;
      border-bottom: none;
      .el-tabs__item {
        transition: none;
        border: 0;
      }
      .is-active {
        border: 0;
        border-radius: 13px 13px 6px 6px;
      }
    }
    .el-tabs__content {
      display: none;
    }
    .el-tabs__header .el-tabs__item {
      color: #333;
    }
    .el-tabs__header .el-tabs__item.is-active {
      color: #527CFF !important;
    }
  }
  .monitorButton {
    // position: relative;
    margin-top: 20px;
    top: 200px;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .el-button.el-button--primary {
    &:hover,
    &:focus {
      background: #527CFF;
    }
  }
  .el-button.is-disabled, 
  .el-button.is-disabled:focus, 
  .el-button.is-disabled:hover {
    color: var(--el-button-disabled-text-color);
    cursor: not-allowed;
    background-image: none;
    background-color: var(--el-button-disabled-bg-color);
    border-color: var(--el-button-disabled-border-color);
  }

  .demo-tabs > .el-tabs__header{
    margin-bottom: 0;
    padding-left: 20px;
    padding-top: 25px;
    background: #fff;
  }

  