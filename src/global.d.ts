interface Window {
  lmsDisplay: any
  GetLMSDataOptionsByAdminCode: any
  IndoorThree: any
  IndoorMap: any
  newIndoorService: any
  CONST_Function_DeviceStateValueConvertFun_Default_3: any
  CONST_OVBuildStyleInfo_Default: any
  DicCache: any
  GISShare: any
  $_getUrlParams: any
  gisMap3DM: any
  CONST_StyleInfo_Default_Deep: any
  CONST_OVVideoStyleInfo_Default: any
}

declare const THREE: any
declare const CameraControls: any
declare const IndoorThree: any
declare const CONST_GeoData_China: any
declare const CONST_GeoData_China_line: any
declare const CONST_GeoData_Item: any
declare const CONST_GeoData_Item_inline: any
declare const CONST_GeoData_Item_outline: any
declare const GISShare: any
declare const ol: any
declare const GSMap: any

declare const DicCache: any
declare const IndoorMap: any
declare const newIndoorService: any
declare const CONST_Function_DeviceStateValueConvertFun_Default_3: any
declare const CONST_SRID_BD09MC: any
declare const CONST_SRID_WebMC: any
