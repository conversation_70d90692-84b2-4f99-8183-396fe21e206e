:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(242, 244, 250, 1);
}
.main_search {
  width: 100%;
  height: auto;
  padding: 20px;
  padding-bottom: 0;
  background-color: #ffffff;

  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  margin-bottom: 15px;
}

.el-table__empty-block {
  min-height: 300px;
}

.table-column-dot {
  display: inline-block;
  width: 0.04167rem;
  height: 0.04167rem;
  vertical-align: middle;
  margin: 0 5px 0 10px;
  border-radius: 50%;
  background: transparent;
}

.table-column-dot:first-child {
  margin-left: 0;
}

.table-column-single-dot {
  display: inline-block;
  width: 0.04167rem;
  height: 0.04167rem;
  vertical-align: middle;
  margin-bottom: 0.01042rem;
  border-radius: 50%;
  background: transparent;
}

.el-table__row--level-1 {
  td:first-child {
    position: relative;
    // overflow: inherit;

    &::before {
      position: absolute;
      top: 50%;
      transform: translateY(-60%);
      left: 5%;
      z-index: 200;
      content: '';
      display: inline-block;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 2px solid #527cff;
      background-color: #fff;
    }

    &::after {
      position: absolute;
      top: -40%;
      left: 8.5%;
      z-index: 199;
      content: '';
      display: inline-block;
      width: 1px;
      height: 100%;
      border: none;

      border-left: 1px dashed #9c9c9c;
    }
  }
}

.el-table__row:first-child {
  td {
    &::after {
      display: none;
    }
  }
}

.table-list {
  height: calc(100% - 50px);
  .el-table--border::before {
    width: 0px !important;
  }

  .el-table--border::after {
    width: 0px !important;
  }

  .el-table__border-left-patch {
    width: 0px;
  }

  // .table-list .el-table__inner-wrapper .el-table__header-wrapper .el-table__header .el-table__cell

  .el-table__header-wrapper {
    .el-table__header {
      .el-table__cell {
        border-left: none !important;
        // border-left: none!important;
        border-right: none !important;

        .cell:hover {
          border-right: 1px solid #e5e7eb;
        }
      }

      // .el-table__cell:last-child{
      //  border-right: none!important;
      // }
    }
  }

  header {
    background: white;
    padding: 20px 20px 0 20px;
    box-sizing: border-box;
    position: relative;

    .right-content {
      position: absolute;
      right: 20px;
      bottom: 20px;
      margin-top: 20px;
    }

    .el-row {
      margin-bottom: 0;
    }
  }
  //  .table-list_wrap
  .el-table__inner-wrapper {
    display: flex;
    flex-direction: column;
    padding: 0;

    .el-table__header-wrapper {
      .el-table__header {
        .el-table__cell {
          background: #fafafa;
          padding: 5px 0;

          .cell {
            color: #333333;
          }
        }
      }
    }

    .el-table__body-wrapper {
      flex: 1;
    }

    .el-table__row {
      .el-table__cell {
        // border: none!important;
        border-left: none !important;
        border-right: none !important;
      }
    }
  }

  .el-table__body {
    .el-table__row {
      &:hover {
        // background: #e6f7ff !important;
        td {
          background: #e6f7ff !important;
        }
      }

      .el-table__cell {
        color: #333333;
        // border-bottom: none;
        border-bottom: 1px solid #e5e7eb;
      }
    }

    .el-table__row--striped {
      .el-table__cell {
        background: #f8faff !important;
      }
    }
  }

  .table-list_wrap {
    background: #fff;
    // padding: 20px 20px 55px 20px;
    box-sizing: border-box;

    .el-table {
      height: calc(100% - 50px);

      .el-table__inner-wrapper,
      .el-table__body-wrapper {
        height: 100%;
      }
    }
  }

  .hasTop {
    // padding-top: 0;
  }

  .pagination-wrap {
    // position: absolute;
    // right: 10px;
    // top: 800px;
    .el-pagination {
      button {
        margin: 0 5px !important;
      }

      .btn-prev {
        border: 1px #d9d9d9 solid;
        border-radius: 4px 4px 4px 4px;
      }

      .btn-next {
        border: 1px #d9d9d9 solid;
        border-radius: 4px 4px 4px 4px;
      }

      .el-pager li {
        border: 1px #d9d9d9 solid;
        border-radius: 4px 4px 4px 4px;
        margin: 0 3px;
      }

      .el-pager li.is-active {
        border: 1px #335cff solid;
        border-radius: 4px 4px 4px 4px;
      }

      .el-pagination__sizes {
        margin-left: 5px;
      }

      .el-input {
        .el-input__inner {
          padding-left: 10px !important;
          text-align: left;
        }
      }

      .el-select {
        height: 32px;

        .el-input__inner {
          height: 32px;
        }

        .el-input {
          width: 110px;
        }
      }
    }
  }

  .pagination-wrap-new {
    .el-pagination {
      position: absolute;
      right: -20px;
      bottom: 0;

      .el-pagination__total {
        margin-right: 10px;
      }

      button {
        // margin: 0 1px !important;
      }

      .btn-prev {
        border: 1px #d9d9d9 solid;
        border-radius: 4px 4px 4px 4px;
      }

      .btn-next {
        border: 1px #d9d9d9 solid;
        border-radius: 4px 4px 4px 4px;
      }

      .el-pager li {
        border: 1px #d9d9d9 solid;
        border-radius: 4px 4px 4px 4px;
        margin: 0 2px;
      }

      .el-pager li.is-active {
        border: 1px #335cff solid;
        border-radius: 4px 4px 4px 4px;
      }

      .el-pagination__sizes {
        margin-left: 2px;
      }

      .el-select {
        height: 32px;

        .el-input__inner {
          height: 32px;
        }

        .el-input {
          width: 110px;
        }
      }
    }
  }
}

.el-table .el-table__cell {
  padding: 5px 0;
}

.table-column-single-dot {
  display: inline-block;
  width: 0.04167rem;
  height: 0.04167rem;
  vertical-align: middle;
  margin-bottom: 0.01042rem;
  border-radius: 50%;
  background: transparent;
}
.el-table .el-scrollbar__view {
  height: 100%;
}
.el-table__body tr,
.el-table__body td {
  padding: 0;
  height: 54px;
  line-height: 54px;
}
.text {
  img {
    cursor: pointer;
    display: inline-block !important;
    vertical-align: top;
    max-width: 100%;
  }
}
