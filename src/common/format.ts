import dayjs from 'dayjs'

const $frm = {
  formatDate(date = '', type = 'YYYY-MM-DD', early = 0) {
    if (!date) return ''

    const _today = dayjs(date).valueOf() - 60 * 1000 * 60 * 24 * early

    const d = dayjs(_today).format(type)
    return d
  },
  // 前一个月的日期范围
  initMonthDate() {
    const old = dayjs().subtract(1, 'month').format('YYYY-MM-DD')
    const today = dayjs().format('YYYY-MM-DD')
    return [old, today]
  },
  getDay(num = 0, early = 0) {
    const _today = new Date().getTime() - 60 * 1000 * 60 * 24 * early
    const old = dayjs(_today).subtract(num, 'day').format('YYYY-MM-DD')
    return old
  },
  getDays(num = 0, early = 0) {
    const _today = new Date().getTime() - 60 * 1000 * 60 * 24 * early
    const old = dayjs(_today).subtract(num, 'day').format('YYYY-MM-DD')
    const today = dayjs(_today).format('YYYY-MM-DD')
    return [old, today]
  },
  getDayTime() {
    const dayjsTime = dayjs(`${new Date()}`).format('YYYY-MM-DD HH:mm:ss')
    return dayjsTime
  },
  fomartTime(time = '', type = 'YYYY-MM-DD HH:mm:ss') {
    if (!time) return ''
    const dayjsTime = dayjs(`${time}`).format(type)
    return dayjsTime
  },
  fomartNum(num: any = 0) {
    if (typeof num == 'string') {
      num = num.replace('%', '')
    }
    if (isNaN(Number(num))) {
      // 非数字
      console.error('foomartNum--' + num + '非数字类型且无法转为数字')
      return num
    }
    const numNew = Number(num)
    if (numNew % 1 === 0) {
      return numNew
    } else {
      const str = parseInt((numNew * 100).toString()) / 100
      return str.toFixed(2)
    }
  },
  yesterdayStamp() {
    return dayjs(dayjs().subtract(1, 'day').format('YYYY-MM-DD')).valueOf()
  },
}

export default $frm
