import { useUserInfo } from '@/store'
import $API from './api'
import { getMessageRole } from './utils'
import { ElMessage } from 'element-plus'
export function getMessageGroupList() {
  const ui = useUserInfo().value
  return $API
    .post({
      method: 'post',
      url: '/message/getMessageGroupList',
      params: {
        unitId: ui.unitId,
        userId: ui._userId,
        messageClass: 1,
      },
    })
    .then((res: any) => {
      let count = 0,
        data = [] as any[]
      const rule = getMessageRole(),
        arr = [] as any[]
      if (res && res.code === 'success') {
        data = res.data
        data.forEach((item) => {
          if (rule.length === 0 || rule.includes(item.messageType.toString())) {
            count += item.unReadCount
            arr.push(item)
          }
        })
      }
      return [count, arr]
    })
}
export async function getVideoUrl(data) {
  if (!data.deviceNum) {
    return ElMessage.warning('视频暂时无法播放')
  }
  let isIframe = false
  const params = {
    deviceId: data.deviceId,
    deviceNum: data.deviceNum,
    manufacturerCode: data.manufacturerCode,
    modelType: 'unit_base_url',
  }
  let _videoUrl: string

  try {
    _videoUrl = await reqVideoUrl(params)
    if (_videoUrl.indexOf('.html') != -1) {
      isIframe = true
    }
    return {
      _videoUrl: _videoUrl,
      isIframe,
    }
  } catch (error) {
    console.log('请求失败--', error)
  }
}
export function reqVideoUrl(params = {}) {
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/device/monitor/getVideoUrl',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data)
        } else {
          reject(data)
        }
      })
  })
}

export function getXkbVideoUrl(params = {}) {
  return new Promise<any>((resolve, reject) => {
    $API
      .post({
        url: '/fireControlSupervise/fireControlRoomDuty/personLeaveRecord/details/alXkbVideoUrl',
        params: params,
      })
      .then((data: any) => {
        if (data && data.code == 'success') {
          resolve(data.data.path)
        } else {
          reject(data)
        }
      })
  })
}
export function getMessageList(params) {
  params.messgaeClass = '1'
  return $API
    .post({
      method: 'post',
      url: '/message/getMessageList',
      params,
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        const d = res.data
        const rows = d.rows
        rows.forEach((item) => Object.keys(item).forEach((key) => (item[key] = item[key].toString())))
        return res.data
      }
    })
}

export function changeMessageReadState(messageIds: string[], type: '1' | '2' = '1') {
  const ui = useUserInfo().value
  const params: any = {
    messageClass: 1,
    type,
    userId: ui._userId,
  }
  if (messageIds.length > 0) {
    params.messageIds = messageIds.join(',')
  }
  return $API
    .post({
      method: 'post',
      url: '/message/changeReadState',
      params: params,
    })
    .then((res: any) => res && res.code === 'success')
}

export function getDeviceInfoById(deviceId: string, ownerType?: string) {
  // const ui = useUserInfo().value;
  return $API
    .post({
      method: 'post',
      url: '/monitor/getDeviceInfoById',
      params: {
        deviceId,
        ownerType,
      },
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        return res.data && res.data[0]
      }
    })
}

export function getEventRecordInfo(options: { disposeId: string; eventType: string; subCenterCode?: string }) {
  const ui = useUserInfo().value
  options.subCenterCode = ui.subCenterCode
  return $API
    .post({
      method: 'post',
      url: '/dispose/getEventRecordInfo',
      params: options,
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        return res.data
      }
      return ''
    })
}

export function queryDeviceEventLisBySystemType(params: any) {
  return $API
    .post({
      method: 'post',
      url: '/monitor/queryDeviceEventLisBySystemType',
      params,
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        return res.data
      }
      return ''
    })
}
export function getHkVisonUrlByDeviceId(params: any) {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/keyPart/getHkVisonUrlByDeviceId',
        params,
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}
