import dayjs from 'dayjs'
import isoWeek from 'dayjs/plugin/isoWeek'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'

dayjs.extend(isoWeek)
dayjs.extend(quarterOfYear)

// 获取今日时间范围
export function getTodayRange() {
  const start = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
  const end = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
  return [start, end]
}

// 获取本周时间范围
export function getWeekRange() {
  const start = dayjs().startOf('isoWeek').format('YYYY-MM-DD HH:mm:ss')
  const end = dayjs().endOf('isoWeek').format('YYYY-MM-DD HH:mm:ss')
  return [start, end]
}

// 获取本月时间范围
export function getMonthRange() {
  const start = dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss')
  const end = dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss')
  return [start, end]
}

// 获取本季度时间范围
export function getQuarterRange() {
  const start = dayjs().startOf('quarter').format('YYYY-MM-DD HH:mm:ss')
  const end = dayjs().endOf('quarter').format('YYYY-MM-DD HH:mm:ss')
  return [start, end]
}
