/* eslint-disable @typescript-eslint/ban-types */
import { SocketOptions, Socket } from '@/types/index'
import config from '@/config'
import { useUserInfo } from '@/store'

const SockJS = window.SockJS,
  Stomp = window.Stomp

class SocketUtil {
  socket: any
  stomp: any
  isClose: any
  CONNECT_COUNT: any
  connectCount: any
  timer: any
  onmessage?: any
  setConnected?: any
  optionsObj: any

  constructor(options: SocketOptions) {
    this.CONNECT_COUNT = 100
    this.connectCount = 1
    this.isClose = false
    this.socket = new SockJS(config.socket_url)
    this.stomp = Stomp.over(this.socket)
    this.stomp.heartbeat.outgoing = 5000 // 客户端发给服务端的心跳
    this.stomp.heartbeat.incoming = 0 // 客户端希望服务端发送的心跳
    this.optionsObj = options
    this.connects()

    this.socket.onclose = () => {
      console.log('socket断开连接' + new Date())
      if (this.isClose) {
        this.connectCount = 0
        return
      }
      // 小于重连次数
      if (this.connectCount < this.CONNECT_COUNT) {
        this.reconnect()
      } else {
        this.connectCount = 0
        this.isClose = true
      }
    }
  }

  reconnect() {
    // 判断用户信息是否存在 存在重连 退出-不重连
    const ui = useUserInfo().value

    if (!this.timer && ui.userId) {
      this.timer = setInterval(() => {
        console.log('socket断线重连' + new Date())
        this.connects()
        this.connectCount = this.connectCount + 1
      }, 3 * 1000)
    }
  }

  connects() {
    this.socket = null
    this.socket = new SockJS(config.socket_url)
    this.stomp = Stomp.over(this.socket)
    this.stomp.connect(
      this.optionsObj.headers,
      (frame) => {
        console.log('socket连接' + new Date())
        if (this.timer) {
          clearInterval(this.timer)
          this.timer = null
        }
        this.stomp.subscribe(
          this.optionsObj.topic,
          (response) => {
            const body = JSON.parse(response.body)

            Object.keys(body).forEach((key) => (body[key] = body[key].toString()))
            console.log('body', body)
            this.onmessage && this.onmessage(body)
          },
          (error) => {
            // 连接失败时（服务器响应 ERROR 帧）的回调方法
            console.log('stompWebSocket connect failed' + error)
            setTimeout(() => {
              this.connects()
            }, 120000)
          }
        )
      },
      (error) => {
        console.log(error)
        this.stomp.disconnect()
        setTimeout(() => {
          this.connects()
        }, 5 * 1000)
      }
    )
  }

  distory() {
    this.stomp.unsubscribe()
  }
}

export default SocketUtil
