import { ref } from 'vue'
import { Message, UserInfo } from '@/types'
import { EVENT_TYPE, ROLE_GROUP, MESSAGE_RULE, SERVICE_MODE } from './eventType'
import { useUserInfo } from '@/store'
export function isTibetUnit(unitId: string) {
  return unitId === '540102DZDA202206010001'
}
export const loading = ref(false)

/**
 * 延迟函数
 * @param t 毫秒
 */
export function sleep(t: number) {
  return new Promise((resolve) => setTimeout(resolve, t))
}

export function normalizeAddress(item: any) {
  let address =
    (item.buildingName || item.buildName || '') +
    '' +
    (item.floorName || item.floorName || '') +
    ' ' +
    (item.deviceAddress || item.faultAddress || '')

  if (item.unitType != 0 && item.unitType) {
    address = (item.houseNumber || '') + (item.deviceAddress || '')
  }

  return address.trim() === '' ? '未采集' : address
}

export function getMessageRole(): string[] {
  const ui = useUserInfo().value
  if (isSafetyManage(ui)) {
    return MESSAGE_RULE[ROLE_GROUP.SAFETY_MANAGE]
  } else if (isWoker(ui)) {
    return MESSAGE_RULE[ROLE_GROUP.WORKER]
  } else if (isManageAdmin(ui)) {
    return MESSAGE_RULE[ROLE_GROUP.MANAGE_ADMIN]
  }
  return []
}

export function isWoker(userInfo: UserInfo) {
  const rids = userInfo.roleIds
  return rids && rids.includes(ROLE_GROUP.WORKER)
}

export function isSafetyManage(userInfo: UserInfo) {
  const rids = userInfo.roleIds
  return rids && rids.includes(ROLE_GROUP.SAFETY_MANAGE)
}

export function isAdmin(userInfo: UserInfo) {
  const rids = userInfo.roleIds
  return rids && rids.includes(ROLE_GROUP.ADMIN)
}

export function isManageAdmin(userInfo: UserInfo) {
  const rids = userInfo.roleIds
  return rids && rids.includes(ROLE_GROUP.MANAGE_ADMIN)
}

export function addPointColorNew(item: any) {
  let bgColor = ''
  if (item == '1') {
    bgColor = '#F30C0C'
  } else if (item == '3') {
    bgColor = '#FD9905'
  } else if (item == '2') {
    bgColor = '#9F1D8B'
  } else if (item == '4') {
    bgColor = '#F9E400'
  } else if (item == '5') {
    bgColor = '#2476EE'
  } else if (item == '7') {
    bgColor = '#999999'
  } else if (item == '0' || '') {
    bgColor = '#2476EE'
  } else {
    bgColor = ''
  }
  return bgColor
}

export const isFontSizeRem = (size) => {
  const clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth
  const clientHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
  if (!clientHeight) return
  const fontSize = clientHeight / 1080
  console.log(clientWidth, size * fontSize)

  return size * fontSize
}
