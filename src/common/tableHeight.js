/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-15 18:43:18
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-15 18:43:23
 * @FilePath: /angang-platform/src/common/tableHeight.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// tableHeight.js
import { ref, onBeforeUnmount, nextTick } from 'vue'
import _ from 'lodash'

export default function (opts) {
  const { subtractHeight = 500 } = opts

  const tableHeight = ref(500)

  // 计算高度
  const getHeight = _.debounce(function () {
    const height = document.body.clientHeight - subtractHeight
    tableHeight.value = height > 200 ? height : 200
  }, 500)

  // 监听窗口变化，触发高度计算
  window.addEventListener('resize', getHeight)
  onBeforeUnmount(() => {
    window.removeEventListener('resize', getHeight)
  })

  // 初始化高度
  nextTick(() => {
    getHeight()
  })
  return {
    tableHeight,
  }
}
