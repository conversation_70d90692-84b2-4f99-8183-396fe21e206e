/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-03 18:23:54
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-17 17:29:37
 * @FilePath: /angang-platform/vite.config.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import WindiCSS from 'vite-plugin-windicss'
import { resolve as r } from 'path'
import { manifestPlugin } from './plugins/vite/vite-plugin-manifest'
// import config from './src/config/index'
const resvole = (_path) => r(__dirname, _path)
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), ['NODE', 'VITE'])
  return {
    base: `${env.VITE_BASE_PREFIX}/${env.VITE_BASE_OUT_DIR}`,
    resolve: {
      alias: {
        '~': resvole('src'),
        '@': resvole('src'),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: "@import '@/assets/css/variables.scss';",
        },
      },
      postcss: './postcss.config.cjs',
    },
    server: {
      host: true,
      proxy: {
        [`${env.VITE_BASE_PREFIX}/api`]: {
          target: env.VITE_BASE_HOST,
          changeOrigin: true,
        },
        [`${env.VITE_BASE_PREFIX}/img1`]: {
          target: env.VITE_BASE_HOST,
          changeOrigin: true,
        },
        [`${env.VITE_BASE_PREFIX}/aqsc/img1`]: {
          target: env.VITE_BASE_HOST,
          changeOrigin: true,
        },
        '/api/v3/bw-svc-indoor-gis-service': {
          target: env.VITE_BASE_HOST,
          changeOrigin: true,
        },
        '/ehs_file': {
          target: env.VITE_BASE_HOST,
          changeOrigin: true,
        },
      },
    },
    build: {
      outDir: 'platform-wych',
      manifest: 'false',
    },
    plugins: [
      vue(),
      createSvgIconsPlugin({
        iconDirs: [resvole('src/assets/svg')],
        symbolId: 'icon-[name]',
        customDomId: '__svg__icons__dom__',
      }),
      AutoImport({
        resolvers: [ElementPlusResolver()],
      }),
      Components({
        resolvers: [ElementPlusResolver()],
      }),
      WindiCSS({
        scan: {
          include: ['./App.vue', './src/**/*.vue'],
        },
      }),
      manifestPlugin({
        output: 'platform-wych',
        preload: ['js/sockjs.min.js', 'js/stomp.min.js', 'js/ajaxhook.js'],
        exclude: ['*login_bg*'],
        enableLog: false,
      }),
    ],
  }
})
